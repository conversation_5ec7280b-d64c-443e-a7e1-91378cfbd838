{
  "compilerOptions": {
    "noImplicitAny": false,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "types": ["vite/client", "@libs/http/axios"],
    "lib": ["ESNext", "DOM"],
    "skipLibCheck": true,
    "baseUrl": ".",
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
  ],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
