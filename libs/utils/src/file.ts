function toBase64(file: Blob) {
  return new Promise<FileReader['result']>((resolve) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
  });
}

function download(fileName: any, url: string): void {
  const link = document.createElement('a');

  link.href = url;
  link.download = fileName;
  link.click();
}

function downloadForIphone(fileName: string, url: string): void {
  fetch(url)
    .then((response) => response.blob())
    .then((blob) => {
      const blobURL = URL.createObjectURL(blob);
      const link = document.createElement('a');

      link.href = blobURL;
      link.download = fileName;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(blobURL);
    })
    .catch((error) => {
      console.error(error);
    });
}
export { download, toBase64, downloadForIphone };
