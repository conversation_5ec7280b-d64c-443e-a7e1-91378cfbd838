#!/usr/bin/env node
import fs from "fs-extra";
import path from "path";
import mustache from "mustache";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { globbySync } from "globby";
import { optimize } from "svgo";
import pascalCase from "pascalcase";
import * as cheerio from "cheerio";
import { trimNewlines } from "trim-newlines";
import { readFile } from "fs/promises";
import { fileURLToPath } from "url";

interface Icon {
  name: string;
  keywords: string[];
  path: string;
}

interface IconsByName {
  [key: string]: Icon;
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

(async () => {
  // Parse CLI options
  const argv = yargs(hideBin(process.argv))
    .option("input", {
      alias: "i",
      type: "array",
      demandOption: true,
      describe: "Glob pattern(s) for input SVG files",
    })
    .option("jsonOutput", {
      alias: "j",
      type: "string",
      describe:
        "Path to output JSON file with icon metadata (if not provided, JSON won’t be generated)",
    })
    .option("componentsOutput", {
      alias: "c",
      type: "string",
      describe:
        "Directory where Vue component files will be generated (if not provided, components won’t be generated)",
    })
    .option("prefix", {
      type: "string",
      default: "Icon",
      describe: "Prefix for the component name",
    })
    .option("postfix", {
      type: "string",
      default: "",
      describe: "Postfix for the component name",
    })
    .parseSync();

  // Load keywords from keywords.json (assumes it's one level up from src)
  const keywordsPath = new URL("../keywords.json", import.meta.url).pathname;

  let keywords: Record<string, string[]> = {};

  try {
    const keywordsContent = await readFile(keywordsPath, "utf8");
    keywords = JSON.parse(keywordsContent);
  } catch (error) {
    console.error("Error reading keywords.json:", error);
  }

  // Find all SVG files based on the input glob(s)
  const filePaths: string[] = globbySync(argv.input as string[]).filter(
    (p) => path.extname(p).toLowerCase() === ".svg",
  );
  if (!filePaths.length) {
    console.error("No SVG files found using the provided input pattern(s).");
    process.exit(1);
  }

  // Process each SVG file into an Icon object
  const icons: Icon[] = filePaths
    .map((filepath) => {
      try {
        const svgContent = fs.readFileSync(path.resolve(filepath), "utf8");
        const result = optimize(svgContent, {
          multipass: true,
          plugins: [
            "removeDoctype",
            "removeXMLProcInst",
            "removeComments",
            "removeMetadata",
            "removeEditorsNSData",
            "cleanupAttrs",
            "mergeStyles",
            "inlineStyles",
            "minifyStyles",
            "convertStyleToAttrs",
            "cleanupIds",
            {
              name: "prefixIds",
              params: { prefix: "darya-icon" },
            },
            "removeRasterImages",
            "removeUselessDefs",
            "cleanupNumericValues",
            "cleanupListOfValues",
            "convertColors",
            "removeUnknownsAndDefaults",
            "removeNonInheritableGroupAttrs",
            {
              name: "removeUselessStrokeAndFill",
              params: { removeNone: true },
            },
            "removeViewBox",
            "cleanupEnableBackground",
            "removeHiddenElems",
            "removeEmptyText",
            "convertShapeToPath",
            "convertEllipseToCircle",
            "moveElemsAttrsToGroup",
            "moveGroupAttrsToElems",
            "collapseGroups",
            "convertPathData",
            "convertTransform",
            "removeEmptyAttrs",
            "removeEmptyContainers",
            "mergePaths",
            "removeUnusedNS",
            "sortAttrs",
            "sortDefsChildren",
            "removeTitle",
            "removeDesc",
            "removeDimensions",
            "removeStyleElement",
            "removeScriptElement",
            "removeOffCanvasPaths",
            "reusePaths",
            {
              name: "removeAttributesBySelector",
              params: {
                selectors: [
                  {
                    selector: ":not(.darya-icon__ignore)",
                    attributes: ["fill", "stroke"],
                  },
                  { selector: "[mask]", attributes: ["d", "mask"] },
                ],
              },
            },
          ],
        });

        // Derive the component name using prefix, filename (in pascal case) and postfix.
        const baseName = path.parse(filepath).name;
        const compName = `${argv.prefix}${pascalCase(baseName)}${argv.postfix}`;

        // Use Cheerio to get inner SVG content (what will be injected into the Vue component)
        const $ = cheerio.load(result.data);
        const innerSVG = trimNewlines($("svg").html() || "").trim();

        return {
          name: compName,
          keywords: keywords[pascalCase(baseName)] || [],
          path: innerSVG,
        };
      } catch (error) {
        console.error(`Error processing "${filepath}":`, error);
        return null;
      }
    })
    .filter((icon): icon is Icon => icon !== null);

  // Write JSON output if requested
  if (argv.jsonOutput) {
    const jsonOutPath = path.resolve(argv.jsonOutput);
    fs.outputJsonSync(
      jsonOutPath,
      icons.reduce((acc, icon) => {
        acc[icon.name] = icon;
        return acc;
      }, {} as IconsByName),
    );
    console.log("Icons metadata written to", jsonOutPath);
  }

  // Generate Vue component files if requested
  if (argv.componentsOutput) {
    const compOutDir = path.resolve(argv.componentsOutput);
    // Load the component template (assumes it is at src/templates/component.mustache)
    const compTemplatePath = path.join(
      __dirname,
      "templates",
      "component.mustache",
    );
    let compTemplate = "";
    try {
      compTemplate = fs.readFileSync(compTemplatePath, "utf8");
    } catch (error) {
      console.error("Error reading component.mustache template:", error);
      process.exit(1);
    }

    // For each icon, render the Vue component and write to a .vue file
    icons.forEach((icon) => {
      const rendered = mustache.render(compTemplate, {
        name: icon.name,
        path: icon.path,
      });
      const outFile = path.join(compOutDir, `${icon.name}.vue`);
      fs.outputFileSync(outFile, rendered);
      console.log("Generated component:", outFile);
    });
  }
})();
