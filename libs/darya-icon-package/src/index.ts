import { App } from "vue";

// Use Vite’s import.meta.glob to eagerly load all icon components from the components folder
const modules = import.meta.glob("./components/*.vue", { eager: true });
const icons: Record<string, any> = {};

for (const filePath in modules) {
  const module = modules[filePath] as any;
  // Assume each module default-exports a Vue component
  const component = module.default;
  // Derive the component name from the filename (e.g. IconExample.vue → IconExample)
  const fileName = filePath.split("/").pop()?.replace(".vue", "");
  if (fileName) {
    icons[fileName] = component;
  }
}

export default {
  install(app: App) {
    for (const key in icons) {
      app.component(key, icons[key]);
    }
  },
};

export { icons };
