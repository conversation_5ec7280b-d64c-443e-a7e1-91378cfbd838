<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M19.802 3.373a1 1 0 0 1 .895.895c.38 3.795-.757 7.057-2.551 9.337-.434.55-.912 1.05-1.422 1.488.631 1.13.775 2.455.244 3.694-.716 1.67-2.518 2.865-5.197 3.247a1 1 0 0 1-1.141-.99c0-1.391.206-2.743.55-4.088l-4.077-4.095-.227.06c-.723.19-1.872.494-3.876.494a1 1 0 0 1-.994-1.11c.3-2.74 1.442-4.612 3.141-5.34 1.288-.552 2.67-.349 3.848.36.434-.502.927-.973 1.47-1.4 2.28-1.795 5.542-2.932 9.337-2.552m-4.737 12.86a7.2 7.2 0 0 1-1.835.682c-.06.423-.157.836-.254 1.25-.126.537-.252 1.075-.299 1.637 1.456-.428 2.176-1.156 2.453-1.803.23-.537.22-1.163-.065-1.767m-7.902-5.424c.139-.623.375-1.24.693-1.835-.685-.377-1.37-.407-1.921-.17-.636.272-1.359 1.013-1.742 2.567.567-.045 1.11-.172 1.652-.299.436-.102.871-.204 1.318-.263m8.001 1.042a2 2 0 1 1-2.828-2.829 2 2 0 0 1 2.828 2.83" clip-rule="evenodd"></path><path d="M9.232 19.275c.151-.498.232-1.08.178-1.569l-3.115-3.01c-.378 0-.91.066-1.407.204-.563.155-1.148.412-1.507.893-.285.358-.433.78-.517 1.159-.335 1.501-.096 3.56.324 3.98s2.52.696 4.014.343c.375-.089.806-.251 1.157-.574.482-.381.74-.988.873-1.426"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaFillRocketIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
