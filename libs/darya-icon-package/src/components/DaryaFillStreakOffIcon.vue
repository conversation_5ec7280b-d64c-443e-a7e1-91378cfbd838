<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M13.902 2.842c.151-1.209-1.385-1.848-2.135-.889L8.704 5.867l8.843 8.842 2.98-3.764c.623-.787.063-1.945-.94-1.945h-6.454zM7.453 7.465l.01-.012 8.834 8.835-.01.012 3.718 3.717a1 1 0 0 1-1.414 1.415l-3.553-3.553-3.316 4.189c-.751.948-2.274.316-2.133-.885l.786-6.683H4.41c-1 0-1.561-1.152-.945-1.94l2.746-3.508-3.622-3.621a1 1 0 1 1 1.414-1.415z"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaFillStreakOffIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
