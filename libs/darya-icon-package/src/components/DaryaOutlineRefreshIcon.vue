<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M14.55 22.42c-.33 0-.64-.22-.73-.56-.11-.4.13-.81.54-.92a9.256 9.256 0 0 0 6.89-8.95c0-5.1-4.15-9.25-9.25-9.25-4.33 0-7.17 2.53-8.5 4.06h2.94a.755.755 0 0 1 0 1.51H2.01c-.05 0-.14-.01-.21-.03a.9.9 0 0 1-.24-.12.66.66 0 0 1-.21-.23.8.8 0 0 1-.1-.31V3c0-.41.34-.75.75-.75s.75.34.75.75v2.39C4.38 3.64 7.45 1.25 12 1.25c5.93 0 10.75 4.82 10.75 10.75 0 4.88-3.29 9.16-8.01 10.4-.06.01-.13.02-.19.02m-3.26.31c-.02 0-.04-.01-.05-.01-1.08-.07-2.14-.31-3.14-.7a.747.747 0 0 1-.43-.97c.15-.38.6-.57.97-.43.87.34 1.78.54 *********.*********.76l-.01.04c-.02.39-.35.7-.74.7m-5.51-2.15c-.17 0-.33-.06-.47-.16-.84-.68-1.58-1.47-2.18-2.35a.73.73 0 0 1 .19-********** 0 0 1 1.04.18v.01c.***********.03.04a9.2 9.2 0 0 0 1.86 1.98c.***********.28.58 0 .17-.05.34-.16.48-.15.18-.36.28-.59.28M2.44 15.7c-.33 0-.62-.21-.71-.52-.32-1.03-.48-2.1-.48-3.18v-.01c.01-.41.34-.74.75-.74s.75.34.75.75c0 .94.14 1.86.41 **********.03.15.03.23a.747.747 0 0 1-.75.74"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineRefreshIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
