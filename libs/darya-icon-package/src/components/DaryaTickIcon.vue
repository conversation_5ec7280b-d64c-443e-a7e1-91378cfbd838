<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M20.53 5.97a.75.75 0 0 1 0 1.06l-11 11a.75.75 0 0 1-1.06 0l-5-5a.75.75 0 1 1 1.06-1.06L9 16.44 19.47 5.97a.75.75 0 0 1 1.06 0" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaTickIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
