<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path d="M0 .333h32v21.334H0z"></path><path d="M0 .333h8.858v21.334H0zm23.142 0H32v21.334h-8.858zm-4.023 12.85 3.144-1.572-1.572-.786V9.253l-3.144 1.572 1.572-3.144h-1.572l-1.572-2.357-1.572 2.357h-1.572l1.572 3.144-3.144-1.572v1.572l-1.572.786 3.144 1.572-.786 1.572h3.144v2.358h1.572v-2.358h3.144z"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 .333h32v21.333H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaCaFlagIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
