<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M19.65 3.862a2.943 2.943 0 0 0 0 4.163l.625.624-8.742 5.828-2.29-2.29a2.943 2.943 0 0 0-4.162 4.163l6.244 6.244L3 30.919V33h2.081l8.325-8.325 6.244 6.244a2.943 2.943 0 1 0 4.163-4.163l-2.29-2.29 5.828-8.74.624.624a2.944 2.944 0 0 0 4.163-4.163l-8.325-8.325a2.943 2.943 0 0 0-4.163 0"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaInteractionFillPinIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
