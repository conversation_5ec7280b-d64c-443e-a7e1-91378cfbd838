<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-opacity=".01" d="M24 0v24H0V0z"></path><path d="M20.195 7.63a4.87 4.87 0 0 0-2.328 4.096 4.74 4.74 0 0 0 2.885 4.347 11.3 11.3 0 0 1-1.477 3.051c-.92 1.324-1.881 2.648-3.344 2.648s-1.84-.85-3.526-.85c-1.644 0-2.229.877-3.566.877s-2.271-1.226-3.344-2.73a13.2 13.2 0 0 1-2.243-7.12c0-4.18 2.717-6.396 5.392-6.396 1.421 0 2.605.934 3.497.934.85 0 2.174-.99 3.79-.99a5.07 5.07 0 0 1 4.264 2.132m-7.942-2.37q-.147 0-.293-.027a2 2 0 0 1-.042-.39 4.67 4.67 0 0 1 1.185-2.913A4.8 4.8 0 0 1 16.266.3q.043.214.042.432a4.8 4.8 0 0 1-1.143 2.996 4.17 4.17 0 0 1-2.912 1.532"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaLogoAppleIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
