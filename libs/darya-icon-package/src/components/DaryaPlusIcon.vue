<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g fill-rule="evenodd" clip-path="url(#darya-icon__a)" clip-rule="evenodd"><path d="M4.179 12a.75.75 0 0 1 .75-.75H19.07a.75.75 0 0 1 0 1.5H4.929a.75.75 0 0 1-.75-.75"></path><path d="M12 4.179a.75.75 0 0 1 .75.75V19.07a.75.75 0 0 1-1.5 0V4.93a.75.75 0 0 1 .75-.75Z"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 0h24v24H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaPlusIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
