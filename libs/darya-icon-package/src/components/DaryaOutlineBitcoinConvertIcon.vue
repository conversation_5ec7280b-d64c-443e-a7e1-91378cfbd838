<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M16 23.72a.745.745 0 0 1-.64-1.13l1.05-1.75c.21-.36.67-.47 1.03-.26s.47.67.26 1.03l-.27.45c2.76-.65 4.83-3.13 4.83-6.09 0-.41.34-.75.75-.75s.75.34.75.75c-.01 4.27-3.49 7.75-7.76 7.75m-15-15c-.41 0-.75-.34-.75-.75C.25 3.7 3.73.22 8 .22a.745.745 0 0 1 .64 1.13L7.59 3.1c-.21.36-.67.47-1.03.26a.75.75 0 0 1-.26-1.03l.27-.45c-2.76.65-4.83 3.13-4.83 6.09a.74.74 0 0 1-.74.75m8.43 7.36H6.62c-.41 0-.75-.34-.75-.75v-2.25c0-.41.34-.75.75-.75h2.81c1 0 1.88.88 1.88 1.88 0 1.02-.85 1.87-1.88 1.87m-2.06-1.5h2.06c.21 0 .38-.17.38-.38 0-.15-.17-.38-.38-.38H7.37z"></path><path d="M9.83 18.33H6.61c-.41 0-.75-.34-.75-.75v-2.25c0-.41.34-.75.75-.75h3.22c1.12 0 2.04.84 2.04 1.88s-.91 1.87-2.04 1.87m-2.46-1.5h2.47c.32 0 .54-.2.54-.38s-.22-.38-.54-.38H7.37z"></path><path d="M8.42 19.45c-.41 0-.75-.34-.75-.75v-1.13c0-.41.34-.75.75-.75s.75.34.75.75v1.13c0 .42-.34.75-.75.75m0-5.63c-.41 0-.75-.34-.75-.75v-1.13c0-.41.34-.75.75-.75s.75.34.75.75v1.13c0 .42-.34.75-.75.75"></path><path d="M8.67 22.25c-3.82 0-6.92-3.11-6.92-6.92s3.11-6.92 6.92-6.92c.18 0 .34.01.52.02 3.39.26 6.13 3 6.38 6.37.01.22.***********.01 3.81-3.1 6.92-6.92 6.92m0-12.35c-2.99 0-5.42 2.43-5.42 5.42s2.43 5.42 5.42 5.42 5.43-2.43 5.43-5.42c0-.13-.01-.26-.02-.39-.2-2.68-2.35-4.82-4.99-5.02-.13 0-.27-.01-.42-.01"></path><path d="M15.33 15.6h-.49c-.39 0-.72-.3-.75-.69A5.44 5.44 0 0 0 9.1 9.92a.75.75 0 0 1-.69-.75v-.49c0-3.82 3.11-6.92 6.93-6.92s6.92 3.11 6.92 6.92-3.12 6.92-6.93 6.92M9.9 8.51c2.82.51 5.08 2.76 5.59 5.59a5.43 5.43 0 0 0 5.26-5.42c0-2.99-2.43-5.42-5.42-5.42A5.42 5.42 0 0 0 9.9 8.51"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineBitcoinConvertIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
