<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M4.615 8.554a.23.23 0 0 0-.23.23v7.351c0 .*************.23h11.077a.23.23 0 0 0 .231-.23V8.784a.23.23 0 0 0-.23-.23H4.614ZM3 8.784c0-.888.724-1.608 1.615-1.608h11.077c.892 0 1.616.72 1.616 1.608v7.351c0 .888-.724 1.608-1.616 1.608H4.615c-.892 0-1.615-.72-1.615-1.608zm3.231 11.027c0-.38.31-.69.692-.69h6.462a.69.69 0 0 1 .692.69c0 .38-.31.69-.692.69H6.923a.69.69 0 0 1-.692-.69" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M11.77 5.108c0-.888.723-1.608 1.615-1.608h6c.892 0 1.615.72 1.615 1.608v13.784c0 .888-.723 1.608-1.615 1.608h-3a.69.69 0 0 1-.693-.69c0-.38.31-.688.693-.688h3a.23.23 0 0 0 .23-.23V5.108a.23.23 0 0 0-.23-.23h-6a.23.23 0 0 0-.231.23v2.01c0 .38-.31.69-.692.69a.69.69 0 0 1-.693-.69v-2.01Zm-1.616 11.257a.69.69 0 0 1 .692.69v2.756c0 .38-.31.69-.692.69a.69.69 0 0 1-.693-.69v-2.757c0-.38.31-.689.693-.689" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaComputerIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
