<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M13.592 10.41a2.25 2.25 0 0 0-3.184 0 2.254 2.254 0 0 0 0 3.184c.88.879 2.306.878 3.184 0a2.254 2.254 0 0 0 0-3.184m1.06-1.061a3.75 3.75 0 0 0-5.305 0 3.754 3.754 0 0 0 0 5.306 3.754 3.754 0 0 0 5.306 0 3.754 3.754 0 0 0 0-5.305" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M12 5.75c-3.43 0-6.502 1.85-7.88 4.668A3.6 3.6 0 0 0 3.75 12c0 .543.125 1.082.37 1.582C5.498 16.4 8.57 18.25 12 18.25s6.502-1.85 7.88-4.668c.245-.5.37-1.039.37-1.582a3.6 3.6 0 0 0-.37-1.582C18.502 7.6 15.43 5.75 12 5.75M2.772 9.758C4.424 6.382 8.048 4.25 12 4.25s7.576 2.132 9.228 5.508c.343.703.522 1.467.522 2.242s-.179 1.54-.522 2.242C19.576 17.618 15.952 19.75 12 19.75s-7.576-2.132-9.228-5.508A5.1 5.1 0 0 1 2.25 12c0-.775.179-1.54.522-2.242" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaEyeIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
