<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M34.594 6.99a2.55 2.55 0 0 1-.084 3.605L15.26 29.094a2.55 2.55 0 0 1-3.647-.13L2.208 18.812a2.55 2.55 0 1 1 3.774-3.43l7.647 8.22 17.36-16.695a2.55 2.55 0 0 1 3.604.083" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaFillCheckmarkIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
