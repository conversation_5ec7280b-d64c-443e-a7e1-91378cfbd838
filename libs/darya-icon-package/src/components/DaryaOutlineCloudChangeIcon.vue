<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M14.81 17.1h-6.7c-1.98-.14-2.86-1.65-2.86-3 0-1.13.62-2.37 1.95-2.83-.3-1.5.07-2.9 1.06-3.94 1.19-1.25 3.1-1.74 4.74-1.24 1.46.45 2.51 1.62 2.92 3.25 1.25.35 2.24 1.33 2.65 2.66.46 1.52.04 3.08-1.1 4.07-.72.66-1.67 1.03-2.66 1.03m-6.67-4.5c-1.02.09-1.39.86-1.39 1.5 0 .65.37 1.43 1.41 1.5h6.62c.65 0 1.22-.22 1.68-.65.84-.73.89-1.78.67-2.52s-.85-1.58-1.94-1.71a.75.75 0 0 1-.65-.62c-.22-1.33-.93-2.24-1.99-2.57-1.09-.34-2.42.01-3.21.84-.78.81-.95 1.96-.5 3.23a.75.75 0 0 1-.45.96c-.08.03-.21.05-.25.04M9 22.75c-4.27 0-7.75-3.48-7.75-7.75 0-.41.34-.75.75-.75s.75.34.75.75c0 2.96 2.06 5.44 4.83 6.09l-.27-.45a.751.751 0 1 1 1.29-.77l1.05 1.75c.***********.01.75-.14.23-.39.38-.66.38m13-13c-.41 0-.75-.34-.75-.75 0-2.96-2.06-5.44-4.83-6.09l.27.45a.75.75 0 1 1-1.29.77l-1.05-1.75a.75.75 0 0 1-.01-.75c.13-.24.38-.38.65-.38 4.27 0 7.75 3.48 7.75 7.75a.74.74 0 0 1-.74.75"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineCloudChangeIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
