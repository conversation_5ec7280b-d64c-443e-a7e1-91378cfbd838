<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path d="M0 .333h32v21.334H0z"></path><path d="m15.998 2.698.266.819h.865l-.697.506.263.82-.697-.507-.697.506.267-.819-.697-.506h.861zm-3.555.948.266.819h.864l-.696.506.262.82-.696-.507-.697.506.266-.819-.697-.506h.861zM9.835 6.254l.266.818h.865l-.696.506.262.82-.697-.506-.697.506.267-.82-.697-.505h.861l.266-.82Zm-.948 3.555.266.819h.865l-.697.506.263.82-.697-.507-.697.506.266-.819-.696-.506h.86zm.948 3.556.266.818h.865l-.696.507.262.819-.697-.506-.697.506.267-.819-.697-.506h.861l.266-.82Zm2.607 2.607.267.819h.864l-.696.506.262.82-.696-.507-.697.506.266-.819-.697-.506h.861l.267-.819Zm7.112-12.326.266.819h.865l-.697.506.262.82-.696-.507-.697.506.266-.819-.697-.506h.862zm2.607 2.608.266.818h.865l-.697.506.263.82-.697-.506-.697.506.267-.82-.697-.505h.86l.267-.82Zm.948 3.555.266.819h.865l-.696.506.262.82-.697-.507-.697.506.267-.819-.697-.506h.861zm-.948 3.556.266.818h.865l-.697.507.263.819-.697-.506-.697.506.267-.819-.697-.506h.86l.267-.82Zm-6.163 3.556.266.818h.865l-.697.506.263.82-.697-.507-.697.506.267-.819-.697-.506h.861zm3.556-.949.266.819h.865l-.697.506.262.82-.696-.507-.697.506.266-.819-.697-.506h.862z"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 .333h32v21.333H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaEuFlagIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
