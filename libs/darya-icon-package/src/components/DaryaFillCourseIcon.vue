<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M13.218 3.261a2.97 2.97 0 0 0-2.436 0L2.594 6.937A1.01 1.01 0 0 0 2 7.86c0 .399.233.76.594.923l8.188 3.675a2.97 2.97 0 0 0 2.436 0l8.188-3.675A1.01 1.01 0 0 0 22 7.86c0-.4-.233-.761-.594-.923zm-1.624 1.846a1 1 0 0 1 .812 0l6.132 2.753-6.132 2.752a1 1 0 0 1-.812 0L5.462 7.86z" clip-rule="evenodd"></path><path d="M5 12.893v5.572c0 .157.036.311.106.452.216.436.59.829.996 1.157.421.34.943.665 1.535.946 1.181.562 2.7.98 4.363.98 1.662 0 3.182-.418 4.363-.98a7.8 7.8 0 0 0 1.535-.946c.407-.328.78-.72.996-1.157.07-.14.106-.295.106-.452v-5.572l-2 .898v4.372c-.073.086-.186.2-.351.334a5.8 5.8 0 0 1-1.137.695A8.3 8.3 0 0 1 12 19.98a8.3 8.3 0 0 1-3.512-.788 5.8 5.8 0 0 1-1.137-.695A2.6 2.6 0 0 1 7 18.163V13.79z"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaFillCourseIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
