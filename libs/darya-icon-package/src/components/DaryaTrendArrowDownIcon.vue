<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M12.738 11.498c-.19 0-.38-.07-.53-.22l-3.55-3.55-1.09 1.64c-.12.19-.33.31-.55.33a.73.73 0 0 1-.6-.22l-3.2-3.2a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.55 2.55 1.09-1.64c.12-.18.32-.3.55-.33.23-.**********.22l4.2 4.2c.*********** 0 1.06-.15.15-.34.22-.53.22"></path><path d="M12.738 11.498h-2c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h1.25v-1.25c0-.41.34-.75.75-.75s.75.34.75.75v2c0 .41-.34.75-.75.75"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaTrendArrowDownIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
