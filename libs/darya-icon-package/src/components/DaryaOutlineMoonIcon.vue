<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M12.46 22.75c-.17 0-.34 0-.51-.01-5.6-.25-10.28-4.76-10.67-10.26C.94 7.76 3.67 3.35 8.07 1.5c1.25-.52 1.91-.12 **********.*********** 2.12a8.2 8.2 0 0 0-.68 3.35c.02 4.43 3.7 8.19 8.19 ********** 1.29-.02 1.91-.13 1.32-.24 1.87.29 2.08.63s.45 1.07-.35 2.15c-2.12 2.9-5.49 4.59-9.1 4.59M2.77 12.37c.34 4.76 4.4 8.66 9.24 8.87 3.29.16 6.41-1.34 8.33-3.96.15-.21.22-.36.25-.44-.09-.01-.25-.02-.5.03-.73.13-1.49.18-2.24.15-5.28-.21-9.6-4.64-9.63-9.86 0-1.38.27-2.71.82-3.96.1-.22.12-.37.13-.45-.09 0-.25.02-.51.13-3.81 1.6-6.17 5.42-5.89 9.49"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineMoonIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
