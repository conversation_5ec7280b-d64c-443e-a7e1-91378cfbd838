<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M12 18.31c-.47 0-.92-.24-1.23-.65l-.72-.96H9.9c-2.02 0-3.9-.47-3.9-3.9v-2.63C6 7.86 7.19 6.49 9.34 6.3c.16-.02.35-.03.56-.03h4.2c2.51 0 3.9 1.38 3.9 3.9v2.63c0 .2-.01.4-.03.58-.18 2.12-1.55 3.31-3.86 3.31h-.15l-.72.96c-.32.43-.77.66-1.24.66M9.9 7.79c-.14 0-.27 0-.4.02-1.41.12-2 .83-2 2.38v2.63c0 2.07.57 2.4 2.4 2.4h.26c.38 0 .79.2 1.02.5l.79 1.06.85-1.05c.24-.32.62-.51 1.02-.51h.26c1.55 0 2.25-.59 2.37-1.97.02-.16.02-.29.02-.43v-2.63c0-1.68-.72-2.4-2.4-2.4z"></path><path d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12S6.07 1.25 12 1.25 22.75 6.07 22.75 12 17.93 22.75 12 22.75m0-20C6.9 2.75 2.75 6.9 2.75 12S6.9 21.25 12 21.25s9.25-4.15 9.25-9.25S17.1 2.75 12 2.75"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineMessageCircleIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
