<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <rect width="14" height="1.5" x="5" y="7" rx=".75"></rect><rect width="14" height="1.5" x="5" y="11.25" rx=".75"></rect><rect width="14" height="1.5" x="5" y="15.5" rx=".75"></rect>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaBurgerMenuIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
