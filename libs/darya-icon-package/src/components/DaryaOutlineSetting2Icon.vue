<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M12 15.75c-2.07 0-3.75-1.68-3.75-3.75S9.93 8.25 12 8.25s3.75 1.68 3.75 3.75-1.68 3.75-3.75 3.75m0-6c-1.24 0-2.25 1.01-2.25 2.25s1.01 2.25 2.25 2.25 2.25-1.01 2.25-2.25S13.24 9.75 12 9.75"></path><path d="M15.21 22.19c-.21 0-.42-.03-.63-.08-.62-.17-1.14-.56-1.47-1.11l-.12-.2c-.59-1.02-1.4-1.02-1.99 0l-.11.19c-.33.56-.85.96-1.47 1.12-.63.17-1.28.08-1.83-.25l-1.72-.99a2.65 2.65 0 0 1-.98-3.62c.29-.51.37-.97.2-1.26s-.6-.46-1.19-.46c-1.46 0-2.65-1.19-2.65-2.65v-1.76c0-1.46 1.19-2.65 2.65-2.65.59 0 1.02-.17 1.19-.46s.1-.75-.2-1.26c-.35-.61-.44-1.33-.26-2.01.18-.69.62-1.26 1.24-1.61l1.73-.99c1.13-.67 2.62-.28 3.3.87l.12.2c.59 1.02 1.4 1.02 1.99 0l.11-.19c.68-1.16 2.17-1.55 3.31-.87l1.72.99a2.65 2.65 0 0 1 .98 3.62c-.29.51-.37.97-.2 1.26s.6.46 1.19.46c1.46 0 2.65 1.19 2.65 2.65v1.76c0 1.46-1.19 2.65-2.65 2.65-.59 0-1.02.17-1.19.46s-.1.75.2 1.26c.35.61.45 1.33.26 2.01a2.58 2.58 0 0 1-1.24 1.61l-1.73.99c-.38.21-.79.32-1.21.32M12 18.49c.89 0 1.72.56 2.29 1.55l.11.19c.12.21.32.36.56.42s.48.03.68-.09l1.73-1a1.157 1.157 0 0 0 .43-1.57c-.57-.98-.64-1.99-.2-2.76s1.35-1.21 2.49-1.21c.64 0 1.15-.51 1.15-1.15v-1.76c0-.63-.51-1.15-1.15-1.15-1.14 0-2.05-.44-2.49-1.21s-.37-1.78.2-2.76c.15-.26.19-.57.11-.87s-.27-.54-.53-.7l-1.73-.99a.92.92 0 0 0-1.26.33l-.11.19c-.57.99-1.4 1.55-2.29 1.55s-1.72-.56-2.29-1.55l-.11-.2a.92.92 0 0 0-1.24-.32l-1.73 1A1.157 1.157 0 0 0 6.19 6c.57.98.64 1.99.2 2.76S5.04 9.97 3.9 9.97c-.64 0-1.15.51-1.15 1.15v1.76c0 .63.51 1.15 1.15 1.15 1.14 0 2.05.44 2.49 1.21s.37 1.78-.2 2.76c-.15.26-.19.57-.11.87s.27.54.53.7l1.73.99c.***********.69.1.24-.06.44-.22.57-.43l.11-.19c.57-.98 1.4-1.55 2.29-1.55"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineSetting2Icon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
