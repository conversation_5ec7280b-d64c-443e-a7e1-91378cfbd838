<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M14.296 5.296a.75.75 0 0 1 1.06 0l3.35 3.35a.75.75 0 0 1-1.06 1.06l-3.35-3.35a.75.75 0 0 1 0-1.06" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M9 4.75A4.25 4.25 0 0 0 4.75 9v6A4.25 4.25 0 0 0 9 19.25h6A4.25 4.25 0 0 0 19.25 15v-2a.75.75 0 0 1 1.5 0v2A5.75 5.75 0 0 1 15 20.75H9A5.75 5.75 0 0 1 3.25 15V9A5.75 5.75 0 0 1 9 3.25h2a.75.75 0 0 1 0 1.5z" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M18.776 5.225a1.62 1.62 0 0 0-2.289 0l-5.473 5.473a.9.9 0 0 0-.264.636v1.667c0 .*************.25h1.665a.9.9 0 0 0 .637-.264l5.474-5.473a1.62 1.62 0 0 0 0-2.29zm-3.35-1.06a3.118 3.118 0 0 1 4.41 4.41l-5.474 5.473a2.4 2.4 0 0 1-1.697.703H11a1.75 1.75 0 0 1-1.75-1.75v-1.667a2.4 2.4 0 0 1 .703-1.697" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaEditIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
