<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M13.902 2.842c.151-1.209-1.385-1.848-2.135-.889L3.465 12.561c-.616.787-.055 1.939.945 1.939h5.965l-.786 6.683c-.141 1.2 1.382 1.833 2.133.885l8.805-11.123c.623-.787.063-1.945-.94-1.945h-6.454z"></path><path d="m11 5-6 8 5-.5z"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaFillStreakOnIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
