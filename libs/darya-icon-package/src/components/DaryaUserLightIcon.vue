<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M13.945 5.055a2.75 2.75 0 1 0-3.89 3.89 2.75 2.75 0 0 0 3.89-3.89m1.06-1.06a4.25 4.25 0 1 0-6.01 6.01 4.25 4.25 0 0 0 6.01-6.01m-8.9 10.225c1.627-.947 3.762-1.462 5.895-1.462s4.268.516 5.895 1.463c1.621.943 2.855 2.397 2.855 4.279v1A1.75 1.75 0 0 1 19 21.25H5a1.75 1.75 0 0 1-1.75-1.75v-1c0-1.882 1.234-3.336 2.855-4.28m.754 1.297c-1.367.796-2.109 1.838-2.109 2.983v1c0 .*************.25h14a.25.25 0 0 0 .25-.25v-1c0-1.145-.742-2.187-2.11-2.983-1.36-.792-3.225-1.26-5.14-1.26s-3.78.468-5.14 1.26z" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaUserLightIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
