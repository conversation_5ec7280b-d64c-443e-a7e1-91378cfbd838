<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M14.75 19c0 1.52-1.23 2.75-2.75 2.75S9.25 20.52 9.25 19s1.23-2.75 2.75-2.75 2.75 1.23 2.75 2.75m-4 0a1.25 1.25 0 1 0 2.5 0 1.25 1.25 0 0 0-2.5 0m4-14c0 1.52-1.23 2.75-2.75 2.75S9.25 6.52 9.25 5 10.48 2.25 12 2.25 14.75 3.48 14.75 5m-4 0a1.25 1.25 0 1 0 2.5 0 1.25 1.25 0 0 0-2.5 0m4 7c0 1.52-1.23 2.75-2.75 2.75S9.25 13.52 9.25 12 10.48 9.25 12 9.25s2.75 1.23 2.75 2.75m-4 0a1.25 1.25 0 1 0 2.5 0 1.25 1.25 0 0 0-2.5 0"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineHorizentalMoreIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
