<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M6 22.75c-.3 0-.62-.03-.94-.09-.17-.04-.32-.08-.47-.12-.17-.05-.33-.11-.48-.17-.04-.01-.07-.03-.1-.04a6.6 6.6 0 0 1-.92-.56c-.02-.01-.03-.03-.05-.04a4 4 0 0 1-.42-.38c-.14-.15-.24-.27-.35-.39-.23-.3-.42-.6-.59-.95-.02-.03-.03-.07-.04-.11-.06-.14-.11-.28-.15-.43-.06-.21-.1-.35-.13-.5-.07-.35-.1-.66-.1-.96V4.51c0-1.91 1.34-3.25 3.25-3.25h3c1.91 0 3.25 1.34 3.25 3.25V18c0 1.24-.48 2.42-1.35 3.31-.16.16-.28.27-.39.36-.32.28-.73.53-1.17.71-.1.04-.22.09-.35.14-.49.15-1 .23-1.5.23M4.74 21c.1.04.2.07.3.1l.33.09c.55.1 1.14.07 1.61-.09.07-.03.17-.06.26-.1.32-.13.58-.29.81-.48.1-.08.17-.15.24-.21.63-.65.96-1.45.96-2.3V4.51c0-1.08-.67-1.75-1.75-1.75h-3c-1.08 0-1.75.67-1.75 1.75V18c0 .21.02.43.07.66.02.09.05.2.08.32.04.11.07.2.11.3.02.03.03.06.04.08s.02.05.03.07c.12.22.24.41.37.58.07.08.16.18.25.28.11.12.2.19.3.27.01.01.03.02.04.03.16.12.34.23.55.34.02.01.04.01.06.02s.05.03.09.05"></path><path d="M19.5 22.75H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75c.35 0 .69-.05.98-.15.07-.03.17-.06.26-.1.32-.13.58-.29.81-.48.1-.08.17-.15.24-.21l6.84-6.83a.75.75 0 0 1 .53-.22h3.84c1.91 0 3.25 1.34 3.25 3.25v3c0 1.9-1.34 3.24-3.25 3.24m-10.04-1.5H19.5c1.08 0 1.75-.67 1.75-1.75v-3c0-1.08-.67-1.75-1.75-1.75h-3.53z"></path><path d="M4.81 22.57q-.105 0-.21-.03c-.74-.22-1.42-.63-1.96-1.18-.54-.53-.95-1.21-1.17-1.95-.12-.4.1-.81.5-.93s.81.1.94.49a3.27 3.27 0 0 0 2.14 2.14c.***********.49.94-.11.31-.41.52-.73.52m4.05-1.03a.754.754 0 0 1-.54-1.28c.6-.61.93-1.42.93-2.27V8.33c0-.2.08-.39.22-.53l2.71-2.71c1.31-1.31 3.29-1.31 4.6 0l2.12 2.12c1.35 1.35 1.35 3.25 0 4.6l-9.51 9.5c-.15.16-.34.23-.53.23m1.89-12.89v9.19l7.09-7.08c.76-.76.76-1.71 0-2.48l-2.12-2.12c-.75-.75-1.73-.75-2.48 0zM6 19.75c-.96 0-1.75-.79-1.75-1.75s.79-1.75 1.75-1.75 1.75.79 1.75 1.75-.79 1.75-1.75 1.75m0-2c-.14 0-.25.11-.25.25 0 .********* 0 0-.14-.11-.25-.25-.25"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineColorSwatchIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
