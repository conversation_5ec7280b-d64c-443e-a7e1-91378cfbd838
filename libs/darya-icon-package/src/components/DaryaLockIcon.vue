<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M7 9.75c-.69 0-1.25.56-1.25 1.25v8c0 .69.56 1.25 1.25 1.25h10c.69 0 1.25-.56 1.25-1.25v-8c0-.69-.56-1.25-1.25-1.25zM4.25 11A2.75 2.75 0 0 1 7 8.25h10A2.75 2.75 0 0 1 19.75 11v8A2.75 2.75 0 0 1 17 21.75H7A2.75 2.75 0 0 1 4.25 19z" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M12 13.75a.75.75 0 0 1 .75.75v2.59a.75.75 0 0 1-1.5 0V14.5a.75.75 0 0 1 .75-.75" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M10.94 12.69a1.5 1.5 0 1 1 2.12 2.121 1.5 1.5 0 0 1-2.12-2.122M12 3.75A3.25 3.25 0 0 0 8.75 7v2a.75.75 0 0 1-1.5 0V7a4.75 4.75 0 0 1 9.5 0v2a.75.75 0 0 1-1.5 0V7A3.25 3.25 0 0 0 12 3.75" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaLockIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
