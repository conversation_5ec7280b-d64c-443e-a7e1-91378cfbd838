<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path fill="#F0F0F0" d="M12 24c6.627 0 12-5.373 12-12S18.627 0 12 0 0 5.373 0 12s5.373 12 12 12" class="darya-icon__ignore"></path><path fill="#D80027" d="M23.898 10.435H13.565V.102a12 12 0 0 0-3.13 0v10.333H.102a12 12 0 0 0 0 3.13h10.333v10.333a12 12 0 0 0 3.13 0V13.565h10.333a12 12 0 0 0 0-3.13" class="darya-icon__ignore"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 0h24v24H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaEnglandIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
