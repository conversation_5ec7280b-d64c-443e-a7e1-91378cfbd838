<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill="#fff" fill-opacity=".01" d="M24 0v24H0V0z" class="darya-icon__ignore"></path><path fill="#0866FF" fill-rule="evenodd" d="M22 12c0 5.112-3.837 9.328-8.788 9.928v-6.873h2.704l.56-3.055h-3.268v-1.08c0-1.617.632-2.237 2.272-2.237.508 0 .92.012 1.156.036V5.951c-.448-.124-1.54-.248-2.172-.248-3.34 0-4.88 1.576-4.88 4.98V12H7.52v3.056h2.064v6.65C5.228 20.626 2 16.69 2 12 2 6.477 6.477 2 12 2s10 4.477 10 10" class="darya-icon__ignore" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaLogoFacebookIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
