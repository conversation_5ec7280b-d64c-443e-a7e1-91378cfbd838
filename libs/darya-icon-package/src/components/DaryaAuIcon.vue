<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path fill="#10338C" d="M0 .333h32v21.334H0z" class="darya-icon__ignore"></path><path d="M13.86 10.981c.019-.019.032-.037.05-.056q-.02.027-.05.056m-2.133 2.614.686 1.428 1.54-.355-.685 1.422 1.241.985-1.547.35.006 1.584-1.241-.992-1.235.992.006-1.584-1.547-.35 1.242-.985-.693-1.422 1.547.355zm12.345 1.777.324.68.736-.168-.33.68.592.468-.736.162v.76l-.586-.473-.593.474.006-.761-.736-.162.593-.468-.33-.68.735.168zM21.04 8.137l.324.68.736-.169-.33.68.593.468-.737.168v.755l-.586-.474-.592.474.006-.755-.743-.168.593-.468-.33-.68.735.168.331-.68Zm3.032-4.13.324.68.736-.168-.33.68.592.467-.736.169v.755l-.586-.475-.593.475.006-.755-.736-.169.593-.468-.33-.68.735.169zm2.65 3.1.325.68.736-.168-.33.68.592.468-.736.162v.76l-.586-.473-.593.474v-.761l-.736-.162.593-.468-.331-.68.736.168zm-1.896 3.612.256.792h.83l-.674.487.262.792-.674-.493-.673.493.256-.792-.668-.487h.83z"></path><path d="M15.894.333v1.909L13.08 3.808h2.814v3.699h-3.687l3.687 2.046v1.428h-1.666L9.643 8.43v2.551H6.175V7.95L.723 10.98h-.798V9.072l2.82-1.565h-2.82v-3.7H3.61L-.075 1.756V.333H1.59l4.585 2.545V.333h3.468v3.032L15.101.333z"></path><path d="M8.907.333H6.911v4.323H-.075v1.996H6.91v4.33h1.996v-4.33h6.987V4.656H8.907z"></path><use xlink:href="#reuse-0"></use><use xlink:href="#reuse-0"></use><path d="m9.643 7.507 6.25 3.475v-.986l-4.478-2.489zm-5.233 0L-.075 9.996v.986l6.25-3.475zM6.175 3.808-.075.333v.98L4.41 3.808z"></path><path d="M6.175 3.808-.075.333v.98L4.41 3.808z"></path><path d="M6.175 3.808-.075.333v.98L4.41 3.808zm5.24 0 4.479-2.495v-.98l-6.25 3.475h1.77Z"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 .333h32v21.333H0z"></path></clipPath><path d="m9.644 7.507 6.25 3.475v-.986l-4.479-2.489z" id="reuse-0"></path></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaAuIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
