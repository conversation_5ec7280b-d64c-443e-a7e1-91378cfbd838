<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M17 21.75H7c-4.41 0-5.75-1.34-5.75-5.75V8c0-4.41 1.34-5.75 5.75-5.75h10c4.41 0 5.75 1.34 5.75 5.75v8c0 4.41-1.34 5.75-5.75 5.75m-10-18c-3.58 0-4.25.68-4.25 4.25v8c0 3.57.67 4.25 4.25 4.25h10c3.58 0 4.25-.68 4.25-4.25V8c0-3.57-.67-4.25-4.25-4.25z"></path><path d="M19 8.75h-5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h5c.41 0 .75.34.75.75s-.34.75-.75.75m0 4h-4c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75m0 4h-2c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h2c.41 0 .75.34.75.75s-.34.75-.75.75M8.5 12.04c-1.41 0-2.56-1.15-2.56-2.56S7.09 6.92 8.5 6.92s2.56 1.15 2.56 2.56-1.15 2.56-2.56 2.56m0-3.62c-.58 0-1.06.48-1.06 1.06s.48 1.06 1.06 1.06 1.06-.48 1.06-1.06-.48-1.06-1.06-1.06m3.5 8.66c-.38 0-.71-.29-.75-.68a2.3 2.3 0 0 0-2.07-2.05 8 8 0 0 0-1.38 0c-1.09.1-1.96.96-2.07 2.05-.04.41-.41.72-.82.67a.75.75 0 0 1-.67-.82c.18-1.8 1.61-3.23 3.42-3.39.55-.05 1.11-.05 1.66 0 1.8.17 3.24 1.6 3.42 3.39a.75.75 0 0 1-.67.82c-.02.01-.05.01-.07.01"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlinePersonalCardIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
