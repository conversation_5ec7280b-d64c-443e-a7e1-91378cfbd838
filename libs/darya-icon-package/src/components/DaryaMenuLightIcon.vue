<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M3 6.599A3.6 3.6 0 0 1 6.599 3h1.153a3.6 3.6 0 0 1 3.6 3.599v1.153a3.6 3.6 0 0 1-3.6 3.6H6.6A3.6 3.6 0 0 1 3 7.751zm3.599-2.14a2.14 2.14 0 0 0-2.14 2.14v1.153a2.14 2.14 0 0 0 2.14 2.14h1.153a2.14 2.14 0 0 0 2.14-2.14V6.6a2.14 2.14 0 0 0-2.14-2.14zM3 16.248a3.6 3.6 0 0 1 3.599-3.6h1.153a3.6 3.6 0 0 1 3.6 3.6V17.4a3.6 3.6 0 0 1-3.6 3.6H6.6A3.6 3.6 0 0 1 3 17.4v-1.153Zm3.599-2.14a2.14 2.14 0 0 0-2.14 2.14V17.4a2.14 2.14 0 0 0 2.14 2.14h1.153a2.14 2.14 0 0 0 2.14-2.14v-1.153a2.14 2.14 0 0 0-2.14-2.14H6.6ZM12.648 6.6a3.6 3.6 0 0 1 3.6-3.6H17.4A3.6 3.6 0 0 1 21 6.6v1.153a3.6 3.6 0 0 1-3.6 3.599h-1.153a3.6 3.6 0 0 1-3.599-3.6zm3.6-2.14a2.14 2.14 0 0 0-2.14 2.14v1.153a2.14 2.14 0 0 0 2.14 2.14H17.4a2.14 2.14 0 0 0 2.14-2.14V6.599a2.14 2.14 0 0 0-2.14-2.14h-1.154Zm-3.6 11.788a3.6 3.6 0 0 1 3.6-3.6H17.4a3.6 3.6 0 0 1 3.599 3.6V17.4a3.6 3.6 0 0 1-3.6 3.6h-1.153a3.6 3.6 0 0 1-3.599-3.6v-1.153Zm3.6-2.14a2.14 2.14 0 0 0-2.14 2.14V17.4a2.14 2.14 0 0 0 2.14 2.14H17.4a2.14 2.14 0 0 0 2.14-2.14v-1.153a2.14 2.14 0 0 0-2.14-2.14h-1.154Z" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaMenuLightIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
