<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M17.25 8a.75.75 0 0 1 .75-.75h1A2.75 2.75 0 0 1 21.75 10v7A2.75 2.75 0 0 1 19 19.75H8A2.75 2.75 0 0 1 5.25 17v-1.583a.75.75 0 0 1 1.5 0V17c0 .69.56 1.25 1.25 1.25h11c.69 0 1.25-.56 1.25-1.25v-7c0-.69-.56-1.25-1.25-1.25h-1a.75.75 0 0 1-.75-.75m-11.125.417a.75.75 0 0 1 .75.75v2.083a.75.75 0 0 1-1.5 0V9.167a.75.75 0 0 1 .75-.75" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M14.979 8.417a.75.75 0 0 1 .75.75v2.083a.75.75 0 0 1-1.5 0V9.167a.75.75 0 0 1 .75-.75" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M2.25 7.083A2.834 2.834 0 0 1 5.083 4.25h10.834a2.83 2.83 0 0 1 2.832 2.835v6.249a2.833 2.833 0 0 1-2.833 2.833H5.083a2.834 2.834 0 0 1-2.833-2.834zM5.083 5.75c-.736 0-1.333.597-1.333 1.333v6.25c0 .737.597 1.334 1.333 1.334h10.833c.737 0 1.333-.597 1.333-1.333v-6.25c0-.736-.596-1.334-1.332-1.334z" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M11.126 9.633a.813.813 0 1 0-1.149 1.151.813.813 0 0 0 1.15-1.15zm1.061-1.06a2.313 2.313 0 1 0-3.27 3.272 2.313 2.313 0 0 0 3.27-3.272" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaTradeIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
