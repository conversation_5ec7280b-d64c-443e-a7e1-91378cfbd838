<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M12.319 22c-5.32 0-8.819-4.5-8.294-9.398.43-4.026 4.995-9.653 7.491-11.507.355-.264.73.06.649.514-.649 2.06-.915 4.891 1.564 6.278a.453.453 0 0 0 .62-.151c.373-.604.527-1.409.59-2.088.047-.496.529-.76.85-.4 1.474 1.65 3.37 5.08 3.865 6.706C20.5 14.731 21 22 12.32 22m-.222-3.44c-2.388 0-3.346-1.829-3.041-3.331.286-1.413 2.005-3.486 2.801-4.345a.31.31 0 0 1 .462.017l.225.26c.823.942 2.338 2.678 2.61 4.046.305 1.524-.667 3.354-3.057 3.354" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaFillFlameIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
