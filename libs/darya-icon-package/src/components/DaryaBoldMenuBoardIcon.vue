<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="m20.9 9.85.59 9.89c.02.27-.11.45-.18.53-.08.09-.25.23-.53.23h-2.73l2.16-10.65zM22 6l-.01.02c.02.24 0 .49-.06.74l-7.37 13.53A2.23 2.23 0 0 1 12.38 22h8.4a2.21 2.21 0 0 0 2.21-2.38zM11.45 2.24c.1-.4-.15-.81-.55-.91-.4-.09-.81.15-.91.55l-.5 2.07h1.54zm6.6-.03c.09-.41-.17-.8-.58-.89a.75.75 0 0 0-.89.58l-.45 2.07h1.54z"></path><path d="M21.82 5.33c-.33-.8-1.11-1.37-2.07-1.37h-2.08l-.56 2.59c-.08.35-.39.59-.73.59-.05 0-.11 0-.16-.02a.765.765 0 0 1-.58-.89l.49-2.28h-5.1l-.63 2.6c-.08.34-.39.57-.73.57-.06 0-.12-.01-.18-.02a.75.75 0 0 1-.55-.91l.54-2.25H7.45c-.98 0-1.85.64-2.14 1.58L1.1 19.07C.66 20.52 1.73 22 3.24 22h13.14c1.04 0 1.94-.7 2.18-1.71l3.37-13.53c.06-.25.08-.5.06-.74a2 2 0 0 0-.17-.69M14.7 16.75h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75m1-4h-8c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h8c.41 0 .75.34.75.75s-.34.75-.75.75"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaBoldMenuBoardIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
