<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M20.5 14.76c.19 0 .38-.07.53-.22.29-.29.29-.77 0-1.06l-5.01-5.01a.754.754 0 0 0-1.06 0c-.29.29-.29.77 0 1.06l5.01 5.01c.***********.53.22"></path><path d="M3.5 14.76h17c.41 0 .75-.34.75-.75s-.34-.75-.75-.75h-17c-.41 0-.75.34-.75.75s.***********"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaArrowSwapHorizontalIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
