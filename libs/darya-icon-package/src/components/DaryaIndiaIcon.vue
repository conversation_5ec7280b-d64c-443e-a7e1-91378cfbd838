<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path fill="#F0F0F0" d="M12 24c6.627 0 12-5.373 12-12S18.627 0 12 0 0 5.373 0 12s5.373 12 12 12" class="darya-icon__ignore"></path><path fill="#FF9811" d="M12 0A12 12 0 0 0 1.19 6.783h21.62A12 12 0 0 0 12 0" class="darya-icon__ignore"></path><path fill="#6DA544" d="M12 24a12 12 0 0 0 10.81-6.783H1.19A12 12 0 0 0 12 24" class="darya-icon__ignore"></path><path fill="#0052B4" d="M12 16.174a4.174 4.174 0 1 0 0-8.348 4.174 4.174 0 0 0 0 8.348" class="darya-icon__ignore"></path><path fill="#F0F0F0" d="M12 14.609a2.609 2.609 0 1 0 0-5.218 2.609 2.609 0 0 0 0 5.218" class="darya-icon__ignore"></path><path fill="#0052B4" d="m12 8.78.805 1.826 1.983-.216L13.61 12l1.178 1.61-1.983-.216L12 15.219l-.805-1.825-1.983.216L10.39 12l-1.178-1.61 1.983.216z" class="darya-icon__ignore"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 0h24v24H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaIndiaIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
