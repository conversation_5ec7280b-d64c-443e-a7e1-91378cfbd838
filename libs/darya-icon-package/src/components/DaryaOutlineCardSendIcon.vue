<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M14.5 9.25H2c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h12.5c.41 0 .75.34.75.75s-.34.75-.75.75m-6.5 8H6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h2c.41 0 .75.34.75.75s-.34.75-.75.75m6.5 0h-4c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h4c.41 0 .75.34.75.75s-.34.75-.75.75"></path><path d="M17.56 21.25H6.44c-3.98 0-5.19-1.2-5.19-5.14V7.89c0-3.94 1.21-5.14 5.19-5.14h8.06c.41 0 .75.34.75.75s-.34.75-.75.75H6.44c-3.14 0-3.69.54-3.69 3.64v8.21c0 3.1.55 3.64 3.69 3.64h11.11c3.14 0 3.69-.54 3.69-3.64v-2.08c0-.41.34-.75.75-.75s.75.34.75.75v2.08c.01 3.95-1.2 5.15-5.18 5.15"></path><path d="M20 10.25c-.41 0-.75-.34-.75-.75v-6c0-.3.18-.58.46-.69s.6-.05.82.16l2 2c.*********** 0 1.06s-.77.29-1.06 0l-.72-.72V9.5c0 .41-.34.75-.75.75"></path><path d="M18 6.25c-.19 0-.38-.07-.53-.22a.754.754 0 0 1 0-1.06l2-2c.29-.29.77-.29 1.06 0s.29.77 0 1.06l-2 2c-.15.15-.34.22-.53.22"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineCardSendIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
