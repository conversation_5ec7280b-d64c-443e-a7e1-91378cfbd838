<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M19.828 4.892a4.46 4.46 0 0 0-3.656 0L3.891 10.405A1.52 1.52 0 0 0 3 11.789c0 .6.35 1.142.89 1.385l12.282 5.513a4.46 4.46 0 0 0 3.656 0l12.281-5.513c.542-.243.891-.786.891-1.385 0-.598-.35-1.14-.89-1.384zM17.39 7.66c.388-.174.83-.174 1.218 0l9.198 4.128-9.198 4.13c-.388.173-.83.173-1.218 0l-9.198-4.13z" clip-rule="evenodd"></path><path d="M7.5 19.34v8.357c0 .236.054.468.158.678.325.655.885 1.244 1.495 1.736.632.511 1.415.997 2.302 1.42A15.4 15.4 0 0 0 18 33a15.4 15.4 0 0 0 6.545-1.47c.887-.422 1.67-.908 2.302-1.419.61-.492 1.17-1.08 1.495-1.736.104-.21.158-.442.158-.678V19.34l-3 1.347v6.558a4 4 0 0 1-.527.501 8.7 8.7 0 0 1-1.705 1.043A12.5 12.5 0 0 1 18 29.97a12.5 12.5 0 0 1-5.268-1.181 8.7 8.7 0 0 1-1.705-1.044 4 4 0 0 1-.527-.501v-6.558z"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaCourseIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
