<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill="#15195A" d="M10.656 11.659H9.058l.999-5.95h1.598zm5.792-5.805a4.1 4.1 0 0 0-1.433-.253c-1.578 0-2.689.81-2.696 1.969-.013.854.796 1.329 1.4 1.614.619.291.83.481.83.74-.007.4-.5.583-.96.583-.639 0-.98-.095-1.5-.316l-.21-.096-.224 1.336c.375.165 1.066.31 1.782.317 1.677 0 2.768-.798 2.781-2.032.007-.678-.42-1.197-1.34-1.62-.56-.273-.902-.457-.902-.735.006-.253.29-.513.92-.513.52-.012.901.108 1.19.228l.145.063z" class="darya-icon__ignore"></path><path fill="#15195A" fill-rule="evenodd" d="M19.308 5.709h1.236l1.29 5.95h-1.48l-.19-.893H18.11l-.335.893H16.1l2.373-5.457c.165-.386.454-.493.835-.493m-.098 2.177-.638 1.665H19.9l-.368-1.722-.112-.512c-.047.132-.115.313-.16.436z" class="darya-icon__ignore" clip-rule="evenodd"></path><path fill="#15195A" d="M4.705 5.709c.348.012.632.12.724.5l.557 2.732.001.002.171.823L7.724 5.71h1.69L6.9 11.653H5.21l-1.424-5.17a6.7 6.7 0 0 0-1.679-.654l.027-.12z" class="darya-icon__ignore"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaVisaIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
