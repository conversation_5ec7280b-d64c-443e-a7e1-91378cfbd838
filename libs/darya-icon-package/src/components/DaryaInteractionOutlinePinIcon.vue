<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M25.334 4.315a4.49 4.49 0 0 0-6.727 5.924l-5.68 2.84-1.353-1.354a4.49 4.49 0 0 0-6.351 6.35l5.292 5.293L3 30.883l.106 2.011L5.117 33l7.515-7.515 5.292 5.292a4.49 4.49 0 0 0 6.351-6.35l-1.354-1.355 2.84-5.678a4.491 4.491 0 0 0 5.924-6.728zM21.1 6.432a1.497 1.497 0 0 1 2.117 0l6.35 6.351a1.497 1.497 0 0 1-2.116 2.117l-1.059-1.058a1.497 1.497 0 0 0-2.397.389l-4.234 8.467a1.5 1.5 0 0 0 .28 1.728l2.117 2.117a1.497 1.497 0 0 1-2.117 2.117L7.34 15.96a1.497 1.497 0 1 1 2.117-2.117l2.117 2.117a1.5 1.5 0 0 0 1.728.28l8.467-4.234a1.497 1.497 0 0 0 .39-2.397l-1.06-1.059a1.497 1.497 0 0 1 0-2.117" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaInteractionOutlinePinIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
