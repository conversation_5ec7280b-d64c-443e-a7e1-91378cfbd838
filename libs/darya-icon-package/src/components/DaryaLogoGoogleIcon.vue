<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill="#fff" fill-opacity=".01" d="M24 0v24H0V0z" class="darya-icon__ignore"></path><path fill="#4285F4" d="M21.634 12.187c0-.82-.067-1.417-.21-2.037h-9.357v3.698h5.492c-.111.92-.709 2.303-2.038 3.233l-.018.124 2.958 2.292.205.02c1.882-1.738 2.968-4.296 2.968-7.33" class="darya-icon__ignore"></path><path fill="#34A853" d="M12.067 21.931c2.69 0 4.949-.886 6.599-2.414l-3.145-2.436c-.841.587-1.97.997-3.455.997-2.635 0-4.872-1.739-5.669-4.141l-.117.01-3.076 2.38-.04.112a9.96 9.96 0 0 0 8.903 5.492" class="darya-icon__ignore"></path><path fill="#FBBC05" d="M6.397 13.937a6.1 6.1 0 0 1-.332-1.971c0-.687.122-1.351.321-1.971l-.006-.132-3.114-2.42-.102.05A10 10 0 0 0 2.1 11.965c0 1.605.387 3.122 1.063 4.473z" class="darya-icon__ignore"></path><path fill="#EB4335" d="M12.067 5.853c1.87 0 3.133.809 3.853 1.484l2.812-2.746C17.005 2.985 14.757 2 12.067 2a9.96 9.96 0 0 0-8.903 5.492l3.222 2.503c.808-2.403 3.045-4.142 5.68-4.142" class="darya-icon__ignore"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaLogoGoogleIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
