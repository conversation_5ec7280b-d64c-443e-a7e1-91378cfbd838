<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M18 10.75c-.41 0-.75-.34-.75-.75V8c0-3.15-.89-5.25-5.25-5.25S6.75 4.85 6.75 8v2c0 .41-.34.75-.75.75s-.75-.34-.75-.75V8c0-2.9.7-6.75 6.75-6.75S18.75 5.1 18.75 8v2c0 .41-.34.75-.75.75"></path><path d="M17 22.75H7c-4.41 0-5.75-1.34-5.75-5.75v-2c0-4.41 1.34-5.75 5.75-5.75h10c4.41 0 5.75 1.34 5.75 5.75v2c0 4.41-1.34 5.75-5.75 5.75m-10-12c-3.58 0-4.25.68-4.25 4.25v2c0 3.57.67 4.25 4.25 4.25h10c3.58 0 4.25-.68 4.25-4.25v-2c0-3.57-.67-4.25-4.25-4.25z"></path><path d="M8 17c-.13 0-.26-.03-.38-.08-.13-.05-.23-.12-.33-.21-.18-.19-.29-.44-.29-.71 0-.13.03-.26.08-.38.05-.13.12-.23.21-.33.1-.09.2-.16.33-.21.36-.16.81-.07 1.09.21q.135.15.21.33c.***********.08.38 0 .26-.11.52-.29.71-.19.18-.45.29-.71.29m4 0c-.26 0-.52-.11-.71-.29-.18-.19-.29-.44-.29-.71 0-.13.02-.26.08-.38q.075-.18.21-.33c.23-.23.58-.34.9-.***********.03.19.06q.09.03.18.09c.**********.15.12q.135.15.21.33c.***********.08.38 0 .27-.11.52-.29.71l-.15.12q-.09.06-.18.09c-.06.03-.12.05-.19.06-.06.01-.13.02-.19.02m4 0c-.27 0-.52-.11-.71-.29q-.135-.15-.21-.33A1 1 0 0 1 15 16c0-.26.11-.52.29-.71l.15-.12q.09-.06.18-.09.09-.045.18-.06c.33-.***********.***********.44.29.71 0 .13-.03.26-.08.38-.05.13-.12.23-.21.33-.19.18-.45.29-.71.29"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineLockIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
