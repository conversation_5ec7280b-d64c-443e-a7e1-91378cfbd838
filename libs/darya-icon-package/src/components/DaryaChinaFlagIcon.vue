<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M0 .923h32v22.154H0z"></path><path fill-rule="evenodd" d="m10.343 10.726-.71.188.44-.588-.398-.617.695.237.464-.57-.01.735.685.265-.702.217-.04.733zm2.17-2.008-.603.42.213-.704-.585-.443.734-.015.24-.694.242.694.734.015-.585.443.213.703-.603-.42Zm.228-3.194-.38.628-.091-.729-.716-.167.665-.312-.062-.732.503.535.677-.285-.355.644.48.556-.72-.138ZM10.33 3.46l-.716.162.46-.572-.376-.63.686.26.484-.552-.036.733.675.29-.709.192-.066.731-.402-.615Zm-5.1 3.925L3.423 8.643l.638-2.109-1.756-1.331 2.203-.045.724-2.081.723 2.081 2.203.045-1.756 1.331.638 2.11-1.808-1.26Z" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaChinaFlagIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
