<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M15.97 22.75h-8c-3.42 0-4.75-1.33-4.75-4.75v-8c0-.41.34-.75.75-.75h16c.41 0 .75.34.75.75v8c0 3.42-1.33 4.75-4.75 4.75m-11.25-12V18c0 2.58.67 3.25 3.25 3.25h8c2.58 0 3.25-.67 3.25-3.25v-7.25z"></path><path d="M19.5 10.75h-15c-1.75 0-2.75-1-2.75-2.75V7c0-1.75 1-2.75 2.75-2.75h15c1.7 0 2.75 1.05 2.75 2.75v1c0 1.7-1.05 2.75-2.75 2.75m-15-5c-.91 0-1.25.34-1.25 1.25v1c0 .91.34 1.25 1.25 1.25h15c.88 0 1.25-.37 1.25-1.25V7c0-.88-.37-1.25-1.25-1.25z"></path><path d="M11.64 5.75H6.12c-.21 0-.41-.09-.55-.24-.61-.67-.59-1.7.05-2.34l1.42-1.42c.66-.66 1.75-.66 2.41 0l2.72 2.72c.21.21.28.54.16.82-.11.28-.38.46-.69.46m-4.97-1.5h3.17L8.39 2.81a.205.205 0 0 0-.29 0L6.68 4.23c0 .01-.01.01-.01.02"></path><path d="M17.87 5.75h-5.52a.74.74 0 0 1-.69-.46c-.12-.28-.05-.6.16-.82l2.72-2.72c.66-.66 1.75-.66 2.41 0l1.42 1.42c.64.64.67 1.67.05 2.34-.14.15-.34.24-.55.24m-3.7-1.5h3.17l-.02-.02-1.42-1.42a.205.205 0 0 0-.29 0zM9.94 16.9a1.75 1.75 0 0 1-1.75-1.75V10c0-.41.34-.75.75-.75h6.04c.41 0 .75.34.75.75v5.13c0 .65-.35 1.24-.92 1.54-.57.31-1.26.27-1.8-.09l-.89-.6a.24.24 0 0 0-.28 0l-.94.62c-.29.2-.63.3-.96.3m-.25-6.15v4.39c0 .***********.22s.15.06.26-.01l.94-.62c.59-.39 1.35-.39 1.93 0l.89.6c.***********.26.01s.13-.09.13-.22v-4.38H9.69z"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineGiftIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
