<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M14.67 6.277c.*********** 0 1.34L10.287 12l4.383 4.383a.947.947 0 1 1-1.34 1.34L8.277 12.67a.947.947 0 0 1 0-1.34l5.053-5.053c.37-.37.97-.37 1.34 0" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaArrowLeftIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
