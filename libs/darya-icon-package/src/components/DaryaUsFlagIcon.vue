<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path d="M0 .666h32V22H0z"></path><path d="M0 .666h32v1.64H0V.667Zm0 3.281h32v1.64H0zm0 3.281h32V8.87H0V7.23Zm0 3.281h32v1.641H0zm0 3.288h32v1.64H0zm0 3.281h32v1.64H0zm0 3.281h32v1.64H0z"></path><path d="M0 .666h16V12.15H0z"></path><path d="m2.982 9.33-.25-.798-.274.799h-.823l.667.48-.25.799.68-.493.662.493-.256-.799.68-.48zm3.512 0-.256-.798-.262.799h-.823l.667.48-.25.799.668-.493.674.493-.25-.799.668-.48zm3.524 0-.268-.798-.25.799h-.84l.686.48-.262.799.667-.493.686.493-.262-.799.668-.48zm3.506 0-.25-.798-.261.799h-.83l.674.48-.25.799.668-.493.673.493-.268-.799.686-.48zM6.238 5.365l-.262.798h-.823l.667.493-.25.786.668-.487.674.487-.25-.786.668-.493h-.836zm-3.505 0-.275.798h-.823l.667.493-.25.786.68-.487.662.487-.256-.786.68-.493h-.836zm7.017 0-.25.798h-.84l.686.493-.262.786.667-.487.686.487-.262-.786.668-.493h-.824zm3.524 0-.261.798h-.83l.674.493-.25.786.668-.487.673.487-.268-.786.686-.493h-.842zM2.733 2.207l-.275.786h-.823l.667.493-.25.792.68-.493.662.493-.256-.792.68-.493h-.836zm3.505 0-.262.786h-.823l.667.493-.25.792.668-.493.674.493-.25-.792.668-.493h-.836zm3.512 0-.25.786h-.84l.686.493-.262.792.667-.493.686.493-.262-.792.668-.493h-.824zm3.524 0-.261.786h-.83l.674.493-.25.792.668-.493.673.493-.268-.792.686-.493h-.842z"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 .333h32v21.333H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaUsFlagIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
