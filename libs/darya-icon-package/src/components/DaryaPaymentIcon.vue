<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M2.25 9A.75.75 0 0 1 3 8.25h18a.75.75 0 0 1 0 1.5H3A.75.75 0 0 1 2.25 9" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M6 5.75A2.25 2.25 0 0 0 3.75 8v8A2.25 2.25 0 0 0 6 18.25h5a.75.75 0 0 1 0 1.5H6A3.75 3.75 0 0 1 2.25 16V8A3.75 3.75 0 0 1 6 4.25h12A3.75 3.75 0 0 1 21.75 8v5a.75.75 0 0 1-1.5 0V8A2.25 2.25 0 0 0 18 5.75z" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M14.25 16.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 0 1.5h-3a.75.75 0 0 1-.75-.75m0 3a.75.75 0 0 1 .75-.75h6a.75.75 0 0 1 0 1.5h-6a.75.75 0 0 1-.75-.75M5.75 13a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 0 1.5H6.5a.75.75 0 0 1-.75-.75" clip-rule="evenodd"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaPaymentIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
