<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#darya-icon__a)"><path d="M0 .333h32v21.334H0z"></path><path d="M18 .333h-4V9H0v4h14v8.666h4V13h14V9H18z"></path><path d="M24.612 14.71 32 18.815V14.71zm-5.133 0L32 21.667V19.7l-8.98-4.99zm9.186 6.957-9.186-5.104v5.104z"></path><path d="M19.479 14.71 32 21.667V19.7l-8.98-4.99z"></path><path d="M19.479 14.71 32 21.667V19.7l-8.98-4.99zm-13.833 0L0 17.845V14.71zm6.876.884v6.072H1.593zM8.98 14.71 0 19.7v1.967l12.522-6.957zM7.388 7.29 0 3.185V7.29zm5.134 0L0 .333V2.3l8.98 4.99zM3.335.333l9.187 5.104V.333z"></path><path d="M12.522 7.29 0 .333V2.3l8.98 4.99z"></path><path d="M12.522 7.29 0 .333V2.3l8.98 4.99zm13.832 0L32 4.153V7.29zm-6.875-.885V.333h10.928L19.48 6.405Zm3.541.885L32 2.3V.333L19.48 7.29z"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 .333h32v21.333H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaGbFlagIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
