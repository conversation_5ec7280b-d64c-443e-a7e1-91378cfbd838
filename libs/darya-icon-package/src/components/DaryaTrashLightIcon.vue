<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <g fill-rule="evenodd" clip-path="url(#darya-icon__a)" clip-rule="evenodd"><path d="M4.697 5.863a.75.75 0 0 1 .55-.24h13.506a.75.75 0 0 1 .747.808l-.965 12.552a3 3 0 0 1-2.992 2.771H8.457a3 3 0 0 1-2.992-2.77L4.499 6.43a.75.75 0 0 1 .198-.568zm10.846 14.391a1.5 1.5 0 0 0 1.496-1.386l.904-11.745H6.057l.903 11.745a1.5 1.5 0 0 0 1.497 1.386z"></path><path d="M3.247 6.373a.75.75 0 0 1 .75-.75h16.007a.75.75 0 0 1 0 1.5H3.997a.75.75 0 0 1-.75-.75"></path><path d="M9.186 3.746a.375.375 0 0 0-.375.376v1.5h6.378v-1.5a.375.375 0 0 0-.376-.376zm-1.326-.95c.352-.352.829-.55 1.326-.55h5.627c1.036 0 1.876.84 1.876 1.876v2.25a.75.75 0 0 1-.75.75H8.06a.75.75 0 0 1-.75-.75v-2.25c0-.498.197-.975.549-1.327zm1.389 14.206a.75.75 0 0 1 .75-.75h4.002a.75.75 0 1 1 0 1.5H9.999a.75.75 0 0 1-.75-.75"></path></g><defs><clipPath id="darya-icon__a"><path d="M0 0h24v24H0z"></path></clipPath></defs>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaTrashLightIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
