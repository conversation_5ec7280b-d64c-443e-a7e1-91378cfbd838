<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M9.25 18.958h-3.5c-3.259 0-4.709-1.45-4.709-4.708v-3.5c0-3.258 1.45-4.708 4.708-4.708h3.5c3.259 0 4.709 1.45 4.709 4.708v3.5c0 3.258-1.45 4.708-4.709 4.708M5.75 7.292c-2.584 0-3.459.875-3.459 3.458v3.5c0 2.583.875 3.458 3.458 3.458h3.5c2.584 0 3.459-.875 3.459-3.458v-3.5c0-2.583-.875-3.458-3.459-3.458z"></path><path d="M14.25 13.958h-.917a.63.63 0 0 1-.625-.625V10.75c0-2.583-.875-3.458-3.459-3.458H6.666a.63.63 0 0 1-.625-.625V5.75c0-3.258 1.45-4.708 4.708-4.708h3.5c3.259 0 4.709 1.45 4.709 4.708v3.5c0 3.258-1.45 4.708-4.709 4.708m-.292-1.25h.291c2.584 0 3.459-.875 3.459-3.458v-3.5c0-2.583-.875-3.458-3.459-3.458h-3.5c-2.583 0-3.458.875-3.458 3.458v.292h1.958c3.259 0 4.709 1.45 4.709 4.708z"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineCopyIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
