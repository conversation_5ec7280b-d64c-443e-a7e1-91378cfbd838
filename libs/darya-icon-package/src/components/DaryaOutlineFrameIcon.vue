<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path d="M12.16 11.62h-.08a.5.5 0 0 0-.18 0C9 11.53 6.81 9.25 6.81 6.44c0-2.86 2.33-5.19 5.19-5.19s5.19 2.33 5.19 5.19c-.01 2.81-2.21 5.09-5 5.18zM12 2.75a3.7 3.7 0 0 0-3.69 3.69c0 2 1.56 3.61 3.55 3.68.05-.01.19-.01.32 0 1.96-.09 3.5-1.7 3.51-3.68A3.7 3.7 0 0 0 12 2.75m.17 19.8c-1.96 0-3.93-.5-5.42-1.5-1.39-.92-2.15-2.18-2.15-3.55s.76-2.64 2.15-3.57c3-1.99 7.86-1.99 10.84 0 1.38.92 2.15 2.18 2.15 3.55s-.76 2.64-2.15 3.57c-1.5 1-3.46 1.5-5.42 1.5m-4.59-7.36c-.96.64-1.48 1.46-1.48 2.32 0 .85.53 1.67 1.48 2.3 2.49 1.67 6.69 1.67 9.18 0 .96-.64 1.48-1.46 1.48-2.32 0-.85-.53-1.67-1.48-2.3-2.49-1.66-6.69-1.66-9.18 0"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaOutlineFrameIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
