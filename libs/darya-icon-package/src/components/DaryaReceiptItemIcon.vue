<template>
  <svg
    :width="size"
    :height="size"
    :fill="color"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" d="M9.47 8.97a.75.75 0 0 1 1.06 0l1.5 1.5a.75.75 0 1 1-1.06 1.06l-1.5-1.5a.75.75 0 0 1 0-1.06" clip-rule="evenodd"></path><path fill-rule="evenodd" d="M14.53 7.97a.75.75 0 0 1 0 1.06l-2.5 2.5a.75.75 0 1 1-1.06-1.06l2.5-2.5a.75.75 0 0 1 1.06 0M7.25 15a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75" clip-rule="evenodd"></path><path d="M9.1 21.5c-.3 0-.5-.1-.8-.2l-1.8-.9h-.2l-.8.4c-.5.3-1.2.2-1.7-.1s-.8-.9-.8-1.5V4.8C3 3.3 4.2 2 5.8 2h12c1.5 0 2.8 1.2 2.8 2.8v14.5c0 .6-.3 1.2-.8 1.5s-1.2.4-1.7.1l-.8-.4h-.2l-1.8.9c-.5.2-1.1.2-1.6 0l-1.8-.9h-.2l-1.8.9c-.2.1-.5.1-.8.1m2.7-2.5c.3 0 .5.1.8.2l1.8.9h.2l1.8-.9c.5-.2 1.1-.2 1.6 0l.8.4c.1.1.2 0 .2 0s.1-.1.1-.2V4.8c0-.7-.6-1.2-1.2-1.2H5.8c-.7 0-1.2.5-1.2 1.2v14.5c0 .*******.2s.1.1.2 0l.8-.4c.5-.2 1.1-.2 1.6 0l1.8.9h.2l1.8-.9c.2-.1.4-.1.7-.1"></path>
  </svg>
</template>

<script>
  export default {
    name: 'DaryaReceiptItemIcon',
    props: {
      size: {
        type: [Number, String],
        default: 24
      },
      color: {
        type: String,
        default: "currentColor"
      }
    }
  }
</script>
