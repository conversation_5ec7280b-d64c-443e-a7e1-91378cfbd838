import fs from "fs";
import path from "path";
import mustache from "mustache";

interface Args {
  input: string[];
  output: string;
  prefix: string;
}

// Read the template files
const componentTemplate = fs.readFileSync(
  path.join(__dirname, "./templates/component.mustache"),
  { encoding: "utf8" },
);
const buildTemplate = fs.readFileSync(
  path.join(__dirname, "../templates/build.mustache"),
  { encoding: "utf8" },
);

// Suppose `icons` is an object where each key is the icons name (without prefix/postfix) and value contains the icons data (e.g. { path: string }).
const icons = {
  Example: { path: '<path d="M12 2L2 22h20z" />' },
  // ...other icons
};

// Assume argv contains your CLI arguments for prefix and postfix.
const argv = {
  prefix: "Icon",
  postfix: "",
  output: "",
};

// Render each component template
const content = Object.entries(icons)
  .map(([name, icon]) => {
    return mustache.render(componentTemplate, {
      path: icon.path,
      name: `${argv.prefix}${name}${argv.postfix}`,
    });
  })
  .join("\n\n");

// Render the final build file
const output = mustache.render(buildTemplate, { content });

// Write to file or output to stdout
if (argv.output) {
  fs.writeFileSync(argv.output, output);
} else {
  process.stdout.write(output);
}
