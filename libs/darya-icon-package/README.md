# Darya Icon Package

**@libs/darya-icon-package** is a Vue 3 icon library that dynamically parses and optimizes SVG files into Vue components. It provides a simple CLI for generating icon components with customizable names, and an easy-to-use plugin for global registration in your Vue application.

## Features

- **Dynamic SVG Parsing:** Automatically converts SVG files into Vue components.
- **Optimized SVGs:** Uses [SVGO](https://github.com/svg/svgo) to optimize SVG files.
- **Customizable Naming:** Easily configure component name prefixes and postfixes.
- **Plugin-Based:** Install the entire icon set as a Vue plugin for global registration.
- **Tree-Shakeable:** Import individual icons if you prefer granular usage.

## Installation

Install the package via your preferred package manager. For example, with pnpm:

```bash
pnpm install @libs/darya-icon-package
```

##  Generate Icon Components
Before using the icons, generate the Vue component files and icon metadata from your SVG files.

Run the generate script:

```bash
first : pnpm run generate-icons
secound: pnpm run build
```