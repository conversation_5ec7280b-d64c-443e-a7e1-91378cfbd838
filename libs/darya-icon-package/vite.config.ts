import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

export default defineConfig({
  plugins: [vue()],
  build: {
    lib: {
      entry: path.resolve(__dirname, "src/index.ts"),
      name: "VueIconPackage",
      fileName: (format) => `darya-icon-package.${format}.js`,
    },
    rollupOptions: {
      // Exclude Vue from your bundle if it will be provided by the consumer
      external: ["vue"],
      output: {
        globals: {
          vue: "Vue",
        },
      },
    },
  },
});
