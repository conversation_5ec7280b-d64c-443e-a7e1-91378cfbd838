{"name": "@libs/darya-icon-package", "version": "1.0.0", "type": "module", "description": "A Vue 3 icon package that dynamically parses SVG files into Vue components", "main": "dist/index.js", "bin": {"vue-icon-generator": "dist/cli.js"}, "scripts": {"dev": "vite", "build": "vite build && tsc", "generate-icons": "tsx src/cli.ts --input 'svgs/**/*.svg' --jsonOutput 'build/icons.json' --componentsOutput 'src/components' --prefix Darya --postfix Icon"}, "exports": {".": {"import": "./dist/darya-icon-package.es.js", "require": "./dist/darya-icon-package.umd.js"}}, "files": ["dist"], "dependencies": {"fs-extra": "^11.3.0", "mustache": "^4.2.0", "trim-newlines": "^5.0.0", "vue": "^3.2.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/lodash.merge": "^4.6.9", "@types/mustache": "^4.2.5", "@types/node": "^22.13.4", "@types/pascalcase": "^1.0.3", "@types/yargs": "^17.0.33", "@vitejs/plugin-vue": "^4.0.0", "cheerio": "^1.0.0", "globby": "^13.2.2", "lodash.merge": "^4.6.2", "pascalcase": "^1.0.0", "svgo": "^3.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^4.8.0", "vite": "^4.0.0", "yargs": "^17.7.2"}}