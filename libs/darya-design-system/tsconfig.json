{"compilerOptions": {"noImplicitAny": false, "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node16", "lib": ["ESNext", "DOM"], "types": ["vite/client"], "jsx": "preserve", "jsxImportSource": "vue", "esModuleInterop": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "outDir": "dist", "resolveJsonModule": true, "sourceMap": true, "baseUrl": ".", "paths": {"*": ["node_modules/*", "libs/design-system/src/*"], "@/*": ["./src/*"]}}, "typeRoots": ["./node_modules/@types", "./node_modules/@tailwindcss/vite/dist"], "include": ["src", "svgs", "vite-env.d.ts"]}