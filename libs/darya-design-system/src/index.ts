import type { App, Plugin } from "vue";
import "./style.css";

import DBox from "./components/Box.vue";
import DButton from "./components/Button/index.vue";
import DDialog from "./components/Dialog.vue";
import DInput from "./components/Input/src/Input.vue";
import DPopper from "./components/Popper/src/Popper.vue";
import DAlert from "./components/Toast/src/Alert.vue";
import DToast from "./components/Toast/src/Toast.vue";
import DSearchBox from "./components/SearchBox.vue";
import DBaseTable from "./components/BaseTable.vue";
import DChip from "@/components/Chip/index.vue";
import { useToast } from "./components/Toast/useToast";
import DField from "./components/Field/src/Field.vue";
import DCheckbox from "@/components/CheckBox/src/CheckBox.vue";
import DCheckboxGroup from "@/components/CheckBox/src/CheckBoxGroup.vue";
import DSelectBox from "@/components/SelectBox.vue";
import DTab from "@/components/Tab.vue";
import DSwitch from "@/components/Switch/src/Switch.vue";
import DRadio from "@/components/radio/Radio/src/Radio.vue";
import DRadioGroup from "@/components/radio/RadioGroup/src/RadioGroup.vue";
import DLoading from "@/components/Loading.vue";

export {
  DBox,
  DButton,
  DDialog,
  DInput,
  DPopper,
  DAlert,
  DToast,
  DSearchBox,
  DBaseTable,
  DChip,
  DField,
  DCheckbox,
  DCheckboxGroup,
  DSelectBox,
  DTab,
  DSwitch,
  DRadio,
  DRadioGroup,
  DLoading,
};

const DaryaDesignSystem: Plugin = {
  install: (app: App) => {
    app.component("DBox", DBox);
    app.component("DButton", DButton);
    app.component("DDialog", DDialog);
    app.component("DInput", DInput);
    app.component("DPopper", DPopper);
    app.component("DAlert", DAlert);
    app.component("DToast", DToast);
    app.component("DSearchBox", DSearchBox);
    app.component("DBaseTable", DBaseTable);
    app.component("DChip", DChip);
    app.component("DField", DField);
    app.component("DCheckbox", DCheckbox);
    app.component("DCheckboxGroup", DCheckboxGroup);
    app.component("DSelectBox", DSelectBox);
    app.component("DTab", DTab);
    app.component("DSwitch", DSwitch);
    app.component("DRadio", DRadio);
    app.component("DRadioGroup", DRadioGroup);
    app.component("DLoading", DLoading);
  },
};

export { useToast };
export default DaryaDesignSystem;
