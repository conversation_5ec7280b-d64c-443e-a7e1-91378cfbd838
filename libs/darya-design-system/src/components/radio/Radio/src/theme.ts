const RADIO_BASE = "self-center w-5 h-5 rounded-full flex-shrink-0 relative";

const RADIO = {
  normal:
    "transition duration-150 ease-in-out border border-neutral-10 bg-white hover:border-primary-shade1 focus-within:border-primary-shade1 ",
  selected:
    'transition duration-150 ease-in-out border border-primary bg-white  focus-within:ring-1 focus-within:ring-white after:content-[""] after:absolute after:top-1/2 after:left-1/2 after:w-2 after:h-2 after:bg-primary-shade1 after:rounded-full after:transform after:-translate-x-1/2 after:-translate-y-1/2',
  disabled:
    "border border-neutral-10 bg-neutral-13 cursor-not-allowed opacity-60",
  disabledSelected:
    'border border-neutral-10 bg-neutral-13 cursor-not-allowed opacity-60 after:content-[""] after:absolute after:top-1/2 after:left-1/2 after:w-2 after:h-2 after:bg-neutral-11 after:rounded-full after:transform after:-translate-x-1/2 after:-translate-y-1/2',
  invalid:
    "border border-error bg-white hover:border-error-tint1 focus-within:border-error focus-within:ring-2 focus-within:ring-error-tint3",
};

const LABEL_BASE = "group cursor-pointer flex items-center gap-2";
const LABEL = {
  horizontal: "inline-flex items-center me-4",
  vertical: "flex mb-4 last:mb-0",
  normal: "text-neutral-2 hover:text-neutral-1",
  disabled: "text-neutral-7 cursor-not-allowed",
  invalid: "text-error",
};

export { RADIO_BASE, RADIO, LABEL_BASE, LABEL };
