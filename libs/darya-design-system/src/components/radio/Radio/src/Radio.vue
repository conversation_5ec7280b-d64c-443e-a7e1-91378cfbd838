<template>
  <label
    :class="classes.label"
    :aria-disabled="isDisabled"
    role="radio"
    :aria-checked="isSelected"
    :tabindex="isDisabled ? -1 : 0"
    @keydown="handleKeydown"
  >
    <div :class="classes.customRadioInput">
      <input
        v-model="model"
        :value="value"
        :name="name"
        :disabled="isDisabled"
        tabindex="-1"
        aria-hidden="true"
        type="radio"
        class="sr-only"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </div>

    <div class="flex-1 min-w-0">
      <slot>{{ label || value }}</slot>
    </div>
  </label>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({ name: "Radio" });
</script>

<script lang="ts" setup>
import { computed, ref, withDefaults } from "vue";

import { RADIO_BASE, RADIO, LABEL_BASE, LABEL } from "./theme";

import useRadio from "../../useRadio";

type RadioProps = {
  modelValue?: string | number | boolean;
  value: string | number | boolean;
  name?: string;
  label?: string;
  disabled?: boolean;
  error?: string;
};

const props = withDefaults(defineProps<RadioProps>(), {
  modelValue: "",
  name: "",
  label: "",
  disabled: false,
  error: "",
});

const emit = defineEmits(["update:modelValue"]);

const { model, isDisabled, isSelected, tabIndex, flow } = useRadio(props, emit);

const isFocused = ref(false);

const handleFocus = () => {
  isFocused.value = true;
};

const handleBlur = () => {
  isFocused.value = false;
};

const handleKeydown = (event: KeyboardEvent) => {
  if (isDisabled.value) return;

  if (event.key === ' ' || event.key === 'Enter') {
    event.preventDefault();
    model.value = props.value;
  }
};

const classes = computed(() => {
  const isInvalid = props.error && !isDisabled.value && !isSelected.value;

  const customRadioInput = [
    RADIO_BASE,
    {
      [RADIO.normal]: !isDisabled.value && !isSelected.value && !isInvalid,
      [RADIO.selected]: isSelected.value && !isDisabled.value,
      [RADIO.disabled]: isDisabled.value && !isSelected.value,
      [RADIO.disabledSelected]: isDisabled.value && isSelected.value,
      [RADIO.invalid]: isInvalid,
    },
  ];

  const label = [
    LABEL_BASE,
    LABEL[flow.value],
    {
      [LABEL.normal]: !props.error && !isDisabled.value,
      [LABEL.disabled]: isDisabled.value,
      [LABEL.invalid]: isInvalid,
    },
  ];

  return {
    customRadioInput,
    label,
  };
});
</script>
