import { computed, inject } from 'vue';
import type { InjectionKey, Ref } from 'vue';

type RadioContextType = {
  handleUpdate(value: any): void;
  modelValue: Ref<string | number | boolean>;
  flow: Ref<string>;
  disabled: Ref<boolean>;
};

export const RadioContextKey: InjectionKey<RadioContextType> =
  Symbol('RadioContext');

export default function useRadio(props, emit) {
  const radioGroup = inject(RadioContextKey);

  const model = computed({
    get() {
      return radioGroup?.modelValue.value ?? props.modelValue;
    },
    set(value) {
      if (radioGroup) {
        radioGroup.handleUpdate(value);
      } else {
        emit('update:modelValue', value);
      }
    },
  });

  const flow = computed(() => radioGroup?.flow.value || 'horizontal');
  const isSelected = computed(() => model.value === props.value);
  const isDisabled = computed(
    () => props.disabled || radioGroup?.disabled.value
  );

  const tabIndex = computed(() => (isDisabled.value ? -1 : 0));

  return {
    flow,
    model,
    isSelected,
    isDisabled,
    tabIndex,
  };
}
