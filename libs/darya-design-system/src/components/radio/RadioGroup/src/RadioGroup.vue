<template>
  <DField
    v-bind="{
      error,
      label,
      required,
      requiredSign,
      showError: !!error,
    }"
    role="radiogroup"
  >
    <template #input>
      <slot />
    </template>
  </DField>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({ name: "RadioGroup" });
</script>

<script lang="ts" setup>
import { provide, toRefs, withDefaults } from "vue";

import DField from "@/components/Field/src/Field.vue";

import { RadioContextKey } from "../../useRadio";

type RadioGroupProps = {
  modelValue?: string | number | boolean;
  flow?: "vertical" | "horizontal";
  label?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  requiredSign?: boolean;
};

const props = withDefaults(defineProps<RadioGroupProps>(), {
  modelValue: "",
  flow: "horizontal",
  label: "",
  error: "",
  disabled: false,
  required: false,
  requiredSign: true,
});

const emit = defineEmits(["update:modelValue"]);

const handleUpdate = (value) => emit("update:modelValue", value);

provide(RadioContextKey, { ...toRefs(props), handleUpdate });
</script>
