import { h, withDirectives, vShow, type Slots, type VNode } from "vue";
import { ARROW_PLACEMENT_CLASS, ARROW_BASE_CLASS } from "../theme";

interface RenderPopperOptions {
  arrow: boolean;
  arrowClass?: string | string[];
  arrowRightClass?: string | string[];
  arrowLeftClass?: string | string[];
  arrowTopClass?: string | string[];
  arrowBottomClass?: string | string[];
  disabled: boolean;
  isOpen: boolean;
  arrowPlacement: string;
  wrapperClass?: string;
  $slots: Slots;
}

export default function renderPopper(options: RenderPopperOptions): VNode {
  const {
    arrow,
    arrowClass = "",
    arrowRightClass = "",
    arrowLeftClass = "",
    arrowTopClass = "",
    arrowBottomClass = "",
    disabled,
    isOpen,
    arrowPlacement,
    wrapperClass = "",
    $slots,
  } = options;

  const placement = arrowPlacement.split("-")[0] as
    | "right"
    | "left"
    | "top"
    | "bottom";

  const arrowPlacementCustomClass = {
    right: arrowRightClass,
    left: arrowLeftClass,
    top: arrowTopClass,
    bottom: arrowBottomClass,
  }[placement];

  const arrowPlacementClass = ARROW_PLACEMENT_CLASS[placement];

  return withDirectives(
    h(
      "div",
      {
        ref: "popperElement",
        class: `z-180 ${wrapperClass}`,
      },
      [
        $slots.content?.(),
        arrow &&
          h("div", {
            class: [
              ARROW_BASE_CLASS,
              arrowPlacementClass,
              arrowPlacementCustomClass,
              arrowClass,
            ].join(" "),
            "data-popper-arrow": "",
          }),
      ].filter(Boolean),
    ),
    [[vShow, !disabled && isOpen]],
  );
}
