import { Fragment, cloneVNode } from "vue";

export default function renderReferenceElement(slot, extraProps) {
  const firstNode = getFirstValidNode(slot);

  return cloneVNode(firstNode, extraProps, true);
}

function getChildren(node) {
  if (isFragment(node)) {
    return getFirstValidNode(node.children);
  }

  return node;
}

function getFirstValidNode(nodes) {
  if (Array.isArray(nodes)) {
    return getChildren(nodes[0]);
  }

  return getChildren(nodes);
}

const isFragment = (node) => node.type === Fragment;
