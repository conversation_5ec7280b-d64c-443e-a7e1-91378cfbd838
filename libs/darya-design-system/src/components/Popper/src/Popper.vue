<script>
import { toRefs, watch, Fragment, Teleport, h, defineComponent } from "vue";

import renderReferenceElement from "./renderers/reference";
import renderPopperElement from "./renderers/popper";

import { PLACEMENTS } from "../constants";
import usePopper from "../usePopper";

export default defineComponent({
  name: "DPopper",

  inheritAttrs: false,

  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },

    placement: {
      type: String,
      default: "bottom",
      validator(value) {
        return PLACEMENTS.includes(value);
      },
    },

    offsetX: {
      type: [String, Number],
      default: 0,
    },

    offsetY: {
      type: [String, Number],
      default: 0,
    },

    hover: {
      type: Boolean,
      default: false,
    },

    arrow: {
      type: Boolean,
      default: false,
    },

    arrowClass: {
      type: [String, Array],
      default: "before:bg-white",
    },

    arrowRightClass: {
      type: [String, Array],
      default: "",
    },

    arrowLeftClass: {
      type: [String, Array],
      default: "",
    },

    arrowTopClass: {
      type: [String, Array],
      default: "",
    },

    arrowBottomClass: {
      type: [String, Array],
      default: "",
    },

    disabled: {
      type: Boolean,
      default: false,
    },

    appendToBody: {
      type: Boolean,
      default: false,
    },

    sameWidth: {
      type: Boolean,
      default: false,
    },

    wrapperClass: {
      type: String,
      default: "",
    },
    clickPersist: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["update:modelValue", "update"],

  setup(props, { emit }) {
    const {
      modelValue,
      placement,
      offsetX,
      offsetY,
      hover,
      disabled,
      sameWidth,
      clickPersist,
    } = toRefs(props);

    const {
      referenceElement,
      arrowPlacement,
      popperElement,
      isOpen,
      toggle,
      onStateChange,
    } = usePopper(modelValue, {
      placement,
      offsetX,
      offsetY,
      hover,
      sameWidth,
    });

    watch(isOpen, (newValue) => {
      emit("update:modelValue", newValue);
    });

    onStateChange((state) => {
      emit("update", state);
    });

    const handleOnClick = () => {
      if (!disabled.value && !hover.value) {
        return clickPersist.value ? toggle(true) : toggle();
      }
    };

    const handleOnMouseEnter = () => {
      return !disabled.value && hover.value && toggle(true);
    };

    const handleOnMouseLeave = () => {
      return !disabled.value && hover.value && toggle(false);
    };

    return {
      referenceElement,
      popperElement,
      isOpen,
      arrowPlacement,
      handleOnClick,
      handleOnMouseEnter,
      handleOnMouseLeave,
    };
  },

  render() {
    const {
      $slots,
      appendToBody,
      handleOnClick,
      handleOnMouseEnter,
      handleOnMouseLeave,
    } = this;

    const referenceElement = renderReferenceElement($slots.default(), {
      ref: "referenceElement",
      onclick: handleOnClick,
      onmouseenter: handleOnMouseEnter,
      onmouseleave: handleOnMouseLeave,
    });

    const popperElement = renderPopperElement(this);

    return h(Fragment, null, [
      referenceElement,
      h(Teleport, { to: "body", disabled: !appendToBody }, [popperElement]),
    ]);
  },
});
</script>
