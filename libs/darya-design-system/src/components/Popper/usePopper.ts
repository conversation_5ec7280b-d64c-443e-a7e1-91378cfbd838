import {
  ref,
  watch,
  computed,
  nextTick,
  onMounted,
  onBeforeUnmount,
  unref,
  isRef,
  type MaybeRef,
  type ComponentPublicInstance,
  type ComputedRef,
} from 'vue';
import { createPopper } from '@popperjs/core';
import type { Instance, Placement, ModifierArguments } from '@popperjs/core';
import { onClickOutside, useToggle, createEventHook } from '@vueuse/core';

export interface UsePopperOptions {
  placement: MaybeRef<Placement>;
  offsetX: MaybeRef<number>;
  offsetY: MaybeRef<number>;
  hover: MaybeRef<boolean>;
  sameWidth: MaybeRef<boolean>;
}

export default function usePopper(
  initialValue: MaybeRef<boolean>,
  options: UsePopperOptions
) {
  const { placement, offsetX, offsetY, hover, sameWidth } = options;

  const stateChangeHook = createEventHook<ModifierArguments<any>['state']>();

  const isOpen = ref(unref(initialValue));
  const toggle = useToggle(isOpen);

  // Sync with external initialValue
  watch(
    () => unref(initialValue),
    (val) => {
      isOpen.value = val;
    }
  );

  watch(isOpen, (val) => {
    if (isRef(initialValue)) {
      initialValue.value = val;
    }
  });

  const referenceElement = ref<ComponentPublicInstance | HTMLElement | null>(
    null
  );
  const popperElement = ref<ComponentPublicInstance | HTMLElement | null>(null);
  const arrowPlacement = ref<Placement>(unref(placement));

  const reference = computed<HTMLElement | null>(() => {
    const el = referenceElement.value;
    return el && '$el' in el ? (el.$el as HTMLElement) : (el as HTMLElement);
  });

  const popper = computed<HTMLElement | null>(() => {
    const el = popperElement.value;
    return el && '$el' in el ? (el.$el as HTMLElement) : (el as HTMLElement);
  });

  onClickOutside(
    reference,
    () => {
      if (!unref(hover) && isOpen.value) {
        toggle(false);
      }
    },
    { ignore: [popper] }
  );

  let popperInstance: Instance | null = null;

  const initPopper = () => {
    const referenceEl = reference.value;
    const popperEl = popper.value;

    if (!referenceEl || !popperEl) return;

    popperInstance = createPopper(referenceEl, popperEl, {
      placement: unref(placement),
      modifiers: [
        {
          name: 'offset',
          options: { offset: [unref(offsetX), unref(offsetY)] },
        },
        {
          name: 'sameWidth',
          enabled: unref(sameWidth),
          phase: 'beforeWrite',
          requires: ['computeStyles'],
          fn: ({ state }: ModifierArguments<any>) => {
            state.styles.popper.width = `${state.rects.reference.width}px`;
          },
        },
        {
          name: 'arrowPlacement',
          enabled: true,
          phase: 'main',
          fn: ({ state }: ModifierArguments<any>) => {
            arrowPlacement.value = state.placement;
            stateChangeHook.trigger(state);
          },
        },
      ],
    });

    popperInstance.update();
  };

  watch(
    [isOpen, () => unref(placement)],
    async ([newIsOpen]) => {
      if (newIsOpen) {
        await nextTick();
        if (popperInstance) {
          popperInstance.update();
        } else {
          initPopper();
        }
      }
    },
    { flush: 'post' }
  );

  onMounted(() => {
    if (isOpen.value) {
      initPopper();
    }
  });

  onBeforeUnmount(() => {
    popperInstance?.destroy();
    popperInstance = null;
  });

  return {
    referenceElement,
    popperElement,
    arrowPlacement,
    isOpen,
    toggle,
    onStateChange: stateChangeHook.on,
  };
}
