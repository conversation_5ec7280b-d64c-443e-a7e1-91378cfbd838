export const inputBaseClasses =
  "w-full bg-transparent placeholder:text-neutral-10  text-sm border border-neutral-9 rounded-lg px-3 py-2 transition duration-300 ease focus:outline-none focus:border-primary hover:border-primary-tint3 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none";

export const inputIconClasses = "pl-10";
export const inputErrorClasses =
  "border-red-500 focus:border-red-500 hover:border-red-500";
export const inputDisabledClasses = "opacity-50 cursor-not-allowed";
