<template>
  <div class="flex flex-col space-y-1" :dir="textDirection">
    <label v-if="label" :for="id" class="text-sm text-gray-700">
      {{ label }}
      <span v-if="required" aria-hidden="true" class="text-red-500">*</span>
    </label>

    <div class="relative flex items-center">
      <!-- Prefix Icon -->
      <component
        v-if="prefixIcon"
        :is="prefixIcon"
        class="absolute h-5 w-5 text-gray-400 transition-colors"
        :class="[
          isRtl ? 'right-3' : 'left-3',
          {
            'text-red-500': hasError,
            'group-hover:text-gray-500': !hasError && !disabled,
            'group-focus:text-blue-500': !hasError && !disabled,
            'text-gray-300': disabled,
          },
        ]"
        aria-hidden="true"
      />

      <input
        v-bind="inputAttrs"
        v-model="internalValue"
        :class="[inputClasses, { 'text-right': isRtl }]"
        @focus="emit('focus', $event)"
        @blur="emit('blur', $event)"
      />

      <!-- Postfix Icon -->
      <component
        v-if="postfixIcon"
        :is="postfixIcon"
        class="absolute h-5 w-5 text-neutral-11 transition-colors"
        :class="[
          isRtl ? 'left-3' : 'right-3',
          {
            'text-red-500': hasError,
            'group-hover:text-gray-500': !hasError && !disabled,
            'group-focus:text-blue-500': !hasError && !disabled,
            'text-gray-300': disabled,
          },
        ]"
        aria-hidden="true"
      />
    </div>

    <span
      v-if="hasError"
      :id="errorId"
      class="text-sm text-red-500"
      role="alert"
      aria-live="polite"
    >
      {{ error }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed, useAttrs } from "vue";
import { useUniqueId } from "../useUniqueId";
import {
  inputBaseClasses,
  inputIconClasses,
  inputErrorClasses,
  inputDisabledClasses,
} from "./theme";

type InputType = "text" | "email" | "password" | "tel" | "number";

type TInputProps = {
  modelValue?: string | number;
  label?: string;
  placeholder?: string;
  type?: InputType;
  prefixIcon?: any;
  postfixIcon?: any;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  min?: number;
  max?: number;
  autofocus?: boolean;
  direction?: "ltr" | "rtl";
};

const props = withDefaults(defineProps<TInputProps>(), {
  type: "text",
  autofocus: false,
  direction: "ltr",
});

const emit = defineEmits<{
  (e: "update:modelValue", value: string | number): void;
  (e: "focus", event: FocusEvent): void;
  (e: "blur", event: FocusEvent): void;
}>();

const attrs = useAttrs();
const id = useUniqueId("input");
const errorId = useUniqueId("error");

const hasError = computed(() => !!props.error);

const textDirection = computed(() => props.direction || "ltr");
const isRtl = computed(() => props.direction === "rtl");

const inputClasses = computed(() => [
  inputBaseClasses,
  props.prefixIcon && (isRtl.value ? "pr-10" : "pl-10"), // Adjust padding for prefixIcon
  props.postfixIcon && (isRtl.value ? "pl-10" : "pr-10"), // Adjust padding for postfixIcon
  hasError.value && inputErrorClasses,
  props.disabled && inputDisabledClasses,
  "group",
]);

const internalValue = computed({
  get() {
    return props.modelValue !== undefined ? props.modelValue : attrs.value;
  },
  set(value: string | number) {
    emit("update:modelValue", value);
  },
});

const inputAttrs = computed(() => ({
  ...attrs,
  id: id.value,
  type: props.type,
  placeholder: props.placeholder,
  required: props.required,
  disabled: props.disabled,
  min: props.type === "number" ? (props.min ?? undefined) : undefined,
  max: props.type === "number" ? (props.max ?? undefined) : undefined,
  autofocus: props.autofocus,
  "aria-invalid": hasError.value,
  "aria-describedby": hasError.value ? errorId.value : undefined,
}));
</script>
