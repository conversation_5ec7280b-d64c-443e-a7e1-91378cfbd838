<template>
  <div class="mb-2">
    <fieldset
      class="w-full"
      :class="{ 'flex items-start': labelPosition === 'inline' }"
      :disabled="disabled"
    >
      <legend
        v-if="label"
        class="mb-0"
        :class="labelPosition === 'inline' ? 'me-2 pt-3' : 'mb-2'"
      >
        <span class="font-medium text-sm inline-block">
          {{ label }}
          <span
            v-if="required && requiredSign"
            class="text-red-500 ms-1"
            aria-hidden="true"
          >
            *
          </span>
        </span>
      </legend>

      <div class="relative w-full">
        <slot
          :field-id="fieldId"
          :aria-describedby="error ? `${fieldId}-error` : undefined"
          :aria-invalid="!!error"
          name="input"
        />

        <div
          v-if="showError && error"
          :id="`${fieldId}-error`"
          aria-live="polite"
        >
          <slot name="error">
            <p class="mt-1 text-sm text-red-500">
              {{ error }}
            </p>
          </slot>
        </div>
      </div>
    </fieldset>
  </div>
</template>

<script setup lang="ts">
import { computed, withDefaults } from "vue";
import { uuid } from "@libs/utils";

interface FieldProps {
  showError?: boolean;
  error?: string;
  label?: string;
  labelPosition?: "top" | "inline";
  required?: boolean;
  requiredSign?: boolean;
  disabled?: boolean;
}

const props = withDefaults(defineProps<FieldProps>(), {
  showError: true,
  labelPosition: "top",
  required: false,
  requiredSign: true,
  error: "",
  label: "",
  disabled: false,
});

const fieldId = computed(() => uuid("input-"));
</script>
