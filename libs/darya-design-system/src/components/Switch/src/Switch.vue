<template>
  <div class="flex gap-3 items-center">
    <div
      class="relative shrink-0 w-11 h-6"
      :class="{
        'order-2': labelPosition === LabelPosition.Start,
      }"
    >
      <!-- Track/Background -->
      <div
        :class="classes.track"
        class="absolute inset-0 rounded-full transition-all duration-200 ease-in-out"
      ></div>

      <!-- Thumb -->
      <div
        :class="classes.thumb"
        class="absolute top-0.5 left-0.5 transition-all duration-200 ease-in-out"
      ></div>

      <!-- Hidden input -->
      <input
        :id="id"
        :class="classes.input"
        :aria-checked="active"
        :checked="active"
        :aria-disabled="disabled"
        :disabled="disabled || loading"
        role="switch"
        type="checkbox"
        @mouseenter="setHovered(true)"
        @mouseleave="setHovered(false)"
        @input="onInput"
      />
    </div>

    <label
      :for="id"
      :class="[
        'text-c1',
        {
          'order-1': labelPosition === LabelPosition.Start,
        },
      ]"
    >
      <slot>
        {{ label }}
      </slot>
    </label>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({ name: "Switch" });
</script>

<script setup lang="ts">
import { computed, withDefaults, toRefs, unref } from "vue";

import useToggle from "@/composables/useToggle";
import useDependentRef from "@/composables/useDependentRef";

import { LabelPosition } from "@/components/Switch/types";

import uuid from "@/utils/uuid";

import { INPUT_BASE } from "./theme";

type PSwitchProps = {
  modelValue?: boolean;
  disabled?: boolean;
  loading?: boolean;
  label?: string;
  labelPosition?: LabelPosition;
};

type PSwitchEmits = {
  (e: "update:modelValue", value: boolean): boolean;
};

const emit = defineEmits<PSwitchEmits>();

const props = withDefaults(defineProps<PSwitchProps>(), {
  modelValue: undefined,
  disabled: false,
  loading: false,
  label: "",
  labelPosition: LabelPosition.End,
});

const id = uuid("switch-");

const { modelValue } = toRefs(props);

const [hovered, setHovered] = useToggle(false);

const active = useDependentRef(modelValue, Boolean(unref(modelValue)));

const onInput = ({ target }) => {
  emit("update:modelValue", target.checked);
};

const classes = computed(() => ({
  track: [
    "w-full h-full",
    {
      // Inactive states
      "bg-neutral-11": !props.disabled && !active.value,
      "bg-neutral-9": !props.disabled && !active.value && hovered.value,

      // Active states
      "bg-primary": !props.disabled && active.value,
      "bg-primary-tint1": !props.disabled && active.value && hovered.value,

      // Disabled states
      "bg-neutral-12": props.disabled && !active.value,
      "bg-neutral-10": props.disabled && active.value,
    },
  ],
  thumb: [
    "w-5 h-5 bg-white rounded-full shadow-sm",
    {
      // Position based on active state
      "translate-x-5": active.value,
      "translate-x-0": !active.value,

      // Disabled state
      "bg-neutral-13": props.disabled,
    },
  ],
  input: [
    INPUT_BASE,
    {
      "cursor-not-allowed": props.disabled,
      "cursor-wait": props.loading,
    },
  ],
}));
</script>
