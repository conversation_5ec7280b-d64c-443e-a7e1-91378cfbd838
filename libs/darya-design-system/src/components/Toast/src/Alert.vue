<template>
  <div
    class="flex flex-row items-center gap-2 p-4 rounded-lg shadow-md"
    :class="alertClass"
  >
    <span class="flex-1">{{ message }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { alertClasses } from "./theme";

const props = defineProps<{
  type?: "success" | "error" | "warning" | "info";
  message: string;
}>();

const alertClass = computed(() => alertClasses[props.type || "info"]);
</script>
