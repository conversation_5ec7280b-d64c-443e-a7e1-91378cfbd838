export const alertClasses: Record<string, string> = {
  success: "bg-green-500 text-white",
  error: "bg-red-500 text-white",
  warning: "bg-yellow-500 text-black",
  info: "bg-blue-500 text-white",
};

export const toastClasses: Record<string, string> = {
  success: "bg-success-tint3 border border-success-tint2  ",
  error: "bg-error-tint3 border border-error-tint2 ",
  warning: "bg-warning-tint3 border border-warning-tint2  ",
  info: "bg-info-tint3 border border-info-tint2",
  primary: "bg-primary-tint8 border border-primary-tint2",
};

export const positionClasses: Record<string, string> = {
  "right-top": "fixed top-6 end-6 flex flex-col gap-3 items-end",
  "right-bottom": "fixed bottom-6 end-6 flex flex-col gap-3 items-end",
  "left-top": "fixed top-6 start-6 flex flex-col gap-3 items-start",
  "left-bottom": "fixed bottom-6 start-6 flex flex-col gap-3 items-start",
  center:
    "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col gap-3 items-center",
};

export const fadeAnimation = {
  enterActive: "transition-all duration-300 ease-out",
  leaveActive: "transition-all duration-300 ease-in",
  enterFrom: "opacity-0 translate-y-4",
  leaveTo: "opacity-0 translate-y-4",
};
