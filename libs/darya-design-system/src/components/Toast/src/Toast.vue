<template>
  <div class="fixed inset-0 pointer-events-none z-[1000]">
    <div
      v-for="(items, position) in groupedToasts"
      :key="position"
      :class="positionClasses[position]"
    >
      <transition-group
        :enter-active-class="fadeAnimation.enterActive"
        :leave-active-class="fadeAnimation.leaveActive"
        :enter-from-class="fadeAnimation.enterFrom"
        :leave-to-class="fadeAnimation.leaveTo"
      >
        <div
          v-for="toast in items"
          :key="toast.id"
          class="flex items-start p-4 rounded-lg pointer-events-auto max-w-sm w-full"
          :class="toastClasses[toast.type]"
        >
          <!-- Toast Icon -->
          <div class="flex-shrink-0 mr-2">
            <DaryaBoldTickCircleIcon
              v-if="toast.type === 'success'"
              class="text-success"
            />
            <DaryaBoldCloseCircleIcon
              v-else-if="toast.type === 'error'"
              class="text-error"
            />
            <DaryaBoldDangerIcon
              v-else-if="toast.type === 'warning'"
              class="text-warning"
            />
            <DaryaBoldInfoCircleIcon
              v-else-if="toast.type === 'info'"
              class="text-info"
            />
            <DaryaBoldTickCircleIcon
              v-else-if="toast.type === 'primary'"
              class="text-primary"
            />
          </div>

          <!-- Toast Content -->
          <div class="flex-1 min-w-0">
            <!-- Title and Close Button Row -->
            <div class="flex items-start justify-between">
              <h4
                v-if="toast.title"
                class="text-base font-bold text-gray-900 leading-tight"
              >
                {{ toast.title }}
              </h4>
              <h4
                v-else
                class="text-base font-bold text-gray-900 leading-tight"
              >
                {{ getDefaultTitle(toast.type) }}
              </h4>

              <!-- Close Button -->
              <button
                @click="removeToast(toast.id)"
                class="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <DaryaCloseIcon />
              </button>
            </div>

            <!-- Message -->
            <p class="text-xs font-normal text-neutral-4 mt-1 leading-relaxed">
              {{ toast.message }}
            </p>
          </div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useToast, type Toast } from "../useToast";
import { positionClasses, toastClasses, fadeAnimation } from "./theme";

const { toasts, removeToast } = useToast();

const groupedToasts = computed(() => {
  return toasts.value.reduce(
    (acc, toast) => {
      if (!acc[toast.position]) acc[toast.position] = [];
      acc[toast.position].push(toast);
      return acc;
    },
    {} as Record<string, Toast[]>,
  );
});

const getDefaultTitle = (type: string) => {
  const titles = {
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Information",
    primary: "Primary",
  };
  return titles[type as keyof typeof titles] || "Notification";
};
</script>
