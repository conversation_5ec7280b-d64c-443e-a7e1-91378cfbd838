import { ref } from "vue";
import { useTimeoutFn } from "@vueuse/core";

type ToastType = "success" | "error" | "warning" | "info";
type ToastPosition =
  | "right-top"
  | "right-bottom"
  | "left-top"
  | "left-bottom"
  | "center";

export type Toast = {
  id: string;
  title?: string;
  message: string;
  type: ToastType;
  position: ToastPosition;
  duration?: number;
};

const state = ref<Toast[]>([]);

export function useToast() {
  const generateId = () =>
    `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const addToast = (toast: Omit<Toast, "id">) => {
    const id = generateId();
    const defaultDuration = 3000; // 3 seconds default
    const newToast = { id, duration: defaultDuration, ...toast };

    state.value = [...state.value, newToast];

    if (newToast.duration) {
      useTimeoutFn(() => removeToast(id), newToast.duration);
    }

    return id;
  };

  const removeToast = (id: string) => {
    state.value = state.value.filter((t) => t.id !== id);
  };

  const clearToasts = () => {
    state.value = [];
  };

  return {
    toasts: state,
    addToast,
    removeToast,
    clearToasts,
  };
}
