<template>
  <div class="relative w-full" :dir="direction">
    <SearchBox v-if="enableSearch" v-model="searchQuery" placeholder="Search" />

    <div class="overflow-auto">
      <table class="w-full">
        <thead class="bg-lightMood-2">
          <tr>
            <th
              v-for="(column, index) in columns"
              :key="index"
              class="p-4 font-medium text-sm text-center border-b whitespace-nowrap border-neutral-12"
            >
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(row, index) in displayedData"
            :key="row.id"
            class="hover:bg-gray-50 transition border-b border-black/5"
          >
            <td
              v-for="(column, colIndex) in columns"
              :key="colIndex"
              class="p-4 text-neutral-3 font-normal text-sm text-center"
            >
              <slot :name="`column-${column.key}`" :row="row" :index="index">
                {{ row[column.key] }}
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      v-if="enablePagination"
      class="flex justify-end items-center py-5 px-4 gap-2 w-full"
    >
      <!-- Previous Button -->
      <button
        @click="prevPage"
        :disabled="currentPage === 1"
        class="disabled:opacity-50"
      >
        <DaryaOutlineArrowLeftIcon class="rtl:rotate-180" size="12" />
      </button>

      <!-- Page Numbers with Ellipsis -->
      <span
        v-for="page in totalPages"
        :key="page"
        @click="typeof page === 'number' && goToPage(page)"
        class="px-2 py-0.5 text-sm font-normal rounded cursor-pointer"
        :class="{ 'border border-primary': currentPage === page }"
      >
        {{ page }}
      </span>

      <!-- Next Button -->
      <button
        @click="nextPage"
        :disabled="currentPage >= totalPages"
        class="disabled:opacity-50"
      >
        <DaryaOutlineArrowRightIcon class="rtl:rotate-180" size="12" />
      </button>

      <!-- Rows Per Page Selector (no label) -->
      <div v-if="showRowsPerPageSelect" class="flex items-center gap-2">
        <SelectBox
          v-model="localRowsPerPageString"
          :options="
            rowsPerPageOptions.map((n) => ({
              label: `${String(n)} / page`,
              value: String(n),
            }))
          "
        />
      </div>

      <!-- "Go to" Input Field -->
      <div class="flex items-center gap-2">
        <span class="text-sm">Go to</span>
        <DInput
          v-model="selectPage"
          type="number"
          @change="goToPage(selectPage)"
          class="w-11 text-center"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import SearchBox from "@/components/SearchBox.vue";
import SelectBox from "@/components/SelectBox.vue";

type Column = {
  key: string;
  label: string;
};

type Props = {
  columns: Column[];
  data: Record<string, any>[];
  enableSearch?: boolean;
  enablePagination?: boolean;
  rowsPerPage?: number;
  showRowsPerPageSelect?: boolean;
  direction?: "ltr" | "rtl";
};

const props = defineProps<Props>();

const searchQuery = ref("");
const currentPage = ref(1);
const selectPage = ref(null);

const direction = props.direction || "ltr";

const localRowsPerPage = ref(props.rowsPerPage || 10);

const rowsPerPageOptions = [5, 10, 20, 50, 100];

const localRowsPerPageString = computed({
  get: () => localRowsPerPage.value.toString(),
  set: (value: string) => {
    localRowsPerPage.value = parseInt(value, 10);
  },
});

const filteredData = computed(() => {
  if (!props.enableSearch || !searchQuery.value.trim()) {
    return props.data;
  }
  return props.data.filter((row) =>
    Object.values(row).some((value) =>
      String(value).toLowerCase().includes(searchQuery.value.toLowerCase()),
    ),
  );
});

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / localRowsPerPage.value);
});

const displayedData = computed(() => {
  if (!props.enablePagination) return filteredData.value;
  const startIndex = (currentPage.value - 1) * localRowsPerPage.value;
  return filteredData.value.slice(
    startIndex,
    startIndex + localRowsPerPage.value,
  );
});

const prevPage = () => {
  if (currentPage.value > 1) currentPage.value--;
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) currentPage.value++;
};

const goToPage = (page: number) => {
  if (page) currentPage.value = page;
};

watch([() => props.data, () => searchQuery.value, localRowsPerPage], () => {
  currentPage.value = 1;
});
</script>
