import { computed, inject, toRefs, provide, Ref } from "vue";
import type { InjectionKey } from "vue";

import useDependentRef from "@/composables/useDependentRef";
import useCallback from "@/composables/useCallback";

import {
  CheckboxContextType,
  CheckboxGroupFlow,
  CheckboxProviderType,
} from "./types";

export const CHECKBOX_GROUP_CONTEXT_KEY: InjectionKey<CheckboxContextType> =
  Symbol("CheckboxContext");

export function useCheckboxContext(props): CheckboxContextType {
  const checkboxProps = toRefs(props);

  const context = inject(CHECKBOX_GROUP_CONTEXT_KEY, null);

  const disabled = computed(() => props.disabled || context?.disabled?.value);

  const tabIndex = computed(() => (disabled.value ? -1 : 0));

  const onStateChange = useCallback();

  if (!context) {
    const active = useDependentRef(checkboxProps.modelValue, props.value);

    const toggle = () => {
      active.value = !active.value;
      onStateChange.trigger(active.value);
    };

    return {
      active,
      disabled,
      tabIndex,
      toggle,
      isActive: () => {},
      onStateChange: onStateChange.on,
    };
  } else {
    const active = computed(() => context.isActive(props.value));

    return {
      active,
      disabled,
      tabIndex,
      flow: context.flow,
      isActive: context.isActive,
      onStateChange: onStateChange.on,
      toggle: context.toggle,
    };
  }
}

export function useCheckboxProvider(props): CheckboxProviderType {
  const groupProps = toRefs(props);

  const state: Ref<any[]> = useDependentRef(props.modelValue, []);

  const onStateChange = useCallback();

  function isActive(item) {
    return state.value.includes(item);
  }

  function toggle(value) {
    const isChecked = state.value.includes(value);
    const index = state.value.indexOf(value);

    if (!isChecked) {
      state.value.push(value);
    } else {
      state.value.splice(index, 1);
    }

    onStateChange.trigger(state.value);
  }

  const exposedAPI = {
    isActive,
    toggle,
    onStateChange: onStateChange.on,
    flow: groupProps.flow || CheckboxGroupFlow.Vertical,
    disabled: groupProps.disabled,
    active: groupProps.active,
  };

  provide(CHECKBOX_GROUP_CONTEXT_KEY, exposedAPI);

  return exposedAPI;
}
