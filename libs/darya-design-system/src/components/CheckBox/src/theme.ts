export interface CheckboxTheme {
  checkbox: {
    base: string;
    states: Record<
      "normal" | "selected" | "disabled" | "disabledSelected" | "invalid",
      string
    >;
  };
  checkmark: {
    base: string;
    states: Record<"normal" | "selected" | "disabled", string>;
  };
  label: {
    base: string;
    layouts: Record<"horizontal" | "vertical", string>;
    states: Record<"normal" | "disabled" | "invalid", string>;
  };
}

export const defaultTheme: CheckboxTheme = {
  checkbox: {
    base: "self-center w-4 h-4 flex-shrink-0 flex rounded justify-center",
    states: {
      normal:
        "transition duration-150 ease-in-out border border-neutral-12 ring-primary-tint1 group-hover:ring group-hover:border-primary-tint1 group-hover:bg-primary-tint",
      selected:
        "transition duration-150 ease-in bg-primary-tint1 ring-primary-tint1 group-hover:ring group-hover:bg-primary-tint1",
      disabled: "border bg-gray-100 border-gray-300",
      disabledSelected: "bg-gray-300 border-gray-400",
      invalid: "border border-red-500",
    },
  },
  checkmark: {
    base: "flex self-center",
    states: {
      normal: "invisible",
      selected: "visible text-white",
      disabled: "visible text-gray-400",
    },
  },
  label: {
    base: "group cursor-pointer",
    layouts: {
      horizontal: "inline-flex items-center me-4",
      vertical: "flex mb-4 last:mb-0",
    },
    states: {
      normal: "text-gray-600",
      disabled: "text-gray-400 cursor-not-allowed",
      invalid: "text-red-600",
    },
  },
};

export const CHECKBOX_BASE = defaultTheme.checkbox.base;
export const CHECKBOX = defaultTheme.checkbox.states;
export const CHECKMARK_BASE = defaultTheme.checkmark.base;
export const CHECKMARK = defaultTheme.checkmark.states;
export const LABEL_BASE = defaultTheme.label.base;
export const LABEL = {
  ...defaultTheme.label.layouts,
  ...defaultTheme.label.states,
};
