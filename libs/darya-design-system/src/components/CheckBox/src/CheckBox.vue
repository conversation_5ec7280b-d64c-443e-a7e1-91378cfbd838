<template>
  <label
    :class="classes.label"
    :aria-disabled="disabled"
    :tabindex="tabIndex"
    role="checkbox"
    :style="{ fontSize: labelFontSize }"
  >
    <span :class="classes.customCheckboxInput">
      <DaryaTickIcon :class="classes.checkmark" size="18" />
    </span>

    <input
      type="checkbox"
      class="appearance-none"
      :value="value"
      :name="name"
      :disabled="disabled"
      tabindex="-1"
      aria-hidden="true"
      @input="handleInput"
    />

    <span v-if="label || $slots.default" class="ms-3">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script lang="ts" setup>
import { computed, withDefaults } from "vue";

import { useCheckboxContext } from "../useCheckbox";

import {
  CHECKBOX_BASE,
  CHECKBOX,
  CHECKMARK_BASE,
  CHECKMARK,
  LABEL_BASE,
  LABEL,
} from "./theme";

import type { CheckboxProps } from "../types";

const props = withDefaults(defineProps<CheckboxProps>(), {
  modelValue: false,
  value: false,
  name: "",
  label: "",
  labelFontSize: "14px", // Default font size
  disabled: false,
  error: "",
});

type CheckboxEmits = {
  (e: "update:modelValue", value: any): void;
};

const emit = defineEmits<CheckboxEmits>();

const { disabled, active, tabIndex, flow, onStateChange, toggle } =
  useCheckboxContext(props);

onStateChange((val) => emit("update:modelValue", val));

const handleInput = () => toggle(props.value);

const classes = computed(() => {
  const isInvalid = !!props.error && !disabled.value && !active.value;

  return {
    customCheckboxInput: [
      CHECKBOX_BASE,
      disabled.value
        ? active.value
          ? CHECKBOX.disabledSelected
          : CHECKBOX.disabled
        : active.value
          ? CHECKBOX.selected
          : isInvalid
            ? CHECKBOX.invalid
            : CHECKBOX.normal,
    ],
    checkmark: [
      CHECKMARK_BASE,
      active.value
        ? disabled.value
          ? CHECKMARK.disabled
          : CHECKMARK.selected
        : CHECKMARK.normal,
    ],
    label: [
      LABEL_BASE,
      LABEL[flow?.value ?? "horizontal"],
      isInvalid
        ? LABEL.invalid
        : disabled.value
          ? LABEL.disabled
          : LABEL.normal,
    ],
  };
});
</script>
