<template>
  <DField
    v-bind="{
      error,
      label,
      required,
      requiredSign,
      showError: required,
    }"
    role="checkboxgroup"
  >
    <template #input>
      <slot />
    </template>
  </DField>
</template>

<script lang="ts" setup>
import { withDefaults } from "vue";

import <PERSON>Field from "@/components/Field/src/Field.vue";
import { CheckboxGroupFlow } from "../types";
import { useCheckboxProvider } from "../useCheckbox";

type CheckboxGroupProps = {
  modelValue?: any[];
  flow?: CheckboxGroupFlow;
  label?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  requiredSign?: boolean;
};

type CheckboxGroupEmits = {
  (e: "update:modelValue", value: any[]): void;
};

const props = withDefaults(defineProps<CheckboxGroupProps>(), {
  modelValue: undefined,
  flow: CheckboxGroupFlow.Vertical,
  label: "",
  error: "",
  disabled: false,
  required: false,
  requiredSign: true,
});

const emit = defineEmits<CheckboxGroupEmits>();

const { onStateChange } = useCheckboxProvider(props);

onStateChange((value) => {
  emit("update:modelValue", value);
});
</script>
