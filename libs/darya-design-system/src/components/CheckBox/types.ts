import { Ref } from "vue";

export const enum CheckboxGroupFlow {
  Horizontal = "horizontal",
  Vertical = "vertical",
}

export type CheckboxContextType = {
  flow?: Ref<CheckboxGroupFlow>;
  tabIndex?: Ref<number>;
  disabled: Ref<boolean>;
  active: Ref<any>;
  toggle(value: any): void;
  onStateChange(value: any): void;
  isActive(value: any): void;
};

export type CheckboxProviderType = {
  flow: Ref<CheckboxGroupFlow>;
  toggle(value: any): void;
  onStateChange(value: any): void;
  isActive(value: any): void;
};

export type CheckboxProps = {
  modelValue?: any;
  value?: any;
  name?: string;
  label?: string;
  labelFontSize?: string;
  disabled?: boolean;
  error?: string;
};
