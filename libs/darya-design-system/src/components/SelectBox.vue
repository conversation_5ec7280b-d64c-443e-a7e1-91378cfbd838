<template>
  <div class="w-full">
    <label v-if="label" class="block text-sm text-gray-700 mb-1">
      {{ label }}
      <span v-if="required" aria-hidden="true" class="text-red-500">*</span>
    </label>
    <div class="relative inline-block w-full text-sm" ref="root">
      <button
        type="button"
        :disabled="disabled"
        class="w-full flex items-center justify-between px-4 py-2 border rounded-lg transition focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-400 focus:border-purple-500"
        :class="[
          'w-full flex items-center justify-between px-4 py-2 border rounded-lg transition focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed hover:border-gray-400 focus:border-purple-500',
          {
            'border-neutral-9': !isOpen && !hasError,
            'border-purple-500': isOpen && !hasError,
            'border-red-500': hasError,
            'focus:border-red-500': hasError,
          },
        ]"
        @click="toggleDropdown()"
        @blur="handleBlur"
        @focus="$emit('focus', $event)"
      >
        <span class="truncate">{{ displayLabel }}</span>
        <DaryaArrowDownIcon size="16" class="ms-1" />
      </button>

      <ul
        v-show="isOpen"
        class="absolute z-20 mt-1 w-full bg-white border border-neutral-9 rounded-lg shadow-lg max-h-60 overflow-auto"
        :class="[
          'absolute z-20 mt-1 w-full bg-white border border-neutral-9 rounded-lg shadow-lg max-h-60 overflow-auto',
        ]"
      >
        <!-- Search Input -->
        <li v-if="searchable" class="p-2 border-b border-gray-200">
          <div class="relative">
            <input
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              :placeholder="searchPlaceholder"
              class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
              @click.stop
              @keydown.stop
            />
            <DaryaOutlineSearchNormalIcon
              size="16"
              class="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
          </div>
        </li>

        <li
          v-if="placeholder && !searchable"
          class="px-4 py-2 text-gray-500 cursor-default"
        >
          {{ placeholder }}
        </li>

        <!-- No Results Message -->
        <li
          v-if="searchable && filteredOptions.length === 0 && searchQuery"
          class="px-4 py-2 text-gray-500 cursor-default text-center"
        >
          {{ noResultsText }}
        </li>

        <!-- Options List -->
        <li
          v-for="opt in displayOptions"
          :key="opt.value"
          class="px-4 py-2 cursor-pointer truncate transition hover:bg-gray-100"
          :class="{
            'bg-gray-100': props.multiple
              ? Array.isArray(props.modelValue) &&
                props.modelValue.includes(opt.value)
              : props.modelValue === opt.value,
            'opacity-50 cursor-not-allowed': opt.disabled,
          }"
          @click="!opt.disabled && select(opt.value)"
        >
          {{ opt.label }}
        </li>
      </ul>
      <span
        v-if="hasError"
        class="text-sm text-red-500 mt-1 block"
        role="alert"
        aria-live="polite"
      >
        {{ error }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, nextTick, watch } from "vue";
import { useToggle } from "@vueuse/core";

interface SelectOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface Props {
  modelValue?: string | string[] | null;
  options: SelectOption[];
  placeholder?: string;
  disabled?: boolean;
  multiple?: boolean;
  label?: string;
  required?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  noResultsText?: string;
  error?: string;
  validateOnBlur?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  disabled: false,
  multiple: false,
  searchable: false,
  required: false,
  searchPlaceholder: "Search...",
  noResultsText: "No results found",
  error: "",
});

const emit = defineEmits<{
  (e: "update:modelValue", value: string | string[] | null): void;
  (e: "blur", event: Event): void;
  (e: "change", value: string | string[] | null): void;
  (e: "focus", event: Event): void;
}>();

const [isOpen, toggle] = useToggle(false);
const root = ref<HTMLElement | null>(null);
const searchInput = ref<HTMLInputElement | null>(null);
const searchQuery = ref<string>("");

const hasError = computed(() => !!props.error);

// Computed property to filter options based on search query
const filteredOptions = computed(() => {
  if (!props.searchable || !searchQuery.value.trim()) {
    return props.options;
  }

  const query = searchQuery.value.toLowerCase().trim();
  return props.options.filter((option) =>
    option.label.toLowerCase().includes(query),
  );
});

// Computed property to determine which options to display
const displayOptions = computed(() => {
  return props.searchable ? filteredOptions.value : props.options;
});

// Custom toggle function to handle search input focus
const toggleDropdown = async () => {
  const wasOpen = isOpen.value;
  toggle();

  if (isOpen.value && props.searchable) {
    // Focus search input when dropdown opens
    await nextTick();
    searchInput.value?.focus();
  } else {
    // Clear search when dropdown closes
    searchQuery.value = "";

    // If dropdown was open and now closed, validate if required
    if (wasOpen && props.required && !validateRequired()) {
      emit('change', props.modelValue);
    }
  }
};

// Watch for dropdown close to clear search
watch(isOpen, (newValue) => {
  if (!newValue) {
    searchQuery.value = "";
  }
});

// Handle click outside to close dropdown
const handleClickAway = (event: MouseEvent) => {
  if (root.value && !root.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
};
document.addEventListener("click", handleClickAway);

// Handle escape key to close dropdown
const handleEscape = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    isOpen.value = false;
  }
};
document.addEventListener("keydown", handleEscape);

onUnmounted(() => {
  document.removeEventListener("click", handleClickAway);
  document.removeEventListener("keydown", handleEscape);
});

// Compute the display label
const displayLabel = computed(() => {
  if (props.multiple) {
    if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      return props.modelValue
        .map((val) => props.options.find((opt) => opt.value === val)?.label)
        .filter((label) => label !== undefined)
        .join(", ");
    }
    return props.placeholder ?? "";
  } else {
    const found = props.options.find((opt) => opt.value === props.modelValue);
    return found?.label ?? props.placeholder ?? "";
  }
});

// Validation logic
const validateRequired = () => {
  if (!props.required) return true;

  if (props.multiple) {
    return Array.isArray(props.modelValue) && props.modelValue.length > 0;
  } else {
    return props.modelValue !== null && props.modelValue !== undefined && props.modelValue !== '';
  }
};

// Handle blur event with validation
const handleBlur = (event: Event) => {
  emit('blur', event);

  if (props.validateOnBlur && props.required && !validateRequired()) {
    // Emit validation error - this can be caught by form validation libraries
    emit('change', props.modelValue);
  }
};

// Handle dropdown close with validation
const closeDropdown = () => {
  isOpen.value = false;

  // Validate when dropdown closes if required
  if (props.required && !validateRequired()) {
    // For single select, if nothing is selected and it's required, emit change to trigger validation
    if (!props.multiple && (props.modelValue === null || props.modelValue === undefined || props.modelValue === '')) {
      emit('change', props.modelValue);
    }
    // For multiple select, if no items are selected and it's required
    if (props.multiple && (!Array.isArray(props.modelValue) || props.modelValue.length === 0)) {
      emit('change', props.modelValue);
    }
  }
};

// Handle selection logic
const select = (val: string) => {
  let newValue: string | string[] | null;

  if (props.multiple) {
    const current = Array.isArray(props.modelValue) ? props.modelValue : [];
    if (current.includes(val)) {
      newValue = current.filter((v) => v !== val);
    } else {
      newValue = [...current, val];
    }
    emit("update:modelValue", newValue);
    emit("change", newValue);
  } else {
    newValue = val;
    emit("update:modelValue", newValue);
    emit("change", newValue);
    closeDropdown(); // Close dropdown for single-select
  }
};
</script>
