const BASE = {
  default: "px-3 inline-flex items-center rounded-lg group border",
};

const PADDINGS = {
  default: "p-inline-3",
  small: "p-inline-2",
  middle: "p-inline-3",
};

const FILL_THEME = {
  success: "bg-success text-green-700 border-transparent",
  warning: "bg-warning text-orange-700 border-transparent",
  error: "bg-error text-red-700 border-transparent",
  neutral: "bg-neutral-11  text-gray-700 border-transparent",
  info: "bg-info text-blue-700 border-transparent",
  primary: "bg-primary text-neutral border-transparent",
};

const OUTLINE_THEME = {
  default: {
    success: "bg-success/15 text-success-shade2 border-success-tint1",
    warning: "bg-warning/15 text-warning-shade2 border-warning-tint2 ",
    error: "bg-error/15 text-error-shade1 border-red-300 ",
    neutral: "bg-neutral-13 text-neutral-8 border-neutral/20 ",
    info: "bg-info/15 text-info-700 border-blue-300",
    primary: "bg-primary/15 text-neutral border-primary-tint6",
  },
  selected: {
    success:
      "bg-green-100 hover:bg-green-200 active:bg-green-300 text-green-700 border-green-300 hover:border-green-400 active:border-green-500",
    warning:
      "bg-orange-100 hover:bg-orange-200 active:bg-orange-300 text-orange-700 border-orange-300 hover:border-orange-400 active:border-orange-500",
    error:
      "bg-red-100 hover:bg-red-200 active:bg-red-300 text-red-700 border-red-300 hover:border-red-400 active:border-red-500",
    neutral:
      "bg-gray-200 hover:bg-gray-300 active:bg-gray-400 text-gray-700 border-gray-300 hover:border-gray-400 active:border-gray-500",
    info: "bg-blue-100 hover:bg-blue-200 active:bg-blue-300 text-blue-700 border-blue-300 hover:border-blue-400 active:border-blue-500",
    primary: "",
  },
};

const PLAIN_THEME = {
  default: {
    success: "text-green-700 hover:text-green-800 active:text-green-900",
    warning: "text-orange-700 hover:text-orange-800 active:text-orange-900",
    error: "text-red-700 hover:text-red-800 active:text-red-900",
    neutral: "text-gray-700 hover:text-gray-800 active:text-gray-900",
    info: "text-blue-700 hover:text-blue-800 active:text-blue-900",
    primary: "",
  },
  selected: {
    success:
      "bg-green-100 hover:bg-green-200 active:bg-green-300 text-green-700",
    warning:
      "bg-orange-100 hover:bg-orange-200 active:bg-orange-300 text-orange-700",
    error: "bg-red-100 hover:bg-red-200 active:bg-red-300 text-red-700",
    neutral: "bg-gray-200 hover:bg-gray-300 active:bg-gray-400 text-gray-700",
    info: "bg-blue-100 hover:bg-blue-200 active:bg-blue-300 text-blue-700",
    primary: "",
  },
};

const SIZES = {
  small: "h-6 text-sm",
  middle: "h-8 text-md",
};

const ICON_SIZES = {
  small: "16",
  middle: "24",
};

export type Size = keyof typeof SIZES;
export type Color = keyof typeof FILL_THEME;

export {
  BASE,
  PADDINGS,
  ICON_SIZES,
  SIZES,
  FILL_THEME,
  OUTLINE_THEME,
  PLAIN_THEME,
};
