<template>
  <div :class="classes" @click="handleSelect">
    <div v-if="showIcon" class="h-4">
      <slot name="icon" />
    </div>
    <span v-if="label" class="text-sm">{{ label }}</span>
    <span
      v-if="closable"
      class="cursor-pointer ps-1 -me-0.5"
      @click.stop.prevent="emit('close')"
    >
      <DaryaCloseIcon :size="ICON_SIZES[size]" />
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import {
  BASE,
  PADDINGS,
  SIZES,
  FILL_THEME,
  OUTLINE_THEME,
  PLAIN_THEME,
  ICON_SIZES,
  Color,
  Size,
} from "./theme";

defineOptions({ name: "DChip" });
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    variant?: "fill" | "outline" | "plain";
    closable?: boolean;
    showIcon?: boolean;
    label?: string | number;
    size?: Size;
    color?: Color;
    selectable?: boolean;
    status?:
      | "confirmed"
      | "pending"
      | "failed"
      | "upgraded"
      | "required"
      | "default";
  }>(),
  {
    variant: "fill",
    closable: false,
    showIcon: false,
    label: "",
    color: "neutral",
    size: "middle",
    selectable: false,
    status: "default",
  },
);

const emit = defineEmits<{
  (e: "close"): void;
  (e: "update:modelValue", v: boolean): void;
}>();

const statusColorMap = {
  confirmed: "success",
  pending: "warning",
  failed: "error",
  upgraded: "info",
  required: "primary",
  default: props.color,
} as const;

const chipColor = computed(() => statusColorMap[props.status]);

const classes = computed(() => {
  const base = [BASE.default, PADDINGS.default, SIZES[props.size]];
  let theme: string;

  if (props.variant === "fill") {
    theme = FILL_THEME[chipColor.value];
  } else if (props.variant === "plain") {
    theme = props.modelValue
      ? PLAIN_THEME.selected[chipColor.value]
      : PLAIN_THEME.default[chipColor.value];
  } /* outline */ else {
    theme = props.modelValue
      ? OUTLINE_THEME.selected[chipColor.value]
      : OUTLINE_THEME.default[chipColor.value];
  }

  return [...base, theme];
});

function handleSelect() {
  if (props.selectable) {
    emit("update:modelValue", !props.modelValue);
  }
}
</script>
