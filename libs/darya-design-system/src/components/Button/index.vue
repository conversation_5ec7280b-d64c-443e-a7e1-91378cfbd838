<template>
  <button
    :type="type || 'button'"
    :class="[
      baseClasses,
      sizeClasses[size],
      variantClasses,
      selectedOffset,
      className || '',
    ]"
    @click="handleClick"
    :disabled="disabled || loading"
    :aria-label="loading ? `${label || 'Button'} loading` : label || 'Button'"
    :aria-pressed="selected"
    :aria-busy="loading"
  >
    <!-- Loading Spinner -->
    <span
      v-if="loading"
      class="inline-flex items-center justify-center me-2 flex-shrink-0"
      aria-hidden="true"
    >
      <svg class="animate-spin" :class="spinnerSizeClass" viewBox="0 0 24 24">
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
          stroke-dasharray="31.416"
          stroke-dashoffset="31.416"
          opacity="0.3"
        />
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          stroke-linecap="round"
          stroke-dasharray="31.416"
          stroke-dashoffset="23.562"
        />
      </svg>
    </span>

    <!-- Left Icon (hidden when loading) -->
    <span
      v-if="!loading && icon && iconPosition === 'left'"
      :class="iconWrapperClass"
      aria-hidden="true"
    >
      <component :is="icon" :size="iconSize || 20" />
    </span>

    <!-- Button Content -->
    <span :class="{ 'opacity-75': loading }">
      <slot>{{ label }}</slot>
    </span>

    <!-- Right Icon (hidden when loading) -->
    <span
      v-if="!loading && icon && iconPosition === 'right'"
      :class="iconWrapperClass"
      aria-hidden="true"
    >
      <component :is="icon" :size="iconSize || 20" />
    </span>
  </button>
</template>

<script lang="ts">
import { DefineComponent } from "vue";

// Define props interface
export interface ButtonProps {
  label?: string;
  styleType?: "primary" | "secondary" | "error" | "success" | "info";
  type?: "button" | "submit" | "reset";
  variant?: "fill" | "outline" | "text";
  size?: "small" | "medium" | "large" | "extraLarge";
  disabled?: boolean;
  loading?: boolean;
  icon?: string | DefineComponent;
  iconPosition?: "left" | "right";
  color?: string;
  selected?: boolean;
  className?: string;
  iconSize?: number;
}

type ButtonSize = "small" | "medium" | "large" | "extraLarge";
</script>

<script setup lang="ts">
import { withDefaults, defineProps, computed } from "vue";
import { baseClasses, sizeClasses } from "./theme";

// Define props with defaults
const props = withDefaults(defineProps<ButtonProps>(), {
  styleType: "primary",
  variant: "fill",
  size: "medium",
  disabled: false,
  loading: false,
  iconPosition: "left",
  type: "button",
  selected: false,
  iconSize: 20,
});

// Define emitted events
const emit = defineEmits(["click"]);

// Compute variant classes based on styleType and variant
const variantClasses = computed(() => {
  // Base classes for loading state
  const loadingClasses = props.loading ? "cursor-not-allowed" : "";

  if (props.variant === "fill") {
    if (props.styleType === "primary") {
      const baseClass = props.selected
        ? "bg-primary"
        : "bg-primary text-white hover:primary-tint1 disabled:bg-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else if (props.styleType === "secondary") {
      const baseClass = props.selected
        ? "bg-gray-200 brightness-90"
        : "bg-gray-200 text-black hover:bg-gray-300 disabled:bg-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else if (props.styleType === "success") {
      const baseClass = props.selected
        ? "bg-success"
        : "bg-success text-white hover:bg-success-tint2 disabled:bg-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else if (props.styleType === "info") {
      const baseClass = props.selected
        ? "bg-info-shade2"
        : "bg-info text-white hover:bg-info-tint1 disabled:bg-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else {
      const baseClass = props.selected
        ? "bg-error"
        : "bg-error text-white hover:bg-error-tint2 disabled:bg-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    }
  } else if (props.variant === "outline") {
    if (props.styleType === "primary") {
      const baseClass = props.selected
        ? "bg-blue-50 border border-blue-600"
        : "border border-blue-600 text-blue-600 hover:bg-blue-50 disabled:border-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else if (props.styleType === "secondary") {
      const baseClass = props.selected
        ? "border border-neutral-10"
        : "border border-neutral-10 text-neutral-0 hover:bg-gray-50 disabled:border-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else {
      const baseClass = props.selected
        ? "border border-error"
        : "text-error border border-error hover:text-error-tint1 hover:border-error-tint1 disabled:border-neutral-11 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    }
  } else {
    if (props.styleType === "primary") {
      const baseClass = props.selected
        ? "bg-blue-50 underline text-blue-600"
        : "text-blue-600 hover:bg-blue-50 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else if (props.styleType === "secondary") {
      const baseClass = props.selected
        ? "bg-gray-50 underline text-gray-500"
        : "text-gray-500 hover:bg-gray-50 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    } else {
      const baseClass = props.selected
        ? "bg-red-50 underline text-red-600"
        : "text-red-600 hover:bg-red-50 disabled:text-neutral-7";
      return `${baseClass} ${loadingClasses}`;
    }
  }
});

const selectedOffset = computed(() => (props.selected ? "ring-offset-1" : ""));

// Compute spinner size based on button size
const spinnerSizeClass = computed(() => {
  const sizeMap = {
    small: "w-4 h-4",
    medium: "w-5 h-5",
    large: "w-5 h-5",
    extraLarge: "w-6 h-6",
  };
  return sizeMap[props.size];
});

// Compute classes for icon wrapper
const iconWrapperClass = computed(() => {
  return [
    "flex",
    "items-center",
    props.iconPosition === "left" ? "me-2" : "ms-2",
  ].join(" ");
});

// Handle click event
const handleClick = () => {
  if (!props.disabled && !props.loading) {
    emit("click");
  }
};
</script>

<style scoped>
/* Enhanced spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Ensure consistent button height during loading */
button {
  min-height: fit-content;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Loading state optimizations */
button:disabled {
  pointer-events: none;
}

/* Smooth transitions */
button * {
  transition: opacity 0.2s ease-in-out;
}
</style>
