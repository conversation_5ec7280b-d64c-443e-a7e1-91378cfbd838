<template>
  <svg
    :width="`${diameter}px`"
    :height="`${diameter}px`"
    class="animate-spin duration-2000"
    viewBox="0 0 100 100"
  >
    <defs>
      <linearGradient
        id="spinnerGradient"
        gradientUnits="userSpaceOnUse"
        x1="0"
        y1="0"
        x2="100"
        y2="0"
      >
        <stop offset="0%" stop-color="#7B62FF" />
        <stop offset="50%" stop-color="rgba(123,98,255,0)" />
        <stop offset="100%" stop-color="rgba(123,98,255,0)" />
      </linearGradient>
    </defs>

    <!-- background ring -->
    <circle
      cx="50"
      cy="50"
      r="45"
      stroke="rgba(0,0,0,0.1)"
      stroke-width="10"
      fill="none"
    />

    <!-- gradient ring -->
    <circle
      cx="50"
      cy="50"
      r="45"
      stroke="url(#spinnerGradient)"
      stroke-width="10"
      stroke-linecap="round"
      fill="none"
    />
  </svg>
</template>

<script setup lang="ts">
type Props = {
  size?: number;
};
const props = withDefaults(defineProps<Props>(), { size: 100 });

const diameter = props.size;
</script>
