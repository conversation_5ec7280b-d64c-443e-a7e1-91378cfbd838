<template>
  <Teleport to="body">
    <transition name="fade">
      <div
        v-if="open"
        ref="modalRef"
        class="fixed inset-0 flex items-center justify-center bg-black/50 p-4 z-50"
        role="dialog"
        aria-modal="true"
        :aria-labelledby="titleId"
        @click.self="handleBackdropClick"
        @keydown.esc="closeDialog"
      >
        <div
          :dir="props.dir"
          :class="[
            'bg-white rounded-3xl shadow-xl w-full overflow-hidden',
            props.customWidth ? '' : maxWidthClass,
            $attrs.class,
          ]"
          :style="props.customWidth ? { 'max-width': props.customWidth } : {}"
        >
          <div
            v-if="showCloseIcon || title"
            :class="[
              'px-4 py-2 flex justify-between items-center',
              props.dir === 'rtl' ? 'flex-row-reverse' : '',
            ]"
          >
            <h2 v-if="title" :id="titleId" class="text-lg font-semibold">
              <slot name="header">
                {{ title }}
              </slot>
            </h2>
            <button
              v-if="showCloseIcon"
              @click="closeDialog"
              class="p-1 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Close"
            >
              <DaryaCloseIcon />
            </button>
          </div>

          <div class="p-4 overflow-y-auto max-h-[850vh]">
            <slot name="body" />
          </div>

          <div
            v-if="$slots.actions"
            :class="[
              'border-t p-4 bg-gray-50 flex gap-2',
              props.dir === 'rtl'
                ? 'flex-row-reverse justify-start'
                : 'justify-end',
            ]"
          >
            <slot name="actions" />
          </div>
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { useScrollLock } from "@vueuse/core";

// Define the props interface for TypeScript
interface DialogProps {
  open: boolean;
  title?: string;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "full";
  customWidth?: string; // New prop for custom max-width
  dir?: "ltr" | "rtl";
  showCloseIcon?: boolean;
  closeOnClickOutside?: boolean; // New prop to control backdrop click behavior
}

// Define props with defaults
const props = withDefaults(defineProps<DialogProps>(), {
  title: undefined,
  maxWidth: "md",
  customWidth: undefined,
  dir: "ltr",
  showCloseIcon: true,
  closeOnClickOutside: true,
});

const emit = defineEmits(["update:open"]);

// Generate a unique ID for accessibility
const titleId = `dialog-title-${Math.random().toString(36).substr(2, 9)}`;
const modalRef = ref<HTMLElement | null>(null);

// Lock scroll when dialog is open
const isLocked = useScrollLock(document.body);

watch(
  () => props.open,
  (isOpen) => (isLocked.value = isOpen),
);

// Close dialog function
const closeDialog = () => {
  emit("update:open", false);
};

// Handle backdrop click
const handleBackdropClick = () => {
  if (props.closeOnClickOutside) {
    closeDialog();
  }
};

// Focus the dialog when it opens
watch(
  () => props.open,
  async (isOpen) => {
    if (isOpen) {
      await nextTick();
      modalRef.value?.focus();
    }
  },
);

// Compute the max-width class based on the maxWidth prop
const maxWidthClass = computed(() => {
  const sizes = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    "3xl": "max-w-3xl",
    full: "max-w-full",
  };
  return sizes[props.maxWidth] || sizes.md;
});
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
