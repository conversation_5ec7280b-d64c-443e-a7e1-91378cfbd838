<template>
  <div class="flex items-center relative mb-4 ms-6">
    <input
      :value="modelValue"
      @input="updateValue"
      type="text"
      :placeholder="placeholder"
      :dir="dir"
      class="border-0 text-neutral-10 rounded-lg p-2 bg-primary/10 focus:outline-none focus:ring-0 w-64"
      :class="[dir === 'ltr' ? 'pl-10' : 'pr-10']"
    />

    <button
      class="absolute h-full bg-transparent text-neutral-8 px-3 flex items-center justify-center"
      :class="[dir === 'ltr' ? 'left-0' : 'right-0']"
    >
      <DaryaOutlineSearchNormalIcon size="20" />
    </button>
  </div>
</template>

<script setup lang="ts">
type TSearchBoxProps = {
  modelValue: string;
  placeholder?: string;
  dir?: "ltr" | "rtl";
};

withDefaults(defineProps<TSearchBoxProps>(), {
  dir: "ltr",
});

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

const updateValue = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit("update:modelValue", target.value);
};
</script>
