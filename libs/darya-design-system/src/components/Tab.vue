<template>
  <div class="tabs">
    <!-- Tab Navigation -->
    <div class="flex gap-2.5 px-3 border-b border-neutral-12" role="tablist">
      <button
        v-for="tab in tabs"
        :key="tab.name"
        role="tab"
        :aria-selected="modelValue === tab.name"
        :tabindex="modelValue === tab.name ? 0 : -1"
        class="relative cursor-pointer transition-colors duration-200"
        :class="{
          'text-primary border-b-2 border-primary': modelValue === tab.name,
          'text-neutral-6 hover:text-primary': modelValue !== tab.name,
        }"
        @click="setActiveTab(tab.name)"
        @keydown.enter.prevent="setActiveTab(tab.name)"
        @keydown.space.prevent="setActiveTab(tab.name)"
      >
        <component
          v-if="tab.icon"
          :is="tab.icon"
          class="mr-1.5 h-5 w-5"
          :class="modelValue === tab.name ? 'text-primary' : 'text-neutral-6'"
        />
        <span class="text-sm mx-1.5 my-3">{{ tab.label }}</span>
      </button>
    </div>

    <div role="tabpanel" :aria-labelledby="modelValue">
      <slot :name="modelValue"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from "vue";

interface Tab {
  name: string;
  label: string;
  icon?: string;
}

const props = defineProps<{
  tabs: Tab[];
  modelValue: string;
}>();

const emit = defineEmits(["update:modelValue"]);

const setActiveTab = (name: string) => {
  emit("update:modelValue", name);
};
</script>
