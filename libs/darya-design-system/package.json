{"name": "@libs/darya-design-system", "version": "1.0.0", "description": "A shared Vue 3 design system for the Darya monorepo", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "private": true, "dependencies": {"@libs/darya-icon-package": "workspace:*", "@libs/utils": "workspace:*", "@popperjs/core": "^2.11.8", "@vueuse/core": "^12.5.0", "vue": "^3.5.13"}, "devDependencies": {"@tailwindcss/vite": "^4.0.6", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.2", "tailwindcss": "^4.1.6", "vite": "^4.0.0", "vite-plugin-dts": "^4.2.3"}, "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "vite build"}}