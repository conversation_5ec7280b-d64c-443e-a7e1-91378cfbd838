{"name": "@libs/use", "private": true, "type": "module", "main": "./src/index.ts", "exports": {".": {"import": "./src/index.ts"}}, "dependencies": {"@tanstack/vue-query": "^5.66.0", "@vueuse/core": "^12.5.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "prettier": "^3.4.2", "typescript": "^5.7.3", "vite": "^5.4.12", "vue": "3.5.13"}}