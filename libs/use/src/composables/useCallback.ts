import { ref } from 'vue';
import type { Ref } from 'vue';

type Fn = (...arg: any[]) => any;

export function useCallback() {
  const callback: Ref<null | Fn> = ref(null);

  const off = () => {
    callback.value = null;
  };

  const on = (fn: Fn) => {
    callback.value = fn;

    return { off };
  };

  const trigger = (...params: any[]) => {
    callback.value?.(...params);
  };

  return { trigger, on, off, callback };
}
