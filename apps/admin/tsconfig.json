{
  "compilerOptions": {
    "noImplicitAny": false,
    "useDefineForClassFields": true,
    "moduleResolution": "node",
    "module": "ESNext",
    "target": "ESNext",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "types": [
      "vite/client",
      "vite-plugin-pages/client",
      "vite-plugin-vue-layouts/client",
      "vitest/globals",
    ],
    "lib": ["ESNext", "DOM"],
    "skipLibCheck": true,
    "baseUrl": ".",
    "outDir": "dist",
    "paths": {
      "@/*": ["./src/*"],
      "@test/*": ["../../test/*"],
      "@tailwindcss/vite": ["node_modules/@tailwindcss/vite/dist/index.d.mts"]
    }
  },
  "typeRoots": ["./node_modules/@types"],
  "include": [
    "src/**/*.ts",
    "src/**/*.js",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "vite.config.ts"
  ],
  "exclude": ["node_modules", "dist"]
}
