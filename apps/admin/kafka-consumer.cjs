const { Kafka } = require('kafkajs');
const WebSocket = require('ws');

const kafka = new Kafka({
  clientId: 'my-app',
  brokers: ['172.20.30.11:9092'],
});

const consumer = kafka.consumer({ groupId: 'my-group' });

const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', (ws) => {
  console.log('Client connected to WebSocket');
  ws.on('close', () => console.log('Client disconnected'));
});

const run = async () => {
  await consumer.connect();
  await consumer.subscribe({ topic: 'Ticks', fromBeginning: false });

  await consumer.run({
    eachMessage: async ({ topic, partition, message }) => {
      const data = {
        topic,
        partition,
        offset: message.offset,
        value: message.value.toString(),
        timestamp: message.timestamp,
      };
      console.log(data);

      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(JSON.stringify(data));
        }
      });
    },
  });
};

run().catch(console.error);