# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

yarn-error.log
pnpm-debug.log
lerna-debug.log
node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.launch
*.sublime-workspace
.project
.classpath
.c9/
.settings/

# Ignore all local history of files
.history
.ionide

# NX cache folder
.nx
.nx/workspace-data
.nx/cache

# Test Files
/coverage

# Temporary or cache directories (if applicable)
.tmp/
.cache/