# Base layer with Node.js and pnpm
FROM node:18 AS base
RUN npm install -g pnpm && pnpm install

# Libs layer for libraries
FROM base AS libs
RUN pnpx nx run-many --target=build --projects=darya-icon-package,design-system --parallel

# App layer for the admin application
FROM base AS app
COPY apps/admin ./apps/admin
RUN pnpm run build:admin

# Production layer for running the application
FROM node:18 AS production
WORKDIR /apps/admin
COPY --from=app /apps/admin/dist ./dist
EXPOSE 3000
CMD ["pnpm", "run", "serve"]