import { computed, type Ref, type ComputedRef } from 'vue';
import {
  getEmptyStateConfig,
  type EmptyStateType,
} from '@/configs/emptyStateConfig';

export interface ErrorConfig {
  title: string;
  message: string;
  image: string;
  alt: string;
}

export interface EmptyConfig {
  title: string;
  message: string;
  image: string;
  alt: string;
}

export interface DataState {
  isLoading: ComputedRef<boolean>;
  hasError: ComputedRef<boolean>;
  hasNoData: ComputedRef<boolean>;
  isRefetching: ComputedRef<boolean>;
  errorConfig: ComputedRef<ErrorConfig>;
  emptyConfig: ComputedRef<EmptyConfig>;
}

/**
 * Composable for managing data state (loading, error, empty states)
 * Provides consistent state management across admin components
 *
 * @param status - Query status from TanStack Query
 * @param data - The data from the query
 * @param isFetching - Whether the query is currently fetching
 * @param isError - Whether the query has an error
 * @param error - The error object from the query
 * @param emptyStateType - Type of empty state to show
 * @returns DataState object with computed properties for different states
 */
export function useDataState<T>(
  status: Ref<string> | ComputedRef<string>,
  data: Ref<T> | ComputedRef<T>,
  isFetching: Ref<boolean> | ComputedRef<boolean>,
  isError: Ref<boolean> | ComputedRef<boolean>,
  error: Ref<any> | ComputedRef<any>,
  emptyStateType: EmptyStateType
): DataState {
  const isLoading = computed(() => {
    return isFetching.value && status.value === 'pending';
  });

  const hasError = computed(() => {
    return isError.value;
  });

  const hasNoData = computed(() => {
    if (isLoading.value || hasError.value) {
      return false;
    }

    const dataValue = data.value;

    // Handle different data types
    if (dataValue === null || dataValue === undefined) {
      return true;
    }

    if (Array.isArray(dataValue)) {
      return dataValue.length === 0;
    }

    if (typeof dataValue === 'object') {
      return Object.keys(dataValue).length === 0;
    }

    if (typeof dataValue === 'string') {
      return dataValue.trim() === '';
    }

    return false;
  });

  const isRefetching = computed(() => {
    return isFetching.value && status.value !== 'pending';
  });

  const errorConfig = computed((): ErrorConfig => {
    const errorValue = error.value;

    // Default error configuration
    let config: ErrorConfig = {
      title: 'Something went wrong',
      message: 'We encountered an unexpected error. Please try again.',
      image: 'error.svg',
      alt: 'Error occurred',
    };

    if (errorValue) {
      // Handle different error types based on status code
      const status = errorValue.response?.status;

      if (status >= 500) {
        config = {
          title: 'Server Error',
          message:
            'We were unable to process your request due to a server error. Please try again later.',
          image: 'server-error.svg',
          alt: 'Server error',
        };
      } else if (status >= 400 && status < 500) {
        config = {
          title: 'Request Error',
          message:
            'There was a problem with your request. Please check your input and try again.',
          image: 'client-error.svg',
          alt: 'Request error',
        };
      } else if (
        errorValue.code === 'NETWORK_ERROR' ||
        errorValue.message?.includes('Network')
      ) {
        config = {
          title: 'Connection Error',
          message: 'Please check your internet connection and try again.',
          image: 'network-error.svg',
          alt: 'Network error',
        };
      }
    }

    return config;
  });

  const emptyConfig = computed((): EmptyConfig => {
    return getEmptyStateConfig(emptyStateType);
  });

  return {
    isLoading,
    hasError,
    hasNoData,
    isRefetching,
    errorConfig,
    emptyConfig,
  };
}
