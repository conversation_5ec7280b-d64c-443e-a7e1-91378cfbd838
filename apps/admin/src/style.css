@import "tailwindcss";
@tailwind base;
@tailwind components;
@tailwind utilities;

@theme {
    /* core keywords */
    --color-transparent: transparent;
    --color-current: currentColor;

    /* primary */
    --color-primary: #7B62FF;
    --color-primary-tint1: #856CFF;
    --color-primary-tint2: #8E77FF;
    --color-primary-tint3: #9783FF;
    --color-primary-tint4: #A18EFF;
    --color-primary-tint5: #AA00FF; /* shorthand #A9F expanded */
    --color-primary-tint6: #B4A5FF;
    --color-primary-tint7: #D9D2FF;
    --color-primary-tint8: #ECE8FF;
    --color-primary-tint9: #A5A5DF;
    --color-primary-tint10: #F6F4FF;
    --color-primary-shade1: #6648FF;
    --color-primary-shade2: #512FFF;
    --color-primary-shade3: #512FFF;
    --color-primary-shade4: #512FFF;

    /* secondary */
    --color-secondary: #E5FF61;
    --color-secondary-tint1: #E8FF77;
    --color-secondary-tint2: #ECFF8E;
    --color-secondary-tint3: #EEFF99; /* shorthand #EF9 expanded */
    --color-secondary-tint4: #F0FFA5;
    --color-secondary-tint5: #F2FFB0;
    --color-secondary-tint6: #F7FFD2;
    --color-secondary-shade1: #E0FF48;
    --color-secondary-shade2: #DCFF2F;
    --color-secondary-shade3: #D8FF15;
    --color-secondary-shade4: #D1FB00;
    --color-secondary-shade5: #BDE200;
    --color-secondary-shade6: #7E9700;

    /* error */
    --color-error: #C92334;
    --color-error-tint1: #DC3647;
    --color-error-tint2: #DF4756;
    --color-error-tint3: #FCEEF0;
    --color-error-shade1: #B92130;
    --color-error-shade2: #AB1E2C;

    /* success */
    --color-success: #269C41;
    --color-success-tint1: #2AAE49;
    --color-success-tint2: #37CF5A;
    --color-success-tint3: #EDFBF0;
    --color-success-shade1: #23913D;
    --color-success-shade2: #23913D;

    /* warning */
    --color-warning: #FFC107;
    --color-warning-tint1: #FFC929;
    --color-warning-tint2: #FFCE3B;
    --color-warning-tint3: #FFF6DB;
    --color-warning-shade1: #F2B500;
    --color-warning-shade2: #DFA700;

    /* info */
    --color-info: #1599AD;
    --color-info-tint1: #19B5CD;
    --color-info-tint2: #1CC6E0;
    --color-info-tint3: #ECFAFD;
    --color-info-shade1: #1599AD;
    --color-info-shade2: #1599AD;

    /* neutral */
    --color-neutral: #000000;
    --color-neutral-1: #121212;
    --color-neutral-2: #494949;
    --color-neutral-3: #494949;
    --color-neutral-4: #494949;
    --color-neutral-5: #5B5B5B;
    --color-neutral-6: #6D6D6D;
    --color-neutral-7: #808080;
    --color-neutral-8: #929292;
    --color-neutral-9: #A4A4A4;
    --color-neutral-10: #B6B6B6;
    --color-neutral-11: #C8C8C8;
    --color-neutral-12: #DBDBDB;
    --color-neutral-13: #EDEDED;

    /* lightMood */
    --color-lightMood-1: #FAFAFA;
    --color-lightMood-2: #F4F4F8;
    --color-lightMood-3: #E6E7F0;

    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

@font-face {
    font-family: 'Public Sans';
    src: url('@/assets/fonts/PublicSans/PublicSans-Regular.woff2') format('woff2'),
    url('@/assets/fonts/PublicSans/PublicSans-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Public Sans';
    src: url('@/assets/fonts/PublicSans/PublicSans-Bold.woff2') format('woff2'),
    url('@/assets/fonts/PublicSans/PublicSans-Bold.woff') format('woff');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Public Sans';
    src: url('@/assets/fonts/PublicSans/PublicSans-Medium.woff2') format('woff2'),
    url('@/assets/fonts/PublicSans/PublicSans-Medium.woff') format('woff');
    font-weight: bold;
    font-style: normal;
}

body {
    padding: 0;
    margin: 0;
    font-family: 'Public Sans', sans-serif;
}

html,
body {
    height: 100%;
    width: 100%;
    overflow: hidden;
}