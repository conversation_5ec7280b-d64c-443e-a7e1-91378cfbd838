<template>
  <!-- Loading State -->
  <div v-if="isLoading" class="flex justify-center items-center py-20">
    <div class="flex flex-col items-center gap-4">
      <DLoading size="60" />
    </div>
  </div>

  <!-- Error State -->
  <div v-else-if="hasError" class="flex flex-col items-center py-20">
    <div class="text-center max-w-md min-h-[200px]">
      <div class="mb-6">
        <Image
          :src="errorConfig.image"
          :alt="errorConfig.alt"
          class="mb-4 max-w-full h-auto"
        />
      </div>
      <h3 class="text-lg font-semibold text-neutral-2 mb-2">
        {{ errorConfig.title }}
      </h3>
      <p class="text-neutral-5 mb-6">
        {{ errorConfig.message }}
      </p>
      <button
        v-if="onRetry"
        @click="onRetry"
        class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-shade1 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>

  <!-- Empty State -->
  <div v-else-if="hasNoData" class="flex flex-col items-center py-20">
    <div class="text-center max-w-md min-h-[200px]">
      <div class="mb-6">
        <Image
          :src="emptyConfig.image"
          :alt="emptyConfig.alt"
          class="mb-4 max-w-full h-auto"
        />
      </div>
      <h3 class="text-lg font-semibold text-neutral-2 mb-2">
        {{ emptyConfig.title }}
      </h3>
      <p class="text-neutral-5">
        {{ emptyConfig.message }}
      </p>
    </div>
  </div>

  <!-- Success State - Show Content -->
  <div v-else class="relative">
    <!-- Refetching Indicator -->
    <!--    <div-->
    <!--      v-if="isRefetching"-->
    <!--      class="absolute top-0 right-0 z-10 bg-primary text-white text-xs px-2 py-1 rounded-bl-lg"-->
    <!--    >-->
    <!--      Updating...-->
    <!--    </div>-->

    <!-- Main Content -->
    <slot />
  </div>
</template>

<script setup lang="ts">
import Image from './Image.vue';
import type { ErrorConfig, EmptyConfig } from '@/composables/useDataState';

defineProps<{
  isLoading: boolean;
  hasError: boolean;
  hasNoData: boolean;
  isRefetching?: boolean;
  errorConfig: ErrorConfig;
  emptyConfig: EmptyConfig;
  onRetry?: () => void;
}>();
</script>
