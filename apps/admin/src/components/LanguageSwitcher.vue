<template>
  <div class="relative mx-5 mt-5">
    <div class="flex flex-col space-y-2">
      <label for="language" class="text-sm font-medium block mb-2">
        {{ t('login.select_language') }}
      </label>

      <div class="relative" ref="dropdownRef">
        <button
          @click="toggleDropdown"
          class="flex items-center justify-between w-full rounded-md border border-gray-300 bg-white p-3 transition-colors focus:outline-none hover:bg-gray-100"
        >
          <span class="flex items-center">
            <Image
              v-if="selectedLanguageIcon"
              :src="selectedLanguageIcon"
              :alt="getSelectedLanguageLabel"
              class="w-4 h-4 me-2"
            />
            <span>{{ getSelectedLanguageLabel }}</span>
          </span>

          <img
            v-if="isDropdownOpen"
            src="@/assets/images/arrow-up.svg"
            alt="arrow"
            class="w-4 h-4"
          />
          <img
            v-else
            alt="arrow"
            src="@/assets/images/arrow-down.svg"
            class="w-4 h-4"
          />
        </button>

        <transition name="slide-fade">
          <div
            v-if="isDropdownOpen"
            class="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg"
          >
            <ul class="max-h-60 overflow-auto">
              <li
                v-for="lang in LANGUAGES"
                :key="lang.code"
                @click="selectLanguage(lang)"
                class="flex items-center cursor-pointer p-2 hover:bg-blue-100"
              >
                <Image
                  :src="lang.icon"
                  :alt="lang.label"
                  class="w-5 h-5 me-2"
                />

                <span>{{ lang.label }}</span>
              </li>
            </ul>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { onClickOutside } from '@vueuse/core';
import Image from '@/components/Image.vue';
import { useLocale, type SupportedLocale } from '@/composables/useLocale';

const { t } = useI18n();
const { locale, updateLocale } = useLocale();

const LANGUAGES = [
  { code: 'en', label: 'English', icon: 'en.svg' },
  { code: 'ja', label: '日本語', icon: 'ja.svg' },
  { code: 'ru', label: 'Русский', icon: 'ru.svg' },
  { code: 'sr', label: 'Српски', icon: 'sr.svg' },
  { code: 'es', label: 'Español', icon: 'es.svg' },
  { code: 'pt', label: 'Português', icon: 'pt.svg' },
  { code: 'fr', label: 'Français', icon: 'fr.svg' },
  { code: 'ar', label: 'العربية', icon: 'ar.svg' },
];

const isDropdownOpen = ref(false);
const dropdownRef = ref<HTMLElement | null>(null);

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};

const selectLanguage = (lang: { code: string }) => {
  updateLocale(lang.code as SupportedLocale);
  isDropdownOpen.value = false;
};

const getSelectedLanguageLabel = computed(() => {
  return (
    LANGUAGES.find((lang) => lang.code === locale.value)?.label ||
    'Select Language'
  );
});

const selectedLanguageIcon = computed(() => {
  return LANGUAGES.find((lang) => lang.code === locale.value)?.icon || null;
});

onClickOutside(dropdownRef, () => {
  isDropdownOpen.value = false;
});
</script>
