<template>
  <img :src="imageSrc" :alt="alt" />
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  alt: {
    type: String,
    required: true,
  },
});

const imageSrc = computed(() => {
  try {
    return new URL(`../assets/images/${props.src}`, import.meta.url).href;
  } catch (err) {
    return '';
  }
});
</script>
