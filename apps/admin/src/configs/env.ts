const VERSION = import.meta.env.VITE_VERSION ?? 'N/A';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL ?? '';
const API_BASE_PATH = import.meta.env.VITE_API_BASE_PATH ?? 'api/v1';

const buildApiUrl = (resource: string): string => {
  const cleanResource = resource.replace(/^\/|\/$/g, '');
  return `${API_BASE_URL}/${cleanResource}/${API_BASE_PATH}`;
};

const USER_MANAGE_API = buildApiUrl('usermanage');
const PAYMENT_MANAGE_API = buildApiUrl('paymentmanage');
const PLAN_MANAGE_API = buildApiUrl('planmanage');
const ACCOUNT_MANAGE_API = buildApiUrl('accountmanage');
const KYC_MANAGE_API = buildApiUrl('kycmanage');

const NODE_ENV = import.meta.env.MODE ?? 'development';
const IS_DEV = NODE_ENV === 'development';
const IS_TEST_MODE = import.meta.env.VITEST || NODE_ENV === 'test';

export {
  VERSION,
  IS_DEV,
  IS_TEST_MODE,
  PAYMENT_MANAGE_API,
  PLAN_MANAGE_API,
  ACCOUNT_MANAGE_API,
  USER_MANAGE_API,
  KYC_MANAGE_API
};
