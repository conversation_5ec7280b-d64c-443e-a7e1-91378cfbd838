export type EmptyStateType =
  | 'data'
  | 'users'
  | 'accounts'
  | 'user-accounts'
  | 'plan'
  | 'breached-rule'
  | 'payout'
  | 'verifications'
  | 'verification-detail';

export interface EmptyStateConfig {
  title: string;
  message: string;
  image: string;
  alt: string;
}

export const emptyStateConfig: Record<EmptyStateType, EmptyStateConfig> = {
  data: {
    title: 'No Data Available',
    message: 'There is no data to display at the moment.',
    image: 'empty-state.svg',
    alt: 'No data available',
  },
  users: {
    title: '',
    message: 'No user data created yet.',
    image: 'empty-state.svg',
    alt: 'No users found',
  },
  accounts: {
    title: '',
    message: 'No trading data created yet.',
    image: 'account-empty-state.svg',
    alt: 'No accounts found',
  },
  'user-accounts': {
    title: '',
    message: 'No accounts created yet.',
    image: 'account-empty-state.svg',
    alt: 'No accounts found',
  },
  plan: {
    title: '',
    message: 'No trading programs created yet.',
    image: 'empty-state.svg',
    alt: 'No plan found',
  },
  'breached-rule': {
    title: '',
    message: 'No rules created yet.',
    image: 'empty-state.svg',
    alt: 'No breached rule found',
  },
  payout: {
    title: '',
    message: 'No payout request created yet.',
    image: 'empty-transactions.svg',
    alt: 'No payout found',
  },
  verifications: {
    title: '',
    message: 'No user data created yet.',
    image: 'empty-state.svg',
    alt: 'No verifications found',
  },
  'verification-detail': {
    title: '',
    message: 'No verification documents submitted yet.',
    image: 'empty-state.svg',
    alt: 'No verification detail found',
  },
};

export const getEmptyStateConfig = (type: EmptyStateType): EmptyStateConfig => {
  return emptyStateConfig[type];
};
