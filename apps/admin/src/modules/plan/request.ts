import http from '@/services/http';

import { PLAN_MANAGE_API } from '@/configs/env';

export function getAllPlan() {
  return http.get(`${PLAN_MANAGE_API}/Plan/GetAll`);
}
export function getPlanById(planId) {
  return http.get(`${PLAN_MANAGE_API}/Plan/GetById`, {
    params: { id: planId },
  });
}
export function createPlan(data) {
  return http.post(`${PLAN_MANAGE_API}/Plan/CreatePlan`, data);
}
export function updatePlan(data) {
  return http.put(`${PLAN_MANAGE_API}/Plan/UpdatePlan`, data);
}
export function deletePlan(planId) {
  return http.delete(`${PLAN_MANAGE_API}/Plan/DeletePlan`, {
    params: { id: planId },
  });
}
