<template>
  <DDialog
    :open="isOpenEditPlan"
    title="Edit Program"
    :closeOnClickOutside="false"
    @update:open="emit('close')"
    maxWidth="xl"
  >
    <template #body>
      <VeeForm
        v-slot="{ meta, handleSubmit }"
        class="overflow-y-scroll h-[600px]"
      >
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="title"
              v-model="formModel.title"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Program Name"
                placeholder="Enter program name"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="description"
              v-model="formModel.description"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Description"
                placeholder="Enter Description"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="symbolNameCurrency"
              v-model="formModel.symbolNameCurrency"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Program Currency"
                placeholder="Enter currency"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField v-slot="{ field, errorMessage }" name="firstReward">
              <DInput
                v-bind="field"
                v-model="formModel.firstReward"
                type="text"
                label="Trading Days limit"
                placeholder="Enter Trading Days limit"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="initialBalance"
              v-model="formModel.initialBalance"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Initial Balance"
                placeholder="Enter initial balance"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="profitSplitUpto"
              v-model="formModel.profitSplitUpto"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Lowest Allowed Equity"
                placeholder="Enter Lowest Allowed Equity"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="drawDownUpdateTime"
            >
              <VDatePicker
                v-model="formModel.drawDownUpdateTime"
                mode="time"
                is24hr
                hide-time-header
                :masks="{
                  input: 'HH:mm:ss',
                  modelValue: 'HH:mm:ss',
                }"
                :model-config="{ type: 'string' }"
              >
                <template #default="{ inputValue, inputEvents }">
                  <DInput
                    :model-value="inputValue"
                    v-on="inputEvents"
                    label="DrawDown Update Time"
                    placeholder="HH:mm"
                    required
                    :error="errorMessage"
                    readonly
                  />
                </template>
              </VDatePicker>
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="price"
              v-model="formModel.price"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Price"
                placeholder="Enter Price"
                required
                :error="errorMessage"
              />
            </VeeField>
          </div>
          <div>
            <VeeField v-slot="{ field, errorMessage }" name="newsTrading">
              <DCheckbox
                v-bind="field"
                v-model="formModel.newsTrading"
                :label="'Send me News trading'"
                :error="errorMessage"
                :value="true"
              />
            </VeeField>
          </div>
          <div class="py-2 border-t border-gray-200">
            <div>Server Detail</div>
            <span class="text-sm text-gray-500">
              Please select the server groups that apply to this trading
              program.
            </span>
          </div>

          <DBox class="w-full bg-gray-100 px-3 py-4 rounded-md">
            <div class="w-full">
              <div class="flex justify-between items-center w-full">
                <span>TestOneEquiry-Server</span>
                <div class="flex gap-2">
                  <DBox
                    class="px-2 !bg-purple-300 !border !border-purple-300 rounded-3xl"
                  >
                    <span class="text-white text-xs">MT4</span>
                  </DBox>
                  <DBox class="px-2 !bg-gray-300 rounded-3xl">
                    <span class="text-gray-500 text-sm">Demo</span>
                  </DBox>
                </div>
              </div>
              <div class="flex w-full mt-4">
                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="Server Groups"
                  v-model="formModel.serverGroups"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    label="Server Groups"
                    placeholder="Enter Server Groups"
                    required
                    :error="errorMessage"
                  />
                </VeeField>
              </div>
            </div>
          </DBox>

          <VeeField v-slot="{ field, errorMessage }" name="tradeType">
            <DSelectBox
              v-bind="field"
              v-model="formModel.tradeType"
              label="Program Trading Type"
              :options="tradingTypeOption"
              placeholder="Select type"
              required
              :error="errorMessage"
              class="!max-w-full"
            />
          </VeeField>

          <VeeField v-slot="{ field, errorMessage }" name="nextPhaseId">
            <DSelectBox
              v-bind="field"
              v-model="formModel.nextPhaseId"
              label="Next Phase Id"
              :options="planList"
              placeholder="Select Next Phase Id"
              required
              :error="errorMessage"
              class="!max-w-full"
            />
          </VeeField>

          <div class="pb-2">
            <div class="w-full flex justify-between items-center">
              <VeeField v-slot="{ field, errorMessage }" name="breachRuleIds">
                <DSelectBox
                  v-bind="field"
                  v-model="formModel.breachRuleIds"
                  label="Breach Rule"
                  :options="breachRuleListOptions"
                  placeholder="Select Breach Rule"
                  multiple
                  required
                  :error="errorMessage"
                />
              </VeeField>
            </div>
          </div>

          <div class="pb-4">
            <div class="w-full flex justify-between items-center">
              <VeeField v-slot="{ field, errorMessage }" name="levelUpRuleIds">
                <DSelectBox
                  v-bind="field"
                  v-model="formModel.levelUpRuleIds"
                  label="LevelUp Rule"
                  :options="levelUpListOptions"
                  placeholder="Select LevelUp Rule"
                  multiple
                  required
                  :error="errorMessage"
                />
              </VeeField>
            </div>
          </div>

          <div class="flex flex-row-reverse gap-4">
            <DButton label="Update plan" type="submit" />
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { computed, toRef, watch, reactive } from 'vue';

import { useGetPlanById, useUpdatePlanMutation } from '@/modules/plan/index';
import {
  useGetAllLevelUpRule,
  useGetAllBreachRule,
} from '@/modules/rule/index';

const emit = defineEmits(['close']);
const props = defineProps<{
  isOpenEditPlan: boolean;
  planId: number;
  planList: any;
}>();

const planIdRef = toRef(props, 'planId');

const { getPlanById, planData } = useGetPlanById(planIdRef, false);
const { levelUpRuleList } = useGetAllLevelUpRule();
const { breachRuleList } = useGetAllBreachRule();

const breachRuleListOptions = computed(() => {
  return (
    breachRuleList.value?.map((x) => ({ label: x.name, value: x.id })) ?? []
  );
});

const levelUpListOptions = computed(() => {
  return (
    levelUpRuleList.value?.map((x) => ({ label: x.name, value: x.id })) ?? []
  );
});

const tradingTypeOption = [
  { label: 'Live trading', value: 1 },
  { label: 'Demo trading', value: 2 },
];

const formModel = reactive({
  title: '',
  firstReward: 0,
  initialBalance: 0,
  profitSplitUpto: 0,
  newsTrading: true,
  drawDownUpdateTime: '09:00:00',
  serverGroups: '',
  nextPhaseId: 0,
  price: 0,
  description: '',
  symbolNameCurrency: '',
  tradeType: 0,
  breachRuleIds: [] as [],
  levelUpRuleIds: [] as [],
  id: props.planId,
});

const { updatePlan } = useUpdatePlanMutation({
  onSuccess: () => {
    emit('close');
  },
  onError: (error) => {
    console.error('Error update plan:', error);
  },
});

watch(
  () => props.isOpenEditPlan,
  (isOpen) => {
    if (isOpen && props.planId) {
      getPlanById();
    }
  },
  { immediate: true }
);

watch(
  planData,
  (newData) => {
    if (newData) {
      Object.assign(formModel, newData);
    }
  },
  { deep: true }
);

const onSubmit = () => {
  updatePlan(formModel);
};
</script>
