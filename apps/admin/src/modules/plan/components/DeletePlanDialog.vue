<template>
  <DDialog
    :open="isOpenDeletePlan"
    title="Delete Program"
    :closeOnClickOutside="false"
    @update:open="emit('close')"
    maxWidth="xl"
  >
    <template #body>
      <div class="flex">
        <span class="text-sm font-medium">
          Are you sure you want to permanently delete this program?
        </span>
      </div>

      <div class="flex flex-row-reverse mt-4 gap-4">
        <DButton
          variant="outline"
          styleType="secondray"
          label="Delete Plan"
          @click="handleDelete"
        />

        <DButton
          variant="outline"
          styleType="primary"
          label="Cancel"
          @click="emit('close')"
        />
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { useToast } from '@libs/darya-design-system';
import { useDeletePlanById } from '@/modules/plan/index';

const emit = defineEmits(['close']);

const props = defineProps<{ isOpenDeletePlan: boolean; planId: string }>();

const { addToast } = useToast();

const handleSuccess = () => {
  emit('close');

  addToast({
    message: 'The plan deleted!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });
};
const handleError = () => {
  emit('close');

  addToast({
    message: 'The plan has not deleted!',
    type: 'error',
    position: 'right-top',
    duration: 3000,
  });
};
const { deletePlanById } = useDeletePlanById({
  onSuccess: handleSuccess,
  onError: handleError,
});

const handleDelete = () => {
  deletePlanById(props.planId);
};
</script>
