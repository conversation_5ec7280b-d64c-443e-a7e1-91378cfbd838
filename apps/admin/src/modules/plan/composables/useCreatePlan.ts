import { isAxiosError } from 'axios';
import { type MaybeRef, ref } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import {
  type planFormModel,
  type ApiResponse,
  isoToTimeSpan,
} from '@/modules/plan';

import { createPlan } from '../request';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<planFormModel>>,
    'mutationFn'
  > {}

function planFactory(formModel) {
  const timeString = ref();

  const timeFormatRegex = /^\d{2}:\d{2}:\d{2}$/; // Matches 'HH:mm:ss' format

  if (timeFormatRegex.test(formModel.drawDownUpdateTime)) {
    timeString.value = formModel.drawDownUpdateTime;
  } else {
    timeString.value = isoToTimeSpan(formModel.drawDownUpdateTime);
  }

  return {
    title: formModel.title,
    initialBalance: formModel.initialBalance,
    drawDownUpdateTime: timeString.value,
    serverGroups: formModel.serverGroups,
    nextPhaseId: formModel.nextPhaseId,
    price: formModel.price,
    description: formModel.description,
    symbolNameCurrency: formModel.symbolNameCurrency,
    tradeType: formModel.tradeType,
    breachRuleIds: formModel.breachRuleIds,
    levelUpRuleIds: formModel.levelUpRuleIds,
  };
}
export default function useCreatePlanMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<planFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await createPlan(planFactory(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    createPlan: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
