import { isAxiosError } from 'axios';
import { type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { updatePlan } from '../request';

import {
  type updatePlanFormModel,
  type ApiResponse,
  isoToTimeSpan,
} from '@/modules/plan';

function planFactory(formModel) {
  const timeString = isoToTimeSpan(formModel.drawDownUpdateTime);

  return {
    title: formModel.title,
    firstReward: formModel.firstReward,
    initialBalance: formModel.initialBalance,
    profitSplitUpto: formModel.profitSplitUpto,
    newsTrading: formModel.newsTrading,
    drawDownUpdateTime: timeString,
    serverGroups: formModel.serverGroups,
    nextPhaseId: formModel.nextPhaseId,
    price: formModel.price,
    description: formModel.description,
    symbolNameCurrency: formModel.symbolNameCurrency,
    tradeType: formModel.tradeType,
    breachRuleIds: formModel.breachRuleIds,
    levelUpRuleIds: formModel.levelUpRuleIds,
    id: formModel.id,
  };
}

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<updatePlanFormModel>>,
    'mutationFn'
  > {}

export default function useUpdatePlanMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<updatePlanFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await updatePlan(planFactory(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    updatePlan: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
