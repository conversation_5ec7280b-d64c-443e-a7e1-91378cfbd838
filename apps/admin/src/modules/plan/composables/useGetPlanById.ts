import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getPlanById } from '../request';

export default function useGetPlanById(
  planId: MaybeRef<number>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const planIdRef = toRef(planId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching } = useQuery({
    queryKey: computed(() => ['planData', planIdRef.value]),
    queryFn: () => {
      const id = planIdRef.value;

      if (!id) throw new Error('plan ID is required');

      return getPlanById(id);
    },
    enabled: shouldFetchRef,
  });

  const planData = computed(() => data.value?.data?.data ?? {});

  return {
    getPlanById: refetch,
    planData,
    isError,
    status,
    isFetching,
  } as const;
}
