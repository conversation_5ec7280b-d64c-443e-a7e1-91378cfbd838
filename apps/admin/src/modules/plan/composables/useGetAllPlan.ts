import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllPlan } from '../request';

type PlanResponse = Awaited<ReturnType<typeof getAllPlan>>;

export default function useGetAllPlan() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    PlanResponse,
    Error
  >({
    queryKey: ['planList'],
    queryFn: getAllPlan,
  });

  const planList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllPlan: refetch,
    planList,
    isFetching,
    isError,
    status,
    error,
  } as const;
}
