import { useMutation } from '@tanstack/vue-query';

import { deletePlan } from '../request';

type DeletePlanResponse = Awaited<ReturnType<typeof deletePlan>>;

export default function useDeletePlanById(options?: {
  onSuccess?: (data: DeletePlanResponse) => void;
  onError?: (error: Error) => void;
}) {
  const { mutate, isError, status } = useMutation<
    DeletePlanResponse,
    Error,
    string
  >({
    mutationKey: ['deletePlan'],
    mutationFn: deletePlan,
    onSuccess: options?.onSuccess,
    onError: options?.onError,
  });

  return {
    deletePlanById: mutate,
    isError,
    status,
  } as const;
}
