export interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

export interface updatePlanFormModel {
  title: string;
  firstReward: number;
  initialBalance: number;
  profitSplitUpto: number;
  newsTrading: boolean;
  drawDownUpdateTime: string;
  serverGroups: string;
  nextPhaseId: number;
  price: number;
  description: string;
  symbolNameCurrency: string;
  tradeType: number;
  breachRuleIds: [];
  levelUpRuleIds: [];
  id: number;
}
export interface planFormModel {
  title: string;
  initialBalance: number;
  firstReward: number;
  profitSplitUpto: number;
  drawDownUpdateTime: string;
  newsTrading: boolean;
  serverGroups: string;
  nextPhaseId: number;
  price: number;
  description: string;
  symbolNameCurrency: string;
  tradeType: number;
  breachRuleIds: [];
  levelUpRuleIds: [];
}
