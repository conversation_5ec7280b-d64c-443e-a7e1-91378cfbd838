import { VerificationStatusTypeId, StatusColorMap } from './types';

export const STATUS_COLOR_MAP: StatusColorMap = {
  [VerificationStatusTypeId.PENDING]: 'neutral',
  [VerificationStatusTypeId.IN_REVIEW]: 'primary',
  [VerificationStatusTypeId.UNDER_REVIEW]: 'warning',
  [VerificationStatusTypeId.APPROVED]: 'success',
  [VerificationStatusTypeId.REJECTED]: 'error',
};

export const QUERY_KEYS = {
  TAB_OF_VERIFICATION: (userId: string) => ['tabOfVerification', userId],
  PROOF_ADDRESS_DATA: (userId: string) => ['proofAddressData', userId],
  PROOF_IDENTIFICATION_DATA: (userId: string) => [
    'proofIdentificationData',
    userId,
  ],
  AUDIT_LOG: (userId: string) => ['auditLog', userId],
};
