import http from '@/services/http/index';

import {
  TabOfVerification,
  ApiResponse,
  AuditLogType,
  ProofOfAddress,
  ProofOfIdentification,
  UpdateUserKyc,
} from '@/modules/kyc';

import { KYC_MANAGE_API } from '@/configs/env';

export function getVerifications() {
  return http
    .get(`${KYC_MANAGE_API}/Verification/GetVerificationsUsers`)
    .then((response) => response.data);
}

export function getTabColumnVerification(
  userId: string | undefined,
  workflowId: number | null
): Promise<ApiResponse<TabOfVerification>> {
  if (!userId || !workflowId) {
    throw new Error('WorkflowId and userId is required');
  }

  return http
    .get(`${KYC_MANAGE_API}/Verification/GetTabColumnVerificationUser`, {
      params: { userId, workflowId },
    })
    .then((response) => response.data);
}

export function getProofOfAddressById(
  userId: string | undefined,
  actionId: number | null
): Promise<ApiResponse<ProofOfAddress>> {
  if (!userId || !actionId) {
    throw new Error('ActionId and userId is required');
  }

  return http
    .get(`${KYC_MANAGE_API}/Verification/GetProofOfAddress`, {
      params: { userId, actionId },
    })
    .then((response) => response.data);
}

export function getProofOfIdentificationById(
  userId: string | undefined,
  actionId: number | null
): Promise<ApiResponse<ProofOfIdentification>> {
  if (!userId || !actionId) {
    throw new Error('ActionId and userId is required');
  }

  return http
    .get(`${KYC_MANAGE_API}/Verification/GetProofOfIdentification`, {
      params: { userId, actionId },
    })
    .then((response) => response.data);
}

export function getAuditLog(
  userId: string
): Promise<ApiResponse<AuditLogType>> {
  return http
    .get(`${KYC_MANAGE_API}/Verification/GetAuditLog`, { params: { userId } })
    .then((response) => response.data);
}

export function updateKycStatus(data: UpdateUserKyc): Promise<any> {
  return http
    .post(`${KYC_MANAGE_API}/Verification/CompleteStepKyc`, data)
    .then((response) => response.data);
}
