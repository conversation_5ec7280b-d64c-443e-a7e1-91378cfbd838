import {
  VerificationStatusTypeId,
  STATUS_COLOR_MAP,
  DocumentObject,
} from '@/modules/kyc';

export function getStatusColor(status: VerificationStatusTypeId) {
  return STATUS_COLOR_MAP[status] || 'neutral';
}

export function isDocumentObject(document: any): document is DocumentObject {
  return (
    document &&
    typeof document === 'object' &&
    typeof document.fileData === 'string' &&
    typeof document.fileName === 'string' &&
    typeof document.fileExtension === 'string'
  );
}

export function getMimeTypeFromExtension(extension: string): string {
  const ext = extension.toLowerCase().replace('.', '');

  const mimeTypes: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    pdf: 'application/pdf',
    gif: 'image/gif',
    webp: 'image/webp',
  };

  return mimeTypes[ext] || 'application/octet-stream';
}

export function documentObjectToDataUrl(document: DocumentObject): string {
  if (!document.fileData) return '';

  // Determine MIME type from file extension
  const mimeType = getMimeTypeFromExtension(document.fileExtension);

  // Create data URL
  return `data:${mimeType};base64,${document.fileData}`;
}

export function getDocumentPreviewUrl(
  document: DocumentObject | string | undefined
): string | null {
  if (!document) return null;

  if (typeof document === 'string') {
    // Legacy format - assume it's base64 image data
    if (document.startsWith('data:')) {
      return document;
    }
    // If it's just base64 without data URL prefix, assume it's an image
    return `data:image/jpeg;base64,${document}`;
  }

  if (isDocumentObject(document)) {
    return documentObjectToDataUrl(document);
  }

  return null;
}
