<template>
  <DataStateWrapper
    :isLoading="proofOfAddressState.isLoading.value"
    :hasError="proofOfAddressState.hasError.value"
    :hasNoData="proofOfAddressState.hasNoData.value"
    :isRefetching="proofOfAddressState.isRefetching.value"
    :errorConfig="proofOfAddressState.errorConfig.value"
    :emptyConfig="proofOfAddressState.emptyConfig.value"
  >
    <template v-if="proofOfAddressData">
      <div class="flex justify-between items-center mt-8">
        <div class="flex items-center gap-2">
          <DaryaOutlineArrowLeftLongIcon
            class="text-neutral-6 cursor-pointer"
            @click="handleGoBack"
          />
          <span class="text-lg font-bold">Proof of Address </span>
          <span class="text-lg text-primary-shade3">{{ name }}</span>
        </div>

        <div class="flex gap-4" v-if="isEditable">
          <DButton
            label="Reject"
            styleType="error"
            size="extraSmall"
            @click="isOpenInsertRejectionReasonDialog = true"
          />
          <DButton
            label="Approve"
            styleType="success"
            size="extraSmall"
            @click="isOpenConfirmDialog = true"
          />
        </div>

        <div v-else class="flex items-center gap-3">
          <span>Verification Status:</span>
          <DChip
            variant="outline"
            :color="
              getStatusInfo(proofOfAddressData?.verificationStatusTypeId).color
            "
            :label="
              getStatusInfo(proofOfAddressData?.verificationStatusTypeId).text
            "
          />
          <span
            v-if="proofOfAddressData?.verificationStatusTypeId === 5"
            class="font-bold text-[13px] text-primary ps-1 cursor-pointer"
            @click="isOpenRejectionReasonDialog = true"
          >
            See why?
          </span>

          <DPopper
            v-if="proofOfAddressData?.verificationStatusTypeId === 4"
            v-model="isOpenRejectDropDown"
            placement="bottom-end"
            :arrow="false"
            offset-y="11"
            append-to-body
          >
            <template #default>
              <DaryaOutlineHorizentalMoreIcon
                class="cursor-pointer text-neutral-6"
              />
            </template>

            <template #content>
              <div
                class="flex justify-center bg-white shadow-lg rounded-lg p-3 w-20 text-left"
              >
                <span
                  class="text-error-shade1 text-sm cursor-pointer"
                  @click="isOpenInsertRejectionReasonDialog = true"
                >
                  Reject
                </span>
              </div>
            </template>
          </DPopper>
        </div>
      </div>

      <div class="flex mt-7">
        <div
          class="flex flex-col me-11 gap-5 rounded-2xl border border-neutral-13 px-6 h-[22.3rem] py-4 w-72"
        >
          <DInput
            label="Country of Residence"
            :value="proofOfAddressData?.residenceCountryName"
            disabled
          />
          <DInput label="City" :value="proofOfAddressData?.city" disabled />
          <DInput
            label="Postal Code"
            :value="proofOfAddressData?.postalCode"
            disabled
          />
          <DInput
            label="Address"
            :value="proofOfAddressData?.address"
            disabled
          />
        </div>

        <div class="flex flex-col gap-3 w-full">
          <!-- No document state -->
          <div
            v-if="!proofOfAddressData?.document"
            class="relative flex flex-col justify-center items-center rounded-2xl border border-neutral-13 px-6 py-8 text-center"
          >
            <img
              src="@/assets/images/empty-state.svg"
              alt="No document"
              class="w-64 h-64 object-contain"
            />
            <span class="text-neutral-5 text-sm"
              >No verification documents submitted yet.</span
            >
          </div>

          <!-- Document preview -->
          <div
            v-else
            class="relative rounded-2xl border border-neutral-13 px-6 py-4"
          >
            <!-- Image loading state -->
            <div
              v-if="!previewSrc"
              class="flex items-center justify-center h-[40rem] bg-neutral-12 rounded-lg"
            >
              <div class="animate-pulse text-neutral-5">Loading preview...</div>
            </div>

            <!-- Actual image -->
            <img
              v-else
              :src="previewSrc"
              alt="Preview"
              class="object-cover w-full h-[40rem] rounded-lg"
              @load="imageLoaded = true"
              @error="imageError = true"
              :class="{ 'opacity-0': !imageLoaded && !imageError }"
            />

            <!-- Error state -->
            <div
              v-if="imageError"
              class="flex items-center justify-center h-[40rem] bg-neutral-12 rounded-lg"
            >
              <span class="text-error">Failed to load preview</span>
            </div>

            <button
              v-if="previewSrc"
              class="flex items-center py-1 px-2 rounded-lg text-white absolute bottom-8 right-8 bg-neutral/30"
              @click="handleDownloadDocument(proofOfAddressData?.document)"
            >
              <span class="text-xs pe-1 cursor-pointer">Download</span>
              <DaryaOutlineImportIcon size="16" />
            </button>
          </div>
        </div>
      </div>
    </template>
  </DataStateWrapper>
  <ConfirmDialog
    :isOpenConfirmDialog="isOpenConfirmDialog"
    @update:isOpenConfirmDialog="isOpenConfirmDialog = $event"
    @close="isOpenConfirmDialog = false"
    @submitForm="submitConfirm"
  />
  <InsertRejectionReasonDialog
    :isOpenInsertRejectionReasonDialog="isOpenInsertRejectionReasonDialog"
    @update:isOpenInsertRejectionReasonDialog="
      isOpenInsertRejectionReasonDialog = $event
    "
    @close="isOpenInsertRejectionReasonDialog = false"
    @submitForm="submitReject"
    title="Proof Of Address"
  />
  <RejectionReasonDialog
    :isOpenRejectionReasonDialog="isOpenRejectionReasonDialog"
    @update:isOpenRejectionReasonDialog="isOpenRejectionReasonDialog = $event"
    :message="proofOfAddressData?.message"
    @close="isOpenRejectionReasonDialog = false"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, reactive } from 'vue';
import {
  useGeProofAddressById,
  getDocumentPreviewUrl,
  isDocumentObject,
  getMimeTypeFromExtension,
  RejectionReasonDialog,
  type DocumentObject,
  ConfirmDialog,
  InsertRejectionReasonDialog,
  useUpdateUserKycStatusMutation,
} from '@/modules/kyc';

import { download } from '@libs/utils';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const emit = defineEmits<{
  (e: 'handleBackButton'): void;
}>();

const isEditable = computed(() => {
  const status = proofOfAddressData.value?.verificationStatusTypeId;
  return status === 3;
});
const props = defineProps<{
  name?: string;
  userId: string;
  actionId: number;
}>();

const {
  proofOfAddressData,
  getProofOfAddressData,
  status,
  isFetching,
  isError,
  error,
} = useGeProofAddressById(props.userId, props.actionId, true);
const { updateUserKycStatus } = useUpdateUserKycStatusMutation({
  onSuccess: () => {
    getProofOfAddressData();
  },
  onError: (error) => {
    console.log('Error update document:', error);
  },
});

const proofOfAddressState = useDataState(
  status,
  proofOfAddressData,
  isFetching,
  isError,
  error,
  'verification-detail'
);
const imageLoaded = ref(false);
const imageError = ref(false);
const isOpenRejectDropDown = ref(false);
const isOpenInsertRejectionReasonDialog = ref(false);
const isOpenRejectionReasonDialog = ref(false);
const isOpenConfirmDialog = ref(false);
const previewSrc = computed(() => {
  if (!proofOfAddressData.value?.document) return null;
  return getDocumentPreviewUrl(proofOfAddressData.value.document);
});

watch(previewSrc, () => {
  imageLoaded.value = false;
  imageError.value = false;
});

const handleGoBack = () => {
  emit('handleBackButton');
};

const handleDownloadDocument = (documentData: DocumentObject | string) => {
  if (!documentData) return;

  if (isDocumentObject(documentData)) {
    // New format: DocumentObject
    const mimeType = getMimeTypeFromExtension(documentData.fileExtension);
    const dataUrl = `data:${mimeType};base64,${documentData.fileData}`;
    download(documentData.fileName, dataUrl);
  } else if (typeof documentData === 'string') {
    // Legacy format: base64 string
    const fileName = 'proof_of_address.jpg';
    const dataUrl = documentData.startsWith('data:')
      ? documentData
      : `data:image/jpeg;base64,${documentData}`;
    download(fileName, dataUrl);
  }
};

// Status mapping with both text and color
const STATUS_MAP = {
  4: { text: 'Approved', color: 'success' },
  3: { text: 'Pending Approval', color: 'warning' },
  2: { text: 'Required', color: 'primary' },
  5: { text: 'Rejected', color: 'error' },
  1: { text: 'Not Required', color: 'neutral' },
};

const getStatusInfo = (status: number | undefined | null) => {
  if (status) {
    return STATUS_MAP[status] || { text: 'Unknown', color: 'neutral' };
  }
  return { text: 'Unknown', color: 'neutral' };
};

const formModel = reactive({
  userId: props.userId,
  actionId: props.actionId,
  userKycStatus: 0,
  kycReasonMessage: '',
});

const submitConfirm = () => {
  formModel.userKycStatus = 3;
  formModel.kycReasonMessage = '';
  updateUserKycStatus(formModel);
  isOpenConfirmDialog.value = false;
};

const submitReject = (reason: string) => {
  formModel.userKycStatus = 5;
  formModel.kycReasonMessage = reason;

  updateUserKycStatus(formModel);

  isOpenInsertRejectionReasonDialog.value = false;
};

onMounted(() => {
  getProofOfAddressData();
});
</script>
