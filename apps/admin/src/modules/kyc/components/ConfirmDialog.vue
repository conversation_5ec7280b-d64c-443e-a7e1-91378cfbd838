<template>
  <DDialog
    :open="isOpenConfirmDialog"
    @update:open="handleClose"
    :closeOnClickOutside="false"
    :showCloseIcon="false"
  >
    <template #body>
      <div class="flex flex-col items-center">
        <div class="p-1 rounded-lg bg-info-tint3">
          <DaryaBoldTickCircleIcon size="32" class="text-info-shade1" />
        </div>

        <span class="text-lg font-bold pt-5">
          Confirm Document Submission
        </span>
        <span class="text-[10px] text-neutral-5 pt-1 w-64 text-center">
          Are you sure you want to approve this submission?
        </span>

        <div class="flex pt-6 gap-2">
          <DButton
            class="w-24"
            variant="outline"
            styleType="secondary"
            size="extraSmall"
            label="No"
            @click="handleClose"
          />
          <DButton
            styleType="info"
            size="extraSmall"
            label="Yes"
            class="w-24"
            @click="onSubmit"
          />
        </div>
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
defineProps<{
  isOpenConfirmDialog: boolean;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submitForm'): void;
}>();

const handleClose = () => {
  emit('close');
};

const onSubmit = () => {
  emit('submitForm');
};
</script>
