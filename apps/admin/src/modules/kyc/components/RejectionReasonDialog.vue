<template>
  <DDialog
    :open="isOpenRejectionReasonDialog"
    @update:open="handleClose"
    :closeOnClickOutside="false"
    :showCloseIcon="false"
  >
    <template #body>
      <div class="flex flex-col items-center">
        <div class="p-1 rounded-lg bg-error-tint3">
          <DaryaBoldCloseCircleIcon size="32" class="text-error-shade1" />
        </div>

        <span class="text-[10px] text-neutral-5 pt-5">
          {{ message }}
        </span>

        <DButton
          label="Ok"
          size="extraSmall"
          styleType="error"
          @click="handleClose"
          class="mt-5 w-[5.5rem]"
        />
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
defineProps<{
  isOpenRejectionReasonDialog: boolean;
  message?: string;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const handleClose = () => {
  emit('close');
};
</script>
