<template>
  <DataStateWrapper
    :isLoading="auditLogState.isLoading.value"
    :hasError="auditLogState.hasError.value"
    :hasNoData="auditLogState.hasNoData.value"
    :isRefetching="auditLogState.isRefetching.value"
    :errorConfig="auditLogState.errorConfig.value"
    :emptyConfig="auditLogState.emptyConfig.value"
  >
    <div
      class="py-4 border-2 border-neutral-13 rounded-2xl mt-11"
      v-if="auditLogData"
    >
      <div class="flex items-center gap-2 ps-6">
        <DaryaOutlineArrowLeftLongIcon
          class="text-neutral-6 cursor-pointer"
          @click="handleGoBack"
        />
        <span class="text-lg font-bold">Audit Log</span>
        <span class="text-lg text-primary-shade3">{{ name }}</span>
      </div>

      <DBaseTable
        :columns="columns"
        :data="auditLogData"
        :enable-pagination="true"
        :showRowsPerPageSelect="true"
        class="mt-7"
      >
        <template #column-row="{ index }">
          {{ index + 1 }}
        </template>
        <template #column-role="{ row }">
          <div class="flex justify-center items-center">
            <div class="w-2 h-2 rounded-full bg-primary-shade3" />
            <span class="text-neutral-5 ps-3">{{ row.role }}</span>
          </div>
        </template>
        <template #column-name="{ row }">
          <div class="flex text-left flex-col">
            <span class="text-xs font-bold">{{ row.name }}</span>
            <span class="text-xs text-neutral-3">{{ row.email }}</span>
          </div>
        </template>
        <template #column-lastUpdated="{ row }">
          {{ d(row.lastUpdated) }}
        </template>
        <template #column-actionType="{ row }">
          <DChip
            variant="outline"
            :color="getStatusInfo(row.actionType).color"
            :label="getStatusInfo(row.actionType).text"
          />
        </template>
      </DBaseTable>
    </div>
  </DataStateWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { useGetAuditLogByUserId } from '@/modules/kyc';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const emit = defineEmits<{
  (e: 'handleBackButton'): void;
}>();
const props = defineProps<{
  name?: string;
  userId: string;
}>();

const { auditLogData, status, isFetching, isError, error } =
  useGetAuditLogByUserId(props.userId, true);
const { d } = useI18n();

const auditLogState = useDataState(
  status,
  auditLogData,
  isFetching,
  isError,
  error,
  'verification-detail'
);
const columns = ref([
  { key: 'row', label: 'Row' },
  { key: 'userRoleName', label: 'Role' },
  { key: 'name', label: 'Name' },
  { key: 'actionType', label: 'Action Type' },
  { key: 'affectedSection', label: 'Affected Section' },
  { key: 'lastUpdated', label: 'Last Update' },
  { key: 'additionalInfo', label: 'Additional Info' },
]);

// Action type mapping with both text and color
const ACTION_TYPE_INFO_MAP = {
  1: { text: 'Created', color: 'primary' },
  2: { text: 'Updated', color: 'warning' },
  3: { text: 'Approved', color: 'success' },
  4: { text: 'Rejected', color: 'error' },
  5: { text: 'Deleted', color: 'neutral' },
};

const getStatusInfo = (status: number) => {
  return ACTION_TYPE_INFO_MAP[status] || { text: 'Unknown', color: 'neutral' };
};
const handleGoBack = () => {
  emit('handleBackButton');
};
</script>
