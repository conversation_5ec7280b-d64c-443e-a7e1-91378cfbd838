<template>
  <DDialog
    :open="isOpenInsertRejectionReasonDialog"
    @update:open="handleClose"
    :closeOnClickOutside="false"
    :showCloseIcon="false"
    maxWidth="md"
  >
    <template #body>
      <div class="flex">
        <div class="flex rounded bg-error-tint3 p-2 me-4">
          <DaryaBoldInfoCircleIcon class="text-error-shade1" size="32" />
        </div>
        <div class="flex flex-col">
          <span class="text-lg font-bold pb-1"
            >Reject {{ title }} Verification</span
          >
          <span class="text-neutral-5 text-[10px]"
            >Please Provide a reason for rejecting this document</span
          >
        </div>
      </div>

      <div class="py-12 px-2">
        <DInput
          placeholder="Enter rejection reason..."
          v-model="reasonDescription"
        />
      </div>
      <div class="flex justify-center gap-2 pb-4">
        <DButton
          label="Cancel"
          size="extraSmall"
          variant="outline"
          styleType="error"
          @click="handleClose"
        />
        <DButton
          label="Confirm Rejection"
          size="extraSmall"
          styleType="error"
          @click="onSubmit"
        />
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineProps<{
  isOpenInsertRejectionReasonDialog: boolean;
  title?: string;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submitForm', reason: string): void;
}>();

const reasonDescription = ref('');

const handleClose = () => {
  emit('close');
};
const onSubmit = () => {
  emit('submitForm', reasonDescription.value);

  reasonDescription.value = '';
};
</script>
