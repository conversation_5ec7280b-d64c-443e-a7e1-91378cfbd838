<template>
  <DataStateWrapper
    :isLoading="proofOfIdentificationState.isLoading.value"
    :hasError="proofOfIdentificationState.hasError.value"
    :hasNoData="proofOfIdentificationState.hasNoData.value"
    :isRefetching="proofOfIdentificationState.isRefetching.value"
    :errorConfig="proofOfIdentificationState.errorConfig.value"
    :emptyConfig="proofOfIdentificationState.emptyConfig.value"
  >
    <template v-if="proofOfIdentificationData">
      <div class="flex justify-between items-center mt-8">
        <div class="flex items-center gap-2">
          <DaryaOutlineArrowLeftLongIcon
            class="text-neutral-6 cursor-pointer"
            @click="handleGoBack"
          />
          <span class="text-lg font-bold">Proof of Identification </span>
          <span class="text-lg text-primary-shade3">{{ name }}</span>
        </div>

        <div class="flex gap-4" v-if="isEditable">
          <DButton
            label="Reject"
            styleType="error"
            size="extraSmall"
            @click="isOpenInsertRejectionReasonDialog = true"
          />
          <DButton
            label="Approve"
            styleType="success"
            size="extraSmall"
            @click="isOpenConfirmDialog = true"
          />
        </div>

        <div v-else class="flex items-center gap-3">
          <span>Verification Status:</span>
          <DChip
            variant="outline"
            :color="
              getStatusInfo(proofOfIdentificationData?.verificationStatusTypeId)
                .color
            "
            :label="
              getStatusInfo(proofOfIdentificationData?.verificationStatusTypeId)
                .text
            "
          />

          <span
            v-if="proofOfIdentificationData?.verificationStatusTypeId === 5"
            class="font-bold text-[13px] text-primary ps-1 cursor-pointer"
            @click="isOpenRejectionReasonDialog = true"
          >
            See why?
          </span>

          <DPopper
            v-else-if="
              proofOfIdentificationData?.verificationStatusTypeId === 4
            "
            v-model="isOpenRejectDropDown"
            placement="bottom-end"
            :arrow="false"
            offset-y="11"
            append-to-body
          >
            <template #default>
              <DaryaOutlineHorizentalMoreIcon
                class="cursor-pointer text-neutral-6"
              />
            </template>

            <template #content>
              <div
                class="flex justify-center bg-white shadow-lg rounded-lg p-3 w-20 text-left"
              >
                <span
                  class="text-error-shade1 text-sm cursor-pointer"
                  @click="isOpenInsertRejectionReasonDialog = true"
                >
                  Reject
                </span>
              </div>
            </template>
          </DPopper>
        </div>
      </div>

      <div class="flex mt-7">
        <div
          class="flex flex-col me-11 gap-5 rounded-2xl border border-neutral-13 px-6 h-[27rem] py-4 w-72"
        >
          <DInput
            label="First Name"
            :value="proofOfIdentificationData?.firstName"
            disabled
          />
          <DInput
            label="Last Name"
            :value="proofOfIdentificationData?.lastName"
            disabled
          />
          <DInput label="Gender" :value="genderLabel" disabled />
          <DInput
            label="Country Of Birth"
            :value="proofOfIdentificationData?.birthCountryName"
            disabled
          />
          <DInput label="Date Of Birth" :value="dateOfBirth" disabled />
        </div>

        <div class="flex flex-col gap-3 w-full">
          <!-- Show message if no documents are available -->
          <div
            v-if="availableDocuments.length === 0"
            class="relative flex flex-col justify-center items-center rounded-2xl border border-neutral-13 px-6 py-8 text-center"
          >
            <img
              src="@/assets/images/empty-state.svg"
              alt="No document"
              class="w-64 h-64 object-contain"
            />
            <span class="text-neutral-5"
              >No verification documents submitted yet.</span
            >
          </div>

          <!-- Render each available document -->
          <div
            v-for="document in availableDocuments"
            :key="document.fieldName"
            class="relative rounded-2xl border border-neutral-13 px-6 py-4"
          >
            <img
              v-if="document.previewSrc"
              :src="document.previewSrc"
              :alt="document.label"
              class="w-full rounded-lg h-[25.5rem] object-cover"
            />
            <button
              class="flex items-center py-1 px-2 rounded-lg text-white absolute bottom-8 right-8 bg-neutral/30"
              @click="handleDownloadDocument(document.data, document.label)"
            >
              <span class="text-xs pe-1 cursor-pointer">Download</span>
              <DaryaOutlineImportIcon size="16" />
            </button>
          </div>
        </div>
      </div>
    </template>
  </DataStateWrapper>

  <ConfirmDialog
    :isOpenConfirmDialog="isOpenConfirmDialog"
    @update:isOpenConfirmDialog="isOpenConfirmDialog = $event"
    @close="isOpenConfirmDialog = false"
    @submitForm="submitConfirm"
  />
  <InsertRejectionReasonDialog
    :isOpenInsertRejectionReasonDialog="isOpenInsertRejectionReasonDialog"
    @update:isOpenInsertRejectionReasonDialog="
      isOpenInsertRejectionReasonDialog = $event
    "
    @close="isOpenInsertRejectionReasonDialog = false"
    @submitForm="submitReject"
    title="Proof Of Identification"
  />
  <RejectionReasonDialog
    :isOpenRejectionReasonDialog="isOpenRejectionReasonDialog"
    @update:isOpenRejectionReasonDialog="isOpenRejectionReasonDialog = $event"
    :message="proofOfIdentificationData?.message"
    @close="isOpenRejectionReasonDialog = false"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue';
import { download } from '@libs/utils';
import { useI18n } from 'vue-i18n';

import {
  useGeProofIdentificationById,
  getDocumentPreviewUrl,
  isDocumentObject,
  getMimeTypeFromExtension,
  type DocumentObject,
  InsertRejectionReasonDialog,
  ConfirmDialog,
  RejectionReasonDialog,
  useUpdateUserKycStatusMutation,
} from '@/modules/kyc';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const emit = defineEmits<{
  (e: 'handleBackButton'): void;
}>();
const props = defineProps<{
  name?: string;
  userId: string;
  actionId: number;
}>();

const {
  proofOfIdentificationData,
  getProofOfIdentificationData,
  isFetching,
  isError,
  error,
  status,
} = useGeProofIdentificationById(props.userId, props.actionId);
const { updateUserKycStatus } = useUpdateUserKycStatusMutation({
  onSuccess: () => {
    getProofOfIdentificationData();
  },
  onError: (error) => {
    console.log('Error update document:', error);
  },
});

const proofOfIdentificationState = useDataState(
  status,
  proofOfIdentificationData,
  isFetching,
  isError,
  error,
  'verification-detail'
);
const isOpenInsertRejectionReasonDialog = ref(false);
const isOpenRejectionReasonDialog = ref(false);
const isOpenRejectDropDown = ref(false);
const isOpenConfirmDialog = ref(false);
const handleGoBack = () => {
  emit('handleBackButton');
};

onMounted(() => {
  getProofOfIdentificationData();
});

// Document field mappings with user-friendly labels
const documentFields = [
  { fieldName: 'passport', label: 'Passport' },
  { fieldName: 'frontOfIdCard', label: 'Front of ID Card' },
  { fieldName: 'backOfIdCard', label: 'Back of ID Card' },
  { fieldName: 'frontOfDrivingLicense', label: 'Front of Driving License' },
  { fieldName: 'backOfDrivingLicense', label: 'Back of Driving License' },
] as const;

// Computed property to get all available documents with their preview sources
const availableDocuments = computed(() => {
  if (!proofOfIdentificationData.value) return [];

  return documentFields
    .map(({ fieldName, label }) => {
      const documentData = proofOfIdentificationData.value?.[fieldName];

      if (!documentData) return null;

      const previewSrc = getDocumentPreviewUrl(documentData);

      if (!previewSrc) return null;

      return {
        fieldName,
        label,
        data: documentData,
        previewSrc,
      };
    })
    .filter((doc): doc is NonNullable<typeof doc> => doc !== null);
});
const isEditable = computed(() => {
  const status = proofOfIdentificationData.value?.verificationStatusTypeId;

  return status === 3;
});
const handleDownloadDocument = (
  documentData: DocumentObject | string,
  label: string
) => {
  if (!documentData) return;

  if (isDocumentObject(documentData)) {
    // New format: DocumentObject
    const mimeType = getMimeTypeFromExtension(documentData.fileExtension);
    const dataUrl = `data:${mimeType};base64,${documentData.fileData}`;
    download(documentData.fileName, dataUrl);
  } else if (typeof documentData === 'string') {
    // Legacy format: base64 string
    const fileName = `${label.replace(/\s+/g, '_').toLowerCase()}.jpg`;
    const dataUrl = documentData.startsWith('data:')
      ? documentData
      : `data:image/jpeg;base64,${documentData}`;
    download(fileName, dataUrl);
  }
};

const { d } = useI18n();
const dateOfBirth = computed(() => {
  if (!proofOfIdentificationData.value?.dateOfBirth) return '';
  return d(proofOfIdentificationData.value?.dateOfBirth, 'shortDate');
});
const genderLabel = computed(() => {
  const id = proofOfIdentificationData.value?.genderTypeId;
  if (id === 0) return 'Man';
  if (id === 1) return 'Female';
  return 'Other';
});
// Status mapping with both text and color
const STATUS_MAP = {
  4: { text: 'Approved', color: 'success' },
  3: { text: 'Pending Approval', color: 'warning' },
  2: { text: 'Required', color: 'primary' },
  5: { text: 'Rejected', color: 'error' },
  1: { text: 'Not Required', color: 'neutral' },
};

const getStatusInfo = (status: number | undefined | null) => {
  if (status) {
    return STATUS_MAP[status] || { text: 'Unknown', color: 'neutral' };
  }
  return { text: 'Unknown', color: 'neutral' };
};

const formModel = reactive({
  userId: props.userId,
  actionId: props.actionId,
  userKycStatus: 0,
  kycReasonMessage: '',
});

const submitConfirm = () => {
  formModel.userKycStatus = 3;
  formModel.kycReasonMessage = '';

  updateUserKycStatus(formModel);

  isOpenConfirmDialog.value = false;
};

const submitReject = (reason: string) => {
  formModel.userKycStatus = 5;
  formModel.kycReasonMessage = reason;

  updateUserKycStatus(formModel);

  isOpenInsertRejectionReasonDialog.value = false;
};
</script>
