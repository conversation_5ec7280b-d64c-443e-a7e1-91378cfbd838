import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import { ApiResponse, ProofOfIdentification } from '../types';
import { getProofOfIdentificationById } from '../request';
import { QUERY_KEYS } from '../constants';

export default function useGeProofIdentificationById(
  userId: MaybeRef<string> | undefined,
  actionId: MaybeRef<number> | null,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const actionIdRef = toRef(actionId);
  const shouldFetchRef = toRef(shouldFetch);

  const queryKey = computed(() =>
    userIdRef.value
      ? QUERY_KEYS.PROOF_IDENTIFICATION_DATA(userIdRef.value)
      : ['proofIdentificationData', null]
  );

  const { data, refetch, isError, status, isFetching, error } = useQuery<
    ApiResponse<ProofOfIdentification>,
    Error
  >({
    queryKey,
    queryFn: () => {
      const userId = userIdRef.value;
      const actionId = actionIdRef.value;

      if (!actionId || !userId) {
        throw new Error('Action ID and User ID is required');
      }

      return getProofOfIdentificationById(userId, actionId);
    },
    enabled: shouldFetchRef,
  });

  const proofOfIdentificationData = computed(() => data.value?.data ?? null);

  const hasData = computed(() => !!proofOfIdentificationData.value);

  const isEnabled = computed(() => shouldFetchRef.value && !!actionIdRef.value);

  return {
    proofOfIdentificationData,
    isError,
    hasData,
    isEnabled,
    error,
    status,
    isFetching,
    refetch,
    getProofOfIdentificationData: refetch,
  } as const;
}
