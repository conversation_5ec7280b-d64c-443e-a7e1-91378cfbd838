import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import { ApiResponse, AuditLog } from '../types';
import { getAuditLog } from '../request';
import { QUERY_KEYS } from '../constants';

export default function useGetAuditLogByUserId(
  userId: MaybeRef<string> | undefined,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const shouldFetchRef = toRef(shouldFetch);

  const queryKey = computed(() =>
    userIdRef.value ? QUERY_KEYS.AUDIT_LOG(userIdRef.value) : ['auditLog', null]
  );

  const { data, refetch, isError, status, isFetching, error } = useQuery<
    ApiResponse<AuditLog>,
    Error
  >({
    queryKey,
    queryFn: () => {
      const userId = userIdRef.value;

      if (!userId) {
        throw new Error('User ID is required');
      }

      return getAuditLog(userId);
    },
    enabled: computed(() => shouldFetchRef.value && !!userIdRef.value),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  const auditLogData = computed(() => data.value?.data ?? null);

  const isLoading = computed(
    () => status.value === 'pending' || isFetching.value
  );

  const hasData = computed(() => !!auditLogData.value);

  const isEnabled = computed(() => shouldFetchRef.value && !!userIdRef.value);

  return {
    auditLogData,
    isLoading,
    isError,
    hasData,
    isEnabled,
    error,
    status,
    isFetching,
    refetch,
    getAuditLogData: refetch,
  } as const;
}
