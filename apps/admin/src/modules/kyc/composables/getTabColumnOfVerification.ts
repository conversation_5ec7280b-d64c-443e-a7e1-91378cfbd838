import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import type { ApiResponse, TabOfVerification } from '../types';
import { getTabColumnVerification } from '../request';
import { QUERY_KEYS } from '../constants';

export default function useGetTabColumnOfVerificationById(
  userId: MaybeRef<string> | undefined,
  workflowId: MaybeRef<number> | null,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const workflowIdRef = toRef(workflowId);
  const shouldFetchRef = toRef(shouldFetch);

  const queryKey = computed(() =>
    userIdRef.value
      ? QUERY_KEYS.TAB_OF_VERIFICATION(userIdRef.value)
      : ['tabOfVerification', null]
  );

  const { data, refetch, isError, status, isFetching, error } = useQuery<
    ApiResponse<TabOfVerification>,
    Error
  >({
    queryKey,
    queryFn: () => {
      const userId = userIdRef.value;
      const workflowId = workflowIdRef.value;

      return getTabColumnVerification(userId, workflowId);
    },
    enabled: shouldFetchRef,
  });

  const tabColumnData = computed(() => data.value?.data ?? null);

  const isLoading = computed(
    () => status.value === 'pending' || isFetching.value
  );

  const hasData = computed(() => !!tabColumnData.value);

  return {
    tabColumnData,
    isLoading,
    isError,
    hasData,
    error,
    status,
    isFetching,
    refetch,
    getTabColumnVerificationData: refetch,
  } as const;
}
