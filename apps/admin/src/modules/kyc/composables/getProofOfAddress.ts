import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import type { ApiResponse, ProofOfAddress } from '../types';
import { getProofOfAddressById } from '../request';
import { QUERY_KEYS } from '../constants';

export default function useGeProofAddressById(
  userId: MaybeRef<string> | undefined,
  actionId: MaybeRef<number> | null,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const actionIdRef = toRef(actionId);
  const shouldFetchRef = toRef(shouldFetch);

  const queryKey = computed(() =>
    userIdRef.value
      ? QUERY_KEYS.PROOF_ADDRESS_DATA(userIdRef.value)
      : ['proofAddressData', null]
  );

  const { data, refetch, isError, status, isFetching, error } = useQuery<
    ApiResponse<ProofOfAddress>,
    Error
  >({
    queryKey,
    queryFn: () => {
      const userId = userIdRef.value;
      const actionId = actionIdRef.value;

      if (!actionId || !userId) {
        throw new Error('Action ID and User ID is required');
      }

      return getProofOfAddressById(userId, actionId);
    },
    enabled: computed(() => shouldFetchRef.value && !!actionIdRef.value),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  const proofOfAddressData = computed(() => data.value?.data ?? null);

  const hasData = computed(() => !!proofOfAddressData.value);

  const isEnabled = computed(() => shouldFetchRef.value && !!actionIdRef.value);

  return {
    proofOfAddressData,
    isError,
    hasData,
    isEnabled,
    error,
    status,
    isFetching,
    refetch,
    getProofOfAddressData: refetch,
  } as const;
}
