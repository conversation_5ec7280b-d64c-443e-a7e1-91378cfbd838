import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getVerifications } from '../request';

export default function useGetVerification(
  shouldFetch: MaybeRef<boolean> = false
) {
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['verifications']),
    queryFn: () => {
      return getVerifications();
    },
    enabled: shouldFetchRef,
  });

  const verificationData = computed(() => data.value?.data ?? []);

  return {
    getUserVerification: refetch,
    verificationData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
