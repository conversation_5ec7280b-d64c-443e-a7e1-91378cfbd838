import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';
import type { UpdateUserKyc, ApiResponse } from '../types';
import { updateKycStatus } from '../request';

export interface UseUpdateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<UpdateUserKyc>>,
    'mutationFn'
  > {}

export default function useUpdateUserKycStatusMutation(
  options?: UseUpdateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<UpdateUserKyc>
  >({
    mutationFn: async (formModel) => {
      try {
        return await updateKycStatus(unref(formModel));
      } catch (err: any) {
        const message =
          err.response?.data?.message ||
          err.message ||
          'An unexpected error occurred';
        throw new Error(message);
      }
    },
    ...options,
  });

  return {
    updateUserKycStatus: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
