export enum VerificationStatusTypeId {
  PENDING = 1,
  IN_REVIEW = 2,
  UNDER_REVIEW = 3,
  APPROVED = 4,
  REJECTED = 5,
}
export type StatusColorMap = {
  [key in VerificationStatusTypeId]:
    | 'success'
    | 'warning'
    | 'primary'
    | 'error'
    | 'neutral';
};

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface TabOfVerification {
  fullName?: string;
  kycColumn: [{ actionId?: number; title?: string }];
}

export interface AuditLogType {
  userRoleName?: string;
  fullName?: string;
  email?: string;
  actionId?: 0;
  actionType?: 4;
  affectedSection?: 2;
  lastUpdated?: string;
  additionalInfo?: string;
}

export interface ProofOfAddress {
  city?: string;
  postalCode?: string;
  address?: string;
  residenceCountryId?: number | null;
  residenceCountryName?: string;
  message?: string;
  document?: DocumentObject | string;
  verificationStatusTypeId?: number | null;
}
export interface ProofOfIdentification {
  firstName?: string;
  lastName?: string;
  genderTypeId?: number | null;
  dateOfBirth?: string;
  birthCountryId?: number;
  birthCountryName: string;
  message?: string;
  proofOfIdentificationTypeId?: number | null;
  verificationStatusTypeId?: number | null;
  passport: DocumentObject | string | null;
  frontOfIdCard: DocumentObject | string | null;
  backOfIdCard: DocumentObject | string | null;
  frontOfDrivingLicense: DocumentObject | string | null;
  backOfDrivingLicense: DocumentObject | string | null;
}

export interface AuditLog {
  userRoleName?: string;
  fullName?: string;
  email?: string;
  actionId?: number | null;
  actionType?: number | null;
  affectedSection?: number | null;
  lastUpdated?: string;
  additionalInfo?: string;
}
export interface DocumentObject {
  fileData: string;
  fileName: string;
  fileExtension: string;
}

export interface UpdateUserKyc {
  userId?: string;
  actionId?: number | null;
  userKycStatus?: number | null;
  kycReasonMessage?: string;
}
