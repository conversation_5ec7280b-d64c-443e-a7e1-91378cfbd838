import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllTransaction } from '../request';

type transactionResponse = Awaited<ReturnType<typeof getAllTransaction>>;

export default function useGetAllTransaction() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    transactionResponse,
    Error
  >({
    queryKey: ['transactionList'],
    queryFn: getAllTransaction,
  });

  const transactionList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllTransaction: refetch,
    transactionList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
