<template>
  <DDialog
    :open="isOpenDeleteUser"
    title="Delete User"
    :closeOnClickOutside="false"
    @update:open="emit('close')"
    class="max-w-2xl"
  >
    <template #body>
      <div class="flex">
        <span class="text-sm font-medium">
          Are you sure you want to permanently delete this user?
        </span>
      </div>

      <div class="flex flex-row-reverse mt-4 gap-4">
        <DButton
          variant="outline"
          styleType="secondray"
          label="Delete User"
          @click="handleDelete"
        />

        <DButton
          variant="outline"
          styleType="primary"
          label="Cancel"
          @click="emit('close')"
        />
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { useDeleteUserById } from '@/modules/user/index';
import { useToast } from '@libs/darya-design-system';

const emit = defineEmits(['close']);

const props = defineProps<{ isOpenDeleteUser: boolean; userId: string }>();

const { addToast } = useToast();

const handleSuccess = () => {
  emit('close');

  addToast({
    message: 'The user deleted!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });
};
const handleError = () => {
  emit('close');

  addToast({
    message: 'The user has not deleted!',
    type: 'error',
    position: 'right-top',
    duration: 3000,
  });
};

const { deleteUserById } = useDeleteUserById({
  onSuccess: handleSuccess,
  onError: handleError,
});

const handleDelete = () => {
  deleteUserById(props.userId);
};
</script>
