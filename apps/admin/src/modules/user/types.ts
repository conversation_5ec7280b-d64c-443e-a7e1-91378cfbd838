export interface UserValidateOtp {
  userId: string;
  otp: string;
}

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface UserCredentials {
  username: string;
  password: string;
}

export interface UserUpdateFormModel {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumberWithoutExt: string;
  residenceCountryId: number;
  isActive: boolean;
  address: string;
  city: string;
  postalCode: string;
  roleIds: string[];
}

export interface UserFormModel {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumberWithoutExt: string;
  residenceCountryId: number;
  isActive: boolean;
  address: string;
  city: string;
  postalCode: string;
  roleIds: string[];
  permissionKeys: string[];
  password: string;
}

export interface User {
  jti: string;
  nameid: string;
  email: string;
  given_name: string;
  family_name: string;
  role: string;
  exp: number;
}
