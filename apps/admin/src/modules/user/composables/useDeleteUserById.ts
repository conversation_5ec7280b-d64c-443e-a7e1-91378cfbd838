import { useMutation } from '@tanstack/vue-query';

import { deleteUser } from '../request';

type DeleteUserResponse = Awaited<ReturnType<typeof deleteUser>>;

export default function useDeleteUserById(options?: {
  onSuccess?: (data: DeleteUserResponse) => void;
  onError?: (error: Error) => void;
}) {
  const { mutate, isError, status } = useMutation<
    DeleteUserResponse,
    Error,
    string
  >({
    mutationKey: ['deleteUser'],
    mutationFn: deleteUser,
    onSuccess: options?.onSuccess,
    onError: options?.onError,
  });

  return {
    deleteUserById: mutate,
    isError,
    status,
  } as const;
}
