import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { updateUser } from '../request';

import type { UserUpdateFormModel } from '@/modules/user';

type ResponseUpdateUser = Awaited<ReturnType<typeof updateUser>>;

export interface UseUpdateMutationOptions
  extends Omit<
    UseMutationOptions<Response, Error, MaybeRef<UserUpdateFormModel>>,
    'mutationFn'
  > {}

export default function useUpdateUserMutation(
  options?: UseUpdateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ResponseUpdateUser,
    Error,
    MaybeRef<UserUpdateFormModel>
  >({
    mutationFn: async (formModel) => {
      return updateUser(unref(formModel));
    },
    onError: (err) => {},
    ...options,
  });

  return {
    updateUser: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
