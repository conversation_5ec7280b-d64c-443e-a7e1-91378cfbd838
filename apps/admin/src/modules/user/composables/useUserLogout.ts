import { useMutation } from '@tanstack/vue-query';

import { logout } from '../request';

type LogoutResponse = Awaited<ReturnType<typeof logout>>;

export default function useUserLogoutMutation() {
  const { mutate, mutateAsync, status, isError } = useMutation<
    LogoutResponse,
    Error
  >({
    mutationFn: logout,
    onError: (error) => {},
  });

  return {
    logout: mutate,
    mutateAsync,
    isError,
    status,
  } as const;
}
