import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getRoles } from '../request';

type UserRolesResponse = Awaited<ReturnType<typeof getRoles>>;

export default function useGetRoles(enabled: MaybeRef<boolean> = true) {
  const enabledRef = toRef(enabled);

  const { status, isError, data, refetch, error } = useQuery<UserRolesResponse, Error>(
    {
      queryKey: ['roleList'],
      queryFn: getRoles,
      enabled: enabledRef,
    }
  );

  const roleList = computed(() => data.value?.data?.data ?? []);

  return {
    getRoles: refetch,
    roleList,
    isError,
    status,
    error,
  } as const;
}
