import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllUser } from '../request';

type UserDetailResponse = Awaited<ReturnType<typeof getAllUser>>;

export default function useGetAllUser() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    UserDetailResponse,
    Error
  >({
    queryKey: ['userList'],
    queryFn: getAllUser,
  });

  const userList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllUser: refetch,
    userList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
