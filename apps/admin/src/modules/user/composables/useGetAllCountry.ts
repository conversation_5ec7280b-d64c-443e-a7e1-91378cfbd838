import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllCountry } from '../request';

type CountryResponse = Awaited<ReturnType<typeof getAllCountry>>;

export default function useGetAllCountry(enabled: MaybeRef<boolean> = true) {
  const enabledRef = toRef(enabled);

  const { status, isError, data, refetch, error } = useQuery<CountryResponse, Error>({
    queryKey: ['countryList'],
    queryFn: getAllCountry,
    enabled: enabledRef,
  });

  const countryList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllCountry: refetch,
    countryList: countryList,
    isError,
    status,
    error,
  } as const;
}
