import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getUserById } from '../request';

export default function useGetUserById(
  userId: MaybeRef<string>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching } = useQuery({
    queryKey: computed(() => ['userData', userIdRef.value]),
    queryFn: () => {
      const id = userIdRef.value;

      if (!id) throw new Error('User ID is required');

      return getUserById(id);
    },
    enabled: shouldFetchRef,
  });

  const userData = computed(() => data.value?.data?.data ?? {});

  return {
    getUserById: refetch,
    userData,
    isError,
    status,
    isFetching,
  } as const;
}
