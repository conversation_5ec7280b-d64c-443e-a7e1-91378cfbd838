import http from '../../services/http/index';
import { USER_MANAGE_API } from '@/configs/env';

export function login(data) {
  return http.post(`${USER_MANAGE_API}/User/loginAsync`, data);
}

export function verifyOtp(data) {
  return http.post(`${USER_MANAGE_API}/User/validateOtpAsync`, data);
}

export function logout() {
  return http.post(`${USER_MANAGE_API}/User/LogoutAsync`);
}

export function createUser(data) {
  return http.post(`${USER_MANAGE_API}/User/CreateUserAsync`, data);
}

export function getUserById(userId) {
  return http.get(`${USER_MANAGE_API}/User/GetUserByIdAsync`, {
    params: { userId: userId },
  });
}

export function getAllUser() {
  return http.get(`${USER_MANAGE_API}/User/GetAllUsersAsync`);
}

export function getRoles() {
  return http.get(`${USER_MANAGE_API}/Role/GetRolesAsync`);
}

export function getAllCountry() {
  return http.get(`${USER_MANAGE_API}/Country/GetAllCountryAsync`);
}

export function deleteUser(userId) {
  return http.delete(`${USER_MANAGE_API}/User/DeleteUserAsync`, {
    params: { userId: userId },
  });
}

export function updateUser(data) {
  return http.put(`${USER_MANAGE_API}/User/updateUserAsync`, data);
}
