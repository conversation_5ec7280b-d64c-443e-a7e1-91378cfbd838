import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllAccount } from '../request';

type accountResponse = Awaited<ReturnType<typeof getAllAccount>>;

export default function useGetAllAccount() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<accountResponse, Error>({
    queryKey: ['accountList'],
    queryFn: getAllAccount,
  });

  const accountList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllAccount: refetch,
    accountList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
