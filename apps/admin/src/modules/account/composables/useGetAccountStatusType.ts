import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAccountStatusType } from '../request';

type accountStatusResponse = Awaited<ReturnType<typeof getAccountStatusType>>;

export default function useGetAccountStatusType() {
  const { status, isError, data, refetch } = useQuery<
    accountStatusResponse,
    Error
  >({
    queryKey: ['accountStatusType'],
    queryFn: getAccountStatusType,
  });

  const accountStatusList = computed(() => data.value?.data?.data ?? []);

  return {
    getAccountStatus: refetch,
    accountStatusList,
    isError,
    status,
  } as const;
}
