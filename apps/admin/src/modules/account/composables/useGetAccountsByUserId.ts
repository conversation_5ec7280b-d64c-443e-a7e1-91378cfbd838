import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAccountsByUserId } from '../request';

export default function useGetUserAccountsById(userId: MaybeRef<string>) {
  const userIdRef = toRef(userId);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['userAccountData', userIdRef.value]),
    queryFn: () => {
      const id = userIdRef.value;

      if (!id) throw new Error('User ID is required');

      return getAccountsByUserId(id);
    },
  });

  const userAccountsData = computed(() => data.value?.data?.data ?? {});

  return {
    getUserAccountsById: refetch,
    userAccountsData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
