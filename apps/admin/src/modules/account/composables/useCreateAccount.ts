import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { createAccount } from '../request';

import type { AccountFormModel, ApiResponse } from '@/modules/account';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<AccountFormModel>>,
    'mutationFn'
  > {}

export default function useCreateAccountMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<AccountFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await createAccount(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    createAccount: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
