import http from '../../services/http/index';
import { ACCOUNT_MANAGE_API } from '@/configs/env';

export function getAllAccount() {
  return http.get(`${ACCOUNT_MANAGE_API}/Account/GetAll`);
}

export function getAccountStatusType() {
  return http.get(`${ACCOUNT_MANAGE_API}/Account/GetAccountStatusTypeEnums`);
}

export function createAccount(data) {
  return http.post(`${ACCOUNT_MANAGE_API}/Account/Post`, data);
}
export function getAccountsByUserId(userId) {
  return http.get(`${ACCOUNT_MANAGE_API}/Account/GetAllAccountByUserId`, {
    params: { userId: userId },
  });
}
