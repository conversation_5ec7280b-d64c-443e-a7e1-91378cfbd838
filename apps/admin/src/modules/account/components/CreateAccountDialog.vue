<template>
  <DDialog
    :open="isOpenAddAccount"
    :closeOnClickOutside="false"
    :title="`Add New account for ${userName}`"
    @update:open="emit('close')"
  >
    <template #body>
      <VeeForm
        v-slot="{ meta, handleSubmit, errors }"
        :initial-values="initialFormValues"
      >
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <VeeField
            v-slot="{ field, errorMessage }"
            name="planId"
            rules="required"
          >
            <DSelectBox
              :modelValue="field.value"
              label="Trading Program"
              :options="planListOptions"
              placeholder="Select a trading program"
              required
              :error="errorMessage"
              @update:modelValue="field.onChange"
              @blur="field.onBlur"
              @change="field.onChange"
            />
          </VeeField>

          <VeeField
            v-slot="{ field, errorMessage }"
            name="tradingPlatform"
            rules="required"
          >
            <DInput
              v-bind="field"
              type="text"
              label="MetaTrader Version"
              placeholder="Enter the MetaTrader version"
              required
              :error="errorMessage"
            />
          </VeeField>

          <div class="flex justify-end pt-4 space-x-3">
            <DButton
              type="submit"
              styleType="primary"
              label="Add Trading Account"
              :loading="isSubmitting"
              :disabled="!meta.valid || isSubmitting"
            />
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>

  <DToast />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToast } from '@libs/darya-design-system';

import { useCreateAccountMutation } from '@/modules/account';
import { useGetAllPlan } from '@/modules/plan';

interface AccountFormData {
  planId: number;
  tradingPlatform: string;
}

const emit = defineEmits<{
  close: [];
}>();

const props = defineProps<{
  isOpenAddAccount: boolean;
  userId: string;
  userName: string;
}>();

const isSubmitting = ref(false);

const { planList } = useGetAllPlan();
const { addToast } = useToast();

const handleSuccess = () => {
  isSubmitting.value = false;

  addToast({
    message: 'Trading account created successfully!',
    type: 'success',
    position: 'right-top',
    duration: 3000,
  });

  emit('close');
};

// Error handler
const handleError = (error: any) => {
  isSubmitting.value = false;

  addToast({
    message: error?.message || 'Failed to create account. Please try again.',
    type: 'error',
    position: 'right-top',
    duration: 3000,
  });
};

const planListOptions = computed(() => {
  return planList.value?.map((x) => ({ label: x.title, value: x.id })) ?? [];
});

const initialFormValues = computed(() => ({
  planId: 0,
  tradingPlatform: '',
}));

const { createAccount } = useCreateAccountMutation({
  onSuccess: handleSuccess,
  onError: handleError,
});

const onSubmit = (values: AccountFormData) => {
  isSubmitting.value = true;

  createAccount({
    userId: props.userId,
    planId: values.planId,
    tradingPlatform: values.tradingPlatform,
  });
};
</script>
