export interface updateLevelUpRuleFormModel {
  name: string;
  description: string;
  levelScorePercent: number;
  operator: number;
  rightType: number;
  rightValue: string;
  profitableTradingDaysFactor: number;
  duration: number;
  durationUnit: number;
  levelUpScoreFactor: number;
  calculationIntervalValue: number;
  calculationIntervalType: number;
  id: number;
}

export interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

export interface updateBreachRuleFormModel {
  name?: string;
  breachType: number;
  description?: string;
  breachScorePercent?: number;
  operator: number;
  rightType: number;
  rightValue?: string;
  duration: number;
  durationUnit: number;
  severity: number;
  severityValuePercentage: number;
  reOccurrenceType: number;
  reOccurrenceValue: number;
  stopLoss?: boolean;
  lossClearanceValue?: number;
  lossClearanceTimeMinutes?: number;
  assetClass: number;
  id: number;
}

export interface levelUpRuleFormModel {
  name: string;
  description?: string;
  levelScorePercent?: number;
  operator: number;
  rightType: number;
  rightValue?: string;
  profitableTradingDaysFactor: number;
  duration: number;
  durationUnit: number;
  levelUpScoreFactor: number;
  calculationIntervalValue: number;
  calculationIntervalType: number;
}

export interface breachRuleFormModel {
  name?: string;
  breachType: number;
  description?: string;
  breachScorePercent?: number;
  operator: number;
  rightType: number;
  rightValue?: string;
  duration: number;
  durationUnit: number;
  severity: number;
  severityValuePercentage: number;
  reOccurrenceType: number;
  reOccurrenceValue: number;
  stopLoss?: boolean;
  lossClearanceValue?: number;
  lossClearanceTimeMinutes?: number;
  assetClass: number;
}
