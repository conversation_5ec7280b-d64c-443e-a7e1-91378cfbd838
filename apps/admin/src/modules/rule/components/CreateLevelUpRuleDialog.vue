<template>
  <DDialog
    :open="isOpenCreateLevelUpRule"
    title="Add Level-Up Rule"
    :closeOnClickOutside="false"
    @update:open="emit('close')"
    maxWidth="xl"
  >
    <template #body>
      <VeeForm v-slot="{ handleSubmit }" :initial-values="formModel">
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="name"
              v-model="formModel.name"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Rule Name"
                placeholder="Enter Rule Name"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="description"
              v-model="formModel.description"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Description"
                placeholder="Enter Description"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="rightType">
              <DSelectBox
                v-bind="field"
                v-model="formModel.rightType"
                label="Right Type"
                :options="LEVEL_UP_RIGHT_TYPE_OPTION"
                placeholder="Select Condition"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="rightValue"
              v-model="formModel.rightValue"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Right Value"
                placeholder="Enter Right Value"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="operator">
              <DSelectBox
                v-bind="field"
                v-model="formModel.operator"
                label="Operator"
                :options="OPERATOR_OPTION"
                placeholder="Select Operator"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="durationUnit">
              <DSelectBox
                v-bind="field"
                label="Duration Unit"
                v-model="formModel.durationUnit"
                :options="DURATION_UNIT_OPTION"
                placeholder="Select Duration Unit"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="duration"
              v-model="formModel.duration"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Duration"
                placeholder="Enter Duration"
                required
                :error="errorMessage"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="profitableTradingDaysFactor"
            >
              <DSelectBox
                v-bind="field"
                v-model="formModel.profitableTradingDaysFactor"
                label="Profitable Trading Days"
                :options="PROFITABLE_TRADING_DAYS_FACTOR_OPTION"
                placeholder="Select Profitable Trading Days"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="levelUpScoreFactor"
            >
              <DSelectBox
                v-bind="field"
                v-model="formModel.levelUpScoreFactor"
                label="LevelUp Score Factor"
                :options="LEVEL_UP_SCORE_FACTOR_OPTION"
                placeholder="Select LevelUp Score Factor"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="levelScorePercent"
              v-model="formModel.levelScorePercent"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Level Score Percent"
                placeholder="Enter Level Score Percent"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="calculationIntervalType"
              v-model="formModel.calculationIntervalType"
            >
              <DSelectBox
                v-bind="field"
                label="Calculation Interval Type"
                :options="CALCULATION_INTERVAL_TYPE_OPTION"
                placeholder="Select Calculation Interval Type"
                v-model="formModel.calculationIntervalValue"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="calculationIntervalValue"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Calculation Interval Value"
                placeholder="Enter Calculation Interval Value"
                required
                :error="errorMessage"
              />
            </VeeField>
          </div>
          <div class="flex flex-row-reverse">
            <DButton label="Save Rule" type="submit" />
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import {
  OPERATOR_OPTION,
  DURATION_UNIT_OPTION,
  LEVEL_UP_RIGHT_TYPE_OPTION,
  PROFITABLE_TRADING_DAYS_FACTOR_OPTION,
  LEVEL_UP_SCORE_FACTOR_OPTION,
  CALCULATION_INTERVAL_TYPE_OPTION,
  useCreateLevelUpRule,
} from '@/modules/rule/index';
import { DButton } from '@libs/darya-design-system';

const emit = defineEmits(['close']);
defineProps<{ isOpenCreateLevelUpRule: boolean }>();

const { createLevelUpRule } = useCreateLevelUpRule({
  onSuccess: () => {
    emit('close');
  },
  onError: (error) => {
    console.error('Error creating level up rule:', error);
  },
});

const formModel = reactive({
  name: '',
  description: '',
  levelScorePercent: 0,
  operator: 1,
  rightType: 1,
  rightValue: '',
  profitableTradingDaysFactor: 1,
  duration: 0,
  durationUnit: 1,
  levelUpScoreFactor: 1,
  calculationIntervalValue: 0,
  calculationIntervalType: 1,
});

const onSubmit = () => {
  createLevelUpRule(formModel);
};
</script>
