<template>
  <DDialog
    :open="isOpenCreateBreachRule"
    title="Add Breach Rule"
    :closeOnClickOutside="false"
    @update:open="emit('close')"
    maxWidth="xl"
  >
    <template #body>
      <VeeForm v-slot="{ handleSubmit }" :initial-values="formModel">
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="name"
              v-model="formModel.name"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Rule Name"
                placeholder="Enter Rule Name"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field }"
              name="description"
              v-model="formModel.description"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Description"
                placeholder="Enter Description"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="breachType">
              <DSelectBox
                v-bind="field"
                v-model="formModel.breachType"
                label="Breach type"
                :options="BREACH_TYPE_OPTION"
                placeholder="Select Breach Type"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="rightType">
              <DSelectBox
                v-bind="field"
                v-model="formModel.rightType"
                label="Right Type"
                :options="BREACH_RIGHT_TYPE_OPTION"
                placeholder="Select Condition"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="assetClass">
              <DSelectBox
                v-bind="field"
                v-model="formModel.assetClass"
                label="Asset Class"
                :options="ASSET_CLASS_OPTION"
                placeholder="Select Asset Class"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField v-slot="{ field, errorMessage }" name="severity">
              <DSelectBox
                v-bind="field"
                v-model="formModel.severity"
                label="Severity"
                :options="SEVERITY_OPTION"
                placeholder="Select Severity"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-if="formModel.severity === 1"
              v-slot="{ field, errorMessage }"
              name="severityValuePercentage"
              v-model="formModel.severityValuePercentage"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Severity Value Percentage %"
                placeholder="Enter Severity Value Percentage"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-if="formModel.severity === 1 || formModel.severity === 2"
              v-slot="{ field, errorMessage }"
              name="breachScorePrecent"
              v-model="formModel.breachScorePercent"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Breach Score Precent %"
                placeholder="Enter breach Score Precent"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="rightValue"
              v-model="formModel.rightValue"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Right Value"
                placeholder="Enter Right Value"
                required
                :error="errorMessage"
              />
            </VeeField>
            <template v-if="formModel.rightType !== 10">
              <VeeField v-slot="{ field, errorMessage }" name="operator">
                <DSelectBox
                  v-bind="field"
                  label="Operator"
                  v-model="formModel.operator"
                  :options="OPERATOR_OPTION"
                  placeholder="Select Operator"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="durationUnit"
                v-model="formModel.durationUnit"
              >
                <DSelectBox
                  v-bind="field"
                  label="Duration Unit"
                  :options="DURATION_UNIT_OPTION"
                  placeholder="Select Duration Unit"
                  required
                  :error="errorMessage"
                />
              </VeeField>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="duration"
                v-model="formModel.duration"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="Duration"
                  placeholder="Enter Duration"
                  required
                  :error="errorMessage"
                />
              </VeeField>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="reOccurrenceType"
              >
                <DSelectBox
                  v-bind="field"
                  v-model="formModel.reOccurrenceType"
                  label="Reoccurrence Type"
                  :options="REOCCURENCE_TYPE_OPTION"
                  placeholder="Select Reoccurrence "
                  required
                  :error="errorMessage"
                />
              </VeeField>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="reOccurrenceValue"
                v-model="formModel.reOccurrenceValue"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="Re-occurrence Value"
                  placeholder="Enter reOccurrenceValue"
                  required
                  :error="errorMessage"
                />
              </VeeField>

              <VeeField
                v-slot="{ field, errorMessage }"
                name="lossClearanceValue"
                v-model="formModel.lossClearanceValue"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="Loss Clearance Value"
                  placeholder="Enter Loss Clearance Value"
                  required
                  :error="errorMessage"
                />
              </VeeField>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="lossClearanceTimeMinutes"
                v-model="formModel.lossClearanceTimeMinutes"
              >
                <DInput
                  v-bind="field"
                  type="text"
                  label="Loss Clearance Time Minutes"
                  placeholder="Enter Loss Clearance Time Minutes"
                  required
                  :error="errorMessage"
                />
              </VeeField>
            </template>
            <template v-else>
              <VeeField
                v-slot="{ field, errorMessage }"
                name="stopLoss"
                v-model="formModel.stopLoss"
              >
                <DSelectBox
                  v-bind="field"
                  label="StopLoss"
                  :options="STOP_LOSS_OPTION"
                  placeholder="Select stopLoss"
                  required
                  :error="errorMessage"
                />
              </VeeField>
            </template>
          </div>
          <div class="flex flex-row-reverse">
            <DButton label="Save Rule" type="submit" />
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

import {
  useCreateBreachRuleMutation,
  BREACH_TYPE_OPTION,
  OPERATOR_OPTION,
  BREACH_RIGHT_TYPE_OPTION,
  DURATION_UNIT_OPTION,
  SEVERITY_OPTION,
  REOCCURENCE_TYPE_OPTION,
  ASSET_CLASS_OPTION,
  STOP_LOSS_OPTION,
} from '@/modules/rule/index';

const emit = defineEmits(['close']);
defineProps<{ isOpenCreateBreachRule: boolean }>();

const formModel = reactive({
  name: '',
  breachType: 1,
  description: '',
  breachScorePercent: 0,
  operator: 1,
  rightType: 1,
  rightValue: undefined,
  duration: 0,
  durationUnit: 1,
  severity: 3,
  severityValuePercentage: 0,
  reOccurrenceType: 1,
  reOccurrenceValue: 0,
  stopLoss: undefined,
  lossClearanceValue: 0,
  lossClearanceTimeMinutes: 0,
  assetClass: 1,
});

const { createBreachRule } = useCreateBreachRuleMutation({
  onSuccess: () => {
    emit('close');
  },
  onError: (error) => {
    console.error('Error creating breach rule:', error);
  },
});

const onSubmit = () => {
  createBreachRule(formModel);
};
</script>
