const BREACH_TYPE_OPTION = [
  { label: 'System Breach', value: 1 },
  { label: 'Plan Breach', value: 2 },
];

const OPERATOR_OPTION = [
  { label: '>', value: 1 },
  { label: '<', value: 2 },
  { label: '=', value: 3 },
  { label: '!=', value: 4 },
  { label: '!=', value: 4 },
  { label: '>=', value: 5 },
  { label: '<=', value: 6 },
];

const BREACH_RIGHT_TYPE_OPTION = [
  { label: 'Equity', value: 1 },
  { label: 'Balance', value: 2 },
  { label: 'BalancePercent', value: 3 },
  { label: 'Daily Drawdown', value: 4 },
  { label: 'Daily Drawdown Percent', value: 5 },
  { label: 'Trailing Daily Drawdown', value: 6 },
  { label: 'Trailing Drawdown', value: 7 },
  { label: 'Trade Value Score', value: 8 },
  { label: 'Floating Loss Ratio', value: 9 },
  { label: 'Stop loss', value: 10 },
  { label: 'Maximum Trading Volume', value: 11 },
  { label: 'Stacking Trades', value: 12 },
  { label: 'Inactivity Days', value: 13 },
];

const DURATION_UNIT_OPTION = [
  { label: 'Seconds', value: 1 },
  { label: 'Hours', value: 2 },
  { label: 'Days', value: 3 },
  { label: 'Weeks', value: 4 },
  { label: 'Months', value: 5 },
  { label: 'Years', value: 6 },
];

const SEVERITY_OPTION = [
  { label: 'Soft', value: 1 },
  { label: 'Medium', value: 2 },
  { label: 'Hard', value: 3 },
];

const REOCCURENCE_TYPE_OPTION = [
  { label: 'Hourly', value: 1 },
  { label: 'Daily', value: 2 },
  { label: 'Weekly', value: 3 },
  { label: 'Monthly', value: 4 },
  { label: 'Yearly', value: 5 },
  { label: 'ReOccurring', value: 6 },
  { label: 'Always', value: 7 },
  { label: 'TillTheEndOfPlan', value: 8 },
];

const ASSET_CLASS_OPTION = [
  { label: 'Forex', value: 1 },
  { label: 'CFD', value: 2 },
  { label: 'Futures', value: 3 },
  { label: 'Stocks', value: 4 },
  { label: 'Bonds', value: 5 },
  { label: 'Options', value: 6 },
];

const STOP_LOSS_OPTION = [
  { label: 'True', value: true },
  { label: 'False', value: false },
];

const LEVEL_UP_RIGHT_TYPE_OPTION = [
  { label: 'Profit Target Value', value: 1 },
  { label: 'Trading Days', value: 2 },
  { label: 'Minimum Daily Profit Consistency Percent', value: 3 },
  { label: 'Profitable Trading Days', value: 4 },
];
const PROFITABLE_TRADING_DAYS_FACTOR_OPTION = [
  { label: 'Calculation Base Of Balance', value: 1 },
];

const LEVEL_UP_SCORE_FACTOR_OPTION = [{ label: 'Default', value: 1 }];
const CALCULATION_INTERVAL_TYPE_OPTION = [
  { label: 'Daily', value: 1 },
  { label: 'Weekly', value: 2 },
  { label: 'MonthLy', value: 3 },
  { label: 'Yearly', value: 4 },
];
export {
  BREACH_TYPE_OPTION,
  OPERATOR_OPTION,
  BREACH_RIGHT_TYPE_OPTION,
  DURATION_UNIT_OPTION,
  SEVERITY_OPTION,
  REOCCURENCE_TYPE_OPTION,
  ASSET_CLASS_OPTION,
  STOP_LOSS_OPTION,
  LEVEL_UP_RIGHT_TYPE_OPTION,
  PROFITABLE_TRADING_DAYS_FACTOR_OPTION,
  LEVEL_UP_SCORE_FACTOR_OPTION,
  CALCULATION_INTERVAL_TYPE_OPTION,
};
