import http from '../../services/http/index';
import { PLAN_MANAGE_API } from '@/configs/env';


export function getAllBreachRule() {
  return http.get(`${PLAN_MANAGE_API}/BreachRule/GetAll`);
}
export function getBreachRuleById(planId) {
  return http.get(`${PLAN_MANAGE_API}/BreachRule/GetById`, {
    params: { id: planId },
  });
}
export function createBreachRule(data) {
  return http.post(`${PLAN_MANAGE_API}/BreachRule/CreateBreachRule`, data);
}
export function updateBreachRule(data) {
  return http.put(`${PLAN_MANAGE_API}/BreachRule/UpdateBreachRule`, data);
}

export function getAllLevelUpRule() {
  return http.get(`${PLAN_MANAGE_API}/LevelUpRule/GetAll`);
}
export function getLevelUpRuleById(planId) {
  return http.get(`${PLAN_MANAGE_API}/LevelUpRule/GetById`, {
    params: { id: planId },
  });
}
export function createLevelUpRule(data) {
  return http.post(`${PLAN_MANAGE_API}/LevelUpRule/CreateLevelUpRule`, data);
}
export function updateLevelUpRule(data) {
  return http.put(`${PLAN_MANAGE_API}/LevelUpRule/UpdateLevelUpRule`, data);
}
