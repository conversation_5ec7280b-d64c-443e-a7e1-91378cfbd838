import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getLevelUpRuleById } from '../request';

export default function useGetLevelUpRuleById(
  levelUpRuleId: MaybeRef<number>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const levelUpRuleIdRef = toRef(levelUpRuleId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching } = useQuery({
    queryKey: computed(() => ['levelUpRuleData', levelUpRuleIdRef.value]),
    queryFn: () => {
      const id = levelUpRuleIdRef.value;

      if (!id) throw new Error('breach rule ID is required');

      return getLevelUpRuleById(id);
    },
    enabled: shouldFetchRef,
  });

  const levelUpRuleData = computed(() => data.value?.data?.data ?? {});

  return {
    getLevelUpRuleById: refetch,
    levelUpRuleData,
    isError,
    status,
    isFetching,
  } as const;
}
