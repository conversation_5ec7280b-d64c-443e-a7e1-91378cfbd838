import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getBreachRuleById } from '../request';

export default function useGetBreachRuleById(
  breachRuleId: MaybeRef<number>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const breachRuleIdRef = toRef(breachRuleId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['breachRuleData', breachRuleIdRef.value]),
    queryFn: () => {
      const id = breachRuleIdRef.value;

      if (!id) throw new Error('breach rule ID is required');

      return getBreachRuleById(id);
    },
    enabled: shouldFetchRef,
  });

  const breachRuleData = computed(() => data.value?.data?.data ?? {});

  return {
    getBreachRuleById: refetch,
    breachRuleData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
