import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { updateBreachRule } from '../request';

import type { ApiResponse, updateBreachRuleFormModel } from '@/modules/rule';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<updateBreachRuleFormModel>>,
    'mutationFn'
  > {}

export default function useUpdateBreachRuleMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<updateBreachRuleFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await updateBreachRule(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    updateBreachRule: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
