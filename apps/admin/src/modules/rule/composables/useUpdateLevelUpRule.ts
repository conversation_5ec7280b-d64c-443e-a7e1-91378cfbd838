import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';
import { isAxiosError } from 'axios';

import { updateLevelUpRule } from '../request';
import type { updateLevelUpRuleFormModel, ApiResponse } from '@/modules/rule';

export interface UseUpdateMutationOptions
  extends Omit<
    UseMutationOptions<
      ApiResponse,
      Error,
      MaybeRef<updateLevelUpRuleFormModel>
    >,
    'mutationFn'
  > {}

export default function useUpdateLevelUpRuleMutation(
  options?: UseUpdateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<updateLevelUpRuleFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await updateLevelUpRule(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message =
            err.response?.data?.message || 'Failed to update level-up rule';
          throw new Error(message);
        }

        throw new Error(
          'An unexpected error occurred while updating level-up rule'
        );
      }
    },
    ...options,
  });

  return {
    updateLevelUpRule: mutate,
    updateLevelUpRuleAsync: mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
