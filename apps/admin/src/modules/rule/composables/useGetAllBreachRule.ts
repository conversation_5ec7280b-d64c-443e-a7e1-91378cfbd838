import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllBreachRule } from '../request';

type PlanResponse = Awaited<ReturnType<typeof getAllBreachRule>>;

export default function useGetAllBreachRule() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    PlanResponse,
    Error
  >({
    queryKey: ['breachRuleList'],
    queryFn: getAllBreachRule,
  });

  const breachRuleList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllBreachRule: refetch,
    breachRuleList,
    isError,
    isFetching,
    error,
    status,
  } as const;
}
