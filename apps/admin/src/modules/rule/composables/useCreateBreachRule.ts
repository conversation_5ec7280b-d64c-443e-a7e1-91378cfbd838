import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { createBreachRule } from '../request';

import type { ApiResponse, breachRuleFormModel } from '@/modules/rule';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<breachRuleFormModel>>,
    'mutationFn'
  > {}

export default function useCreateBreachRuleMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<breachRuleFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await createBreachRule(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    createBreachRule: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
