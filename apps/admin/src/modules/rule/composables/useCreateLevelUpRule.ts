import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { createLevelUpRule } from '../request';

import type { ApiResponse, levelUpRuleFormModel } from '@/modules/rule';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<levelUpRuleFormModel>>,
    'mutationFn'
  > {}

export default function useCreateLevelUpRuleMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<levelUpRuleFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await createLevelUpRule(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    createLevelUpRule: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
