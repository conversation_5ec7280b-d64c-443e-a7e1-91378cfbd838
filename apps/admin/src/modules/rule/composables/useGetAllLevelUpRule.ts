import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllLevelUpRule } from '../request';

type PlanResponse = Awaited<ReturnType<typeof getAllLevelUpRule>>;

export default function useGetAllLevelUpRule() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    PlanResponse,
    Error
  >({
    queryKey: ['levelUpRuleList'],
    queryFn: getAllLevelUpRule,
  });

  const levelUpRuleList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllLevelUpRule: refetch,
    levelUpRuleList,
    isError,
    isFetching,
    error,
    status,
  } as const;
}
