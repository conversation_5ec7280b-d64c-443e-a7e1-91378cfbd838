<template>
  <DTab :tabs="tabs" v-model="activeTab" class="mt-8">
    <template v-for="(tab, idx) in tabs" #[tab.name]>
      <component
        v-if="tab.name !== 'auditLog' && typeof tab.actionId === 'number'"
        :is="getTabComponent(tab.actionId)"
        :user-id="userId"
        :name="tabColumnData?.fullName"
        :action-id="tab.actionId"
        :key="tab.name"
        @handleBackButton="handleBackButton"
      />

      <AuditLogTab
        v-else
        :user-id="userId"
        :name="tabColumnData?.fullName"
        @handleBackButton="handleBackButton"
      />
    </template>
  </DTab>

  <InsertRejectionReasonDialog
    :isOpenInsertRejectionReasonDialog="showRejectionDialog"
    @update:isOpenInsertRejectionReasonDialog="showRejectionDialog = $event"
    @close="showRejectionDialog = false"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  InsertRejectionReasonDialog,
  ProofOfIdentificationTab,
  ProofOfAddressTab,
  AuditLogTab,
  useGetTabColumnOfVerificationById,
} from '@/modules/kyc';

const activeTab = ref('tab1');
const showRejectionDialog = ref(false);
const route = useRoute();
const router = useRouter();
const userId = Array.isArray(route.params.id)
  ? route.params.id[0]
  : route.params.id || '';

const workflowId = route.query.workflowId
  ? Number(route.query.workflowId)
  : null;

const { tabColumnData, getTabColumnVerificationData } =
  useGetTabColumnOfVerificationById(userId, workflowId, false);

const tabs = computed(() => {
  if (!tabColumnData.value || !tabColumnData.value.kycColumn) return [];
  // Filter out any "Audit Log" tab from kycColumn
  const kycTabs = tabColumnData.value.kycColumn
    .filter((col) => col.title !== 'Audit Log')
    .map((col, idx) => ({
      name: `tab${idx + 1}`,
      label: col.title || `Tab ${idx + 1}`,
      actionId: col.actionId ?? undefined,
    }));
  // Always add Audit Log as the last tab, with actionId undefined
  return [
    ...kycTabs,
    { name: 'auditLog', label: 'Audit log', actionId: undefined },
  ];
});

function getTabComponent(actionId: number | undefined) {
  if (typeof actionId !== 'number') return null;
  if (tabs.value[0] && actionId === tabs.value[0].actionId)
    return ProofOfAddressTab;
  if (tabs.value[1] && actionId === tabs.value[1].actionId)
    return ProofOfIdentificationTab;
  return null;
}

onMounted(() => {
  getTabColumnVerificationData();
});

const handleBackButton = () => {
  router.push('/verifications');
};
</script>
