<template>
  <div class="py-4 border-2 border-neutral-13 rounded-2xl">
    <span class="px-6 text-lg font-bold">Verifications</span>
    <DataStateWrapper
      :isLoading="verificationState.isLoading.value"
      :hasError="verificationState.hasError.value"
      :hasNoData="verificationState.hasNoData.value"
      :isRefetching="verificationState.isRefetching.value"
      :errorConfig="verificationState.errorConfig.value"
      :emptyConfig="verificationState.emptyConfig.value"
    >
      <DBaseTable
        :columns="columns"
        :data="verificationData"
        :enable-pagination="true"
        :enable-search="true"
        :showRowsPerPageSelect="true"
        class="mt-7"
      >
        <template #column-row="{ index }">
          {{ index + 1 }}
        </template>
        <template #column-poiVerificationStatus="{ row }">
          <DChip
            variant="outline"
            :color="getStatusInfo(row.poiVerificationStatus).color"
            :label="getStatusInfo(row.poiVerificationStatus).text"
          />
        </template>
        <template #column-country="{ row }">
          <div class="flex justify-center items-center">
            <component :is="row.icon" />
            <span class="ps-2">{{ row.country }}</span>
          </div>
        </template>
        <template #column-poaVerificationStatus="{ row }">
          <DChip
            variant="outline"
            :color="getStatusInfo(row.poaVerificationStatus).color"
            :label="getStatusInfo(row.poaVerificationStatus).text"
          />
        </template>
        <template #column-lastUpdate="{ row }">
          {{ d(row.lastUpdate) }}
        </template>
        <template #column-actions="{ row }">
          <div class="flex justify-center items-center">
            <DPopper
              v-model="dropdownState[row.userId]"
              placement="bottom-end"
              :arrow="false"
              offset-y="6"
              append-to-body
            >
              <template #default>
                <DaryaOutlineMoreIcon class="cursor-pointer" />
              </template>

              <template #content>
                <div class="bg-white shadow-lg rounded-lg p-2 w-56 text-left">
                  <router-link
                    v-slot="{ navigate }"
                    :to="{
                      path: `/verifications/${row.userId}/details`,
                      query: { workflowId: row.workflowId },
                    }"
                  >
                    <div
                      class="flex items-center cursor-pointer rounded-2xl p-3 hover:border hover:border-primary-tint2"
                      @click="navigate"
                    >
                      <DaryaOutlineSortIcon size="20" />
                      <span class="text-neutral-4 text-base ps-1">
                        Verification Details
                      </span>
                    </div>
                  </router-link>
                </div>
              </template>
            </DPopper>
          </div>
        </template>
      </DBaseTable>
    </DataStateWrapper>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useGetVerification } from '@/modules/kyc';
import { useI18n } from 'vue-i18n';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const dropdownState = ref<Record<string, boolean>>({});
const { t, d } = useI18n();
const { verificationData, status, isFetching, isError, error } =
  useGetVerification(true);

const verificationState = useDataState(
  status,
  verificationData,
  isFetching,
  isError,
  error,
  'verifications'
);
const columns = ref([
  { key: 'row', label: 'Row' },
  { key: 'fullName', label: 'Name' },
  { key: 'email', label: 'Email' },
  { key: 'residenceCountryName', label: 'Country' },
  { key: 'poiVerificationStatus', label: 'POI' },
  { key: 'poaVerificationStatus', label: 'POA' },
  { key: 'lastUpdate', label: 'Last Update' },
  { key: 'actions', label: 'Actions' },
]);

const STATUS_MAP = {
  4: { text: 'Approved', color: 'success' },
  3: { text: 'Pending Approval', color: 'warning' },
  2: { text: 'Required', color: 'primary' },
  5: { text: 'Rejected', color: 'error' },
  1: { text: 'Not Required', color: 'neutral' },
};

const getStatusInfo = computed(() => (status: number) => {
  return STATUS_MAP[status] || { text: 'Unknown', color: 'neutral' };
});
</script>
