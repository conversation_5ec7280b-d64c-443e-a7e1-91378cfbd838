<template>
  <span>Breach Rule</span>
  <!-- BreachRule Data with State Management -->
  <DataStateWrapper
    :isLoading="breachRuleState.isLoading.value"
    :hasError="breachRuleState.hasError.value"
    :hasNoData="breachRuleState.hasNoData.value"
    :isRefetching="breachRuleState.isRefetching.value"
    :errorConfig="breachRuleState.errorConfig.value"
    :emptyConfig="breachRuleState.emptyConfig.value"
    :onRetry="retryBreachRuleData"
  >
    <div class="mb-4 flex" dir="rtl">
      <DButton
        label="Add BreachRule"
        icon="DaryaPlusIcon"
        icon-position="right"
        class="cursor-pointer"
        @click="isOpenCreateBreachRule = true"
      />
    </div>

    <DBox v-for="item in breachRuleList" :key="item.id" class="mt-3 px-4 py-3">
      <div class="flex justify-between items-center">
        <div>
          <span class="block font-medium text-sm mb-1">{{ item.name }}</span>
          <span class="block text-xs">{{ item.description }}</span>
        </div>

        <DaryaEditIcon
          class="cursor-pointer"
          @click="handleOpenUpdateRuleDialog(item.id)"
        />
      </div>
    </DBox>
  </DataStateWrapper>
  <CreateBreachRuleDialog
    :isOpenCreateBreachRule="isOpenCreateBreachRule"
    @update:isOpenCreateBreachRule="isOpenCreateBreachRule = $event"
    @close="handleCloseCreateBreachRuleDialog"
  />
  <EditBreachRuleDialog
    :isOpenUpdateBreachRule="isOpenUpdateBreachRule"
    :id="breachRoleId"
    :data="breachRuleData"
    @update="isOpenUpdateBreachRule = $event"
    @close="handleCloseUpdateBreachRuleDialog"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { DButton } from '@libs/darya-design-system';

import {
  CreateBreachRuleDialog,
  EditBreachRuleDialog,
  useGetAllBreachRule,
  useGetBreachRuleById,
} from '@/modules/rule/index';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const breachRoleId = ref();
const isOpenCreateBreachRule = ref(false);
const isOpenUpdateBreachRule = ref(false);

const {
  getAllBreachRule,
  breachRuleList,
  status: breachRuleStatus,
  isFetching: breachRuleFetching,
  isError: breachRuleError,
  error: breachRuleErrorObj,
} = useGetAllBreachRule();
const { getBreachRuleById, breachRuleData } = useGetBreachRuleById(
  breachRoleId,
  false
);

const breachRuleState = useDataState(
  breachRuleStatus,
  breachRuleList,
  breachRuleFetching,
  breachRuleError,
  breachRuleErrorObj,
  'breached-rule'
);
const handleCloseCreateBreachRuleDialog = () => {
  isOpenCreateBreachRule.value = false;
  getAllBreachRule();
};
const handleCloseUpdateBreachRuleDialog = () => {
  isOpenUpdateBreachRule.value = false;
  getAllBreachRule();
};
const handleOpenUpdateRuleDialog = (item) => {
  if (item) {
    breachRoleId.value = item;
    getBreachRuleById();
    isOpenUpdateBreachRule.value = true;
  }
};
const retryBreachRuleData = () => {
  getAllBreachRule();
};
</script>
