<template>
  <div class="flex h-screen" :class="{ 'flex-row-reverse': isRtl }">
    <div :dir="textDirection" class="flex-1">
      <LoginForm />
    </div>
  </div>
</template>

<script setup lang="ts">
import { LoginForm } from '@/modules/user/index';

import { useLocale } from '@/composables/useLocale';

const { textDirection, isRtl } = useLocale();
</script>

<route lang="yaml">
meta:
  auth: false
  layout: false
</route>
