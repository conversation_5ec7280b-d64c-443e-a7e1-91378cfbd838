<template>
  <span class="font-medium">Payment</span>

  <!-- Transaction Data with State Management -->
  <DataStateWrapper
    :isLoading="transactionState.isLoading.value"
    :hasError="transactionState.hasError.value"
    :hasNoData="transactionState.hasNoData.value"
    :isRefetching="transactionState.isRefetching.value"
    :errorConfig="transactionState.errorConfig.value"
    :emptyConfig="transactionState.emptyConfig.value"
    :onRetry="retryTransactionData"
  >
    <DBox class="flex w-full mt-4">
      <DBaseTable
        :data="transactionList"
        :columns="columns"
        :enable-pagination="true"
        rows-per-page="6"
      >
        <template #column-name="{ row }">
          <div class="flex flex-col text-left">
            <span class="font-bold">
              {{ row.fullName }}
            </span>
            <span>
              {{ row.email }}
            </span>
            <span class="inline-flex">
              <span class="font-bold me-0.5">Reference ID: </span>
              <span>{{ row.refrenceId }}</span>
            </span>
            <span class="inline-flex">
              <span class="font-bold pe-0.5">Requested On: </span>
              <span> {{ row.createDate }}</span>
            </span>
          </div>
        </template>
        <template #column-type="{ row }">
          <span>{{ row.transactionType === 1 ? 'Withdraw' : 'Deposit' }}</span>
        </template>
        <template #column-transaction="{ row }">
          <span class="font-bold pe-0.5">Currency: </span>
          <span>{{ row.currency }}</span>
        </template>
        <template #column-amount="{ row }">
          <span>${{ row.amount }}</span>
        </template>
        <template #column-status="{ row }">
          <DChip
            :label="mapStatusChip[row.status].label"
            :status="mapStatusChip[row.status].color"
            variant="outline"
          />
        </template>
      </DBaseTable>
    </DBox>
  </DataStateWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useGetAllTransaction } from '@/modules/payment';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

// Transaction data with state management
const {
  transactionList,
  status,
  isFetching,
  isError,
  error,
  getAllTransaction,
} = useGetAllTransaction();

const transactionState = useDataState(
  status,
  transactionList,
  isFetching,
  isError,
  error,
  'data'
);

// Retry function
const retryTransactionData = () => getAllTransaction();

const columns = ref([
  { key: 'name', label: 'Name' },
  { key: 'type', label: 'Type' },
  { key: 'transaction', label: 'Transaction Details' },
  { key: 'amount', label: 'Amount' },
  { key: 'status', label: 'Status' },
]);

const mapStatusChip = {
  Created: { label: 'Created', status: 'confirmed' },
  Failed: { label: 'Failed', color: 'failed' },
  Canceled: { label: 'Canceled', color: 'failed' },
  Succeeded: { label: 'Succeeded', color: 'confirmed' },
};
</script>
