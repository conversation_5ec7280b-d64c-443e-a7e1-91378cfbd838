<template>
  <span>LevelUp Rule</span>

  <div class="mb-4 flex" dir="rtl">
    <DButton
      label="Add LevelUp Rule"
      icon="DaryaPlusIcon"
      icon-position="right"
      class="cursor-pointer"
      @click="isOpenCreateLevelUpRule = true"
    />
  </div>
  <!-- BreachRule Data with State Management -->
  <DataStateWrapper
    :isLoading="levelUpRuleState.isLoading.value"
    :hasError="levelUpRuleState.hasError.value"
    :hasNoData="levelUpRuleState.hasNoData.value"
    :isRefetching="levelUpRuleState.isRefetching.value"
    :errorConfig="levelUpRuleState.errorConfig.value"
    :emptyConfig="levelUpRuleState.emptyConfig.value"
    :onRetry="retryLevelUpData"
  >
    <DBox v-for="item in levelUpRuleList" :key="item.id" class="mt-3 px-4 py-3">
      <div class="flex justify-between items-center">
        <div>
          <span class="block font-medium text-sm mb-1">{{ item.name }}</span>
          <span class="block text-xs">{{ item.description }}</span>
        </div>
        <DaryaEditIcon
          class="cursor-pointer"
          @click="handleOpenUpdateDialog(item.id)"
        />
      </div>
    </DBox>
  </DataStateWrapper>
  <CreateLevelUpRuleDialog
    :isOpenCreateLevelUpRule="isOpenCreateLevelUpRule"
    @update:isOpenCreateLevelUpRule="isOpenCreateLevelUpRule = $event"
    @close="handleCloseCreateLevelUpDialog"
  />
  <EditLevelUpRuleDialog
    :isOpenUpdateLevelUpRule="isOpenUpdateLevelUpRule"
    @update:isOpenUpdateLevelUpRule="isOpenUpdateLevelUpRule = $event"
    :data="levelUpRuleData"
    :id="levelUpRuleId"
    @close="handleCloseUpdateLevelUpDialog"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';

import {
  useGetAllLevelUpRule,
  CreateLevelUpRuleDialog,
  EditLevelUpRuleDialog,
  useGetLevelUpRuleById,
} from '@/modules/rule/index';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const levelUpRuleId = ref<number>(0);
const isOpenCreateLevelUpRule = ref(false);
const isOpenUpdateLevelUpRule = ref(false);

const {
  getAllLevelUpRule,
  levelUpRuleList,
  status,
  isFetching,
  isError,
  error,
} = useGetAllLevelUpRule();
const { getLevelUpRuleById, levelUpRuleData } = useGetLevelUpRuleById(
  levelUpRuleId,
  false
);
const levelUpRuleState = useDataState(
  status,
  levelUpRuleList,
  isFetching,
  isError,
  error,
  'breached-rule'
);
const handleCloseCreateLevelUpDialog = () => {
  isOpenCreateLevelUpRule.value = false;
  getAllLevelUpRule();
};
const handleCloseUpdateLevelUpDialog = () => {
  isOpenUpdateLevelUpRule.value = false;
  getAllLevelUpRule();
};
const handleOpenUpdateDialog = (id) => {
  if (id) {
    levelUpRuleId.value = id;
    getLevelUpRuleById();
    isOpenUpdateLevelUpRule.value = true;
  }
};
const retryLevelUpData = () => {
  getAllLevelUpRule();
};
</script>
