<template>
  <span class="font-medium">Programs</span>
  <div class="mb-4 flex" dir="rtl">
    <DButton
      label="Add Program"
      icon="DaryaPlusIcon"
      icon-position="right"
      @click="isOpenCreatePlan = true"
    />
  </div>
  <!-- Plan Data with State Management -->
  <DataStateWrapper
    :isLoading="planState.isLoading.value"
    :hasError="planState.hasError.value"
    :hasNoData="planState.hasNoData.value"
    :isRefetching="planState.isRefetching.value"
    :errorConfig="planState.errorConfig.value"
    :emptyConfig="planState.emptyConfig.value"
    :onRetry="retryPlanData"
  >
    <DBox class="flex w-full pt-4">
      <DBaseTable
        :data="planList"
        :columns="columns"
        :enable-pagination="true"
        :rowsPerPage="5"
        :enable-search="true"
        :showRowsPerPageSelect="true"
      >
        <template #column-actions="{ row }">
          <DPopper
            v-model="dropdownState[row.id]"
            placement="bottom-end"
            :arrow="false"
            offset-y="6"
            append-to-body
          >
            <template #default>
              <DButton
                icon="DaryaArrowDownIcon"
                icon-position="right"
                class="cursor-pointer"
                label="Action"
              />
            </template>

            <template #content>
              <div
                class="bg-white shadow-lg border border-gray-200 rounded-md w-48 text-left"
              >
                <ul>
                  <li
                    class="flex items-center p-3 hover:bg-gray-300 cursor-pointer border-b border-gray-200"
                    @click="handleOpenEditPlanDialog(row)"
                  >
                    <DaryaEyeIcon size="16" color="gray" />
                    <span class="ms-1 text-gray-500">Edit Program</span>
                  </li>
                  <li
                    class="flex items-center p-3 hover:bg-gray-300 cursor-pointer"
                    @click="handleOpenDeletePlanDialog(row)"
                  >
                    <DaryaTrashLightIcon size="16" color="gray" />
                    <span class="ms-1 text-gray-500">Delete Program</span>
                  </li>
                </ul>
              </div>
            </template>
          </DPopper>
        </template>
      </DBaseTable>
    </DBox>
  </DataStateWrapper>

  <EditPlanDialog
    :isOpenEditPlan="isOpenEditPlan"
    :planId="planId"
    :planList="planListOptions"
    @update:isOpenEditPlan="isOpenEditPlan = $event"
    @close="handleCloseEditPlanDialog"
  />
  <DeletePlanDialog
    :isOpenDeletePlan="isOpenDeletePlan"
    :planId="planId"
    @update:isOpenDeletePlan="isOpenDeletePlan = $event"
    @close="handleCloseDeletePlanDialog"
  />
  <CreatePlanDialog
    :isOpenCreatePlan="isOpenCreatePlan"
    :planList="planListOptions"
    @update:isOpenCreatePlan="isOpenCreatePlan = $event"
    @close="handleCloseCreatePlanDialog"
  />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import {
  CreatePlanDialog,
  EditPlanDialog,
  DeletePlanDialog,
  useGetAllPlan,
} from '@/modules/plan/index';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const planId = ref();
const isOpenDeletePlan = ref(false);
const isOpenEditPlan = ref(false);
const isOpenCreatePlan = ref(false);

const columns = ref([
  { key: 'title', label: 'Name' },
  { key: 'initialBalance', label: 'Initial Balance' },
  { key: 'drawDownUpdateTime', label: 'DrawDown Update Time' },
  { key: 'serverGroups', label: 'serverGroups' },
  { key: 'nextPhaseId', label: 'Next PhaseId' },
  { key: 'price', label: 'Price' },
  { key: 'description', label: 'Description' },
  { key: 'symbolNameCurrency', label: 'Symbol Name Currency' },
  { key: 'tradeType', label: 'Trade Type' },
  { key: 'actions', label: 'Action' },
]);

const { getAllPlan, planList, status, isFetching, isError, error } =
  useGetAllPlan();

const planState = useDataState(
  status,
  planList,
  isFetching,
  isError,
  error,
  'user-accounts'
);
const dropdownState = ref<Record<string, boolean>>({});

const handleOpenEditPlanDialog = (item) => {
  isOpenEditPlan.value = true;
  dropdownState.value[item.id] = false;
  planId.value = item.id;
};
const handleOpenDeletePlanDialog = (item) => {
  dropdownState.value[item.id] = false;
  planId.value = item.id;
  isOpenDeletePlan.value = true;
};
const handleCloseDeletePlanDialog = () => {
  isOpenDeletePlan.value = false;
  getAllPlan();
};
const handleCloseEditPlanDialog = () => {
  isOpenEditPlan.value = false;
  getAllPlan();
};
const handleCloseCreatePlanDialog = () => {
  isOpenCreatePlan.value = false;
  getAllPlan();
};

const planListOptions = computed(() => {
  return planList.value?.map((x) => ({ label: x.title, value: x.id })) ?? [];
});

const retryPlanData = () => getAllPlan();
</script>
