<template>
  <!-- User Accounts Data with State Management -->
  <DataStateWrapper
    :isLoading="userAccountState.isLoading.value"
    :hasError="userAccountState.hasError.value"
    :hasNoData="userAccountState.hasNoData.value"
    :isRefetching="userAccountState.isRefetching.value"
    :errorConfig="userAccountState.errorConfig.value"
    :emptyConfig="userAccountState.emptyConfig.value"
    :onRetry="retryUserAccountData"
  >
    <span class="block mb-4 font-medium">
      {{ safeUserAccountsData[0]?.fullName }}
    </span>

    <DBox class="flex w-full pt-4">
      <DBaseTable
        v-if="safeUserAccountsData.length > 0"
        :columns="columns"
        :data="safeUserAccountsData"
        :enable-pagination="true"
        :showRowsPerPageSelect="true"
      >
        <template #column-initialBalance="{ row }">
          <span class="block">
            <span class="text-xs">{{ row.accountId }}</span>
          </span>
          <span class="block">
            <span class="text-gray-500 text-xs">{{ row.planName }}</span>
          </span>
          <span class="block">
            <span class="text-xs font-medium"> Initial Balance: </span>
            <span class="text-xs"> {{ row.initialBalance }}</span>
          </span>
          <span class="block">
            <span class="block text-xs font-medium">Trading Platform: </span>
            <span class="text-xs">{{ row.tradingPlatform }}</span>
          </span>
        </template>
        <template #column-fullName="{ row }">
          <span class="block text-xs font-medium">{{ row.fullName }}</span>
          <span class="text-xs">{{ row.email }}</span>
        </template>
        <template #column-balance="{ row }">
          <span class="block text-xs font-medium"
            >Balance: ${{ row.balance }}</span
          >
          <span class="text-xs">Equity: ${{ row.equity }}</span>
        </template>
        <template #column-tradingDays="{ row }">
          <span>{{ row.tradingDays }} Days </span>
        </template>
        <template #column-activeDays="{ row }">
          <span>{{ row.activeDays }} Days </span>
        </template>
        <template #column-growth="{ row }">
          <span>{{ row.growth }} % </span>
        </template>
        <template #column-status="{ row }">
          <DChip
            v-if="row.status !== 0"
            :label="STATUS[row.status].label"
            :status="STATUS[row.status].status"
            variant="outline"
          />
          <span></span>
        </template>

        <template #column-actions="{ row }">
          <router-link
            v-slot="{ isActive, navigate }"
            :to="`/account/${row.id}/report`"
            custom
          >
            <DButton
              icon="DaryaReportIcon"
              icon-position="left"
              class="cursor-pointer"
              label="View report"
              @click="navigate"
            />
          </router-link>
        </template>
      </DBaseTable>
    </DBox>
  </DataStateWrapper>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useGetUserAccountsById } from '@/modules/account';
import { useRoute } from 'vue-router';

import DataStateWrapper from '@/components/DataStateWrapper.vue';

import { useDataState } from '@/composables/useDataState';

const route = useRoute();
const userId = computed(() => {
  const id = route.params.id;
  return Array.isArray(id) ? id[0] : id;
});

const {
  userAccountsData,
  status,
  isFetching,
  isError,
  error,
  getUserAccountsById,
} = useGetUserAccountsById(userId);
const safeUserAccountsData = computed(() => userAccountsData.value || []);

const userAccountState = useDataState(
  status,
  userAccountsData,
  isFetching,
  isError,
  error,
  'user-accounts'
);

const columns = ref([
  { key: 'initialBalance', label: 'Account' },
  { key: 'fullName', label: 'User' },
  { key: 'balance', label: 'Current' },
  { key: 'tradingDays', label: 'Trading Days' },
  { key: 'activeDays', label: 'Active Days' },
  { key: 'growth', label: 'Growth' },
  { key: 'status', label: 'Status' },
  { key: 'actions', label: 'Actions' },
]);

const STATUS = {
  1: { label: 'Active', status: 'confirmed' },
  2: { label: 'Key Pending', status: 'pending' },
  3: { label: 'Breached', status: 'failed' },
  4: { label: 'Upgraded', status: 'upgraded' },
  5: { label: 'Pending', status: 'pending' },
  6: { label: 'Waiting Withdrawal', status: 'default' },
};

const retryUserAccountData = () => {
  getUserAccountsById();
};
</script>
