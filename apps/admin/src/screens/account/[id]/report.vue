<template>
  <!-- Header Section -->
  <DBox
    class="inline-block px-2 py-1 text-sm text-gray-500 border border-blue-200 rounded bg-blue-50 mb-6"
  >
    Latest update 2023-01-26 07:20:55 (UTC+2)
  </DBox>

  <div class="grid grid-cols-3 gap-4 w-full mb-6">
    <!-- Profit Card -->
    <DBox class="flex items-start px-4 py-3 bg-light-blue">
      <DaryaCalendarIcon color="blue" class="mt-1 shrink-0" />
      <div class="ms-3 w-full">
        <div class="text-2xl font-bold">$18.6K</div>
        <div class="text-sm text-gray-500">Today Profit</div>
      </div>
    </DBox>

    <DBox class="flex items-start px-4 py-3 bg-light-blue">
      <DaryaCalendarIcon color="blue" class="mt-1 shrink-0" />
      <div class="ms-3 w-full">
        <div class="text-2xl font-bold">55%</div>
        <div class="text-sm text-gray-500">Drawdown</div>
      </div>
    </DBox>

    <DBox class="flex items-start px-4 py-3 bg-light-blue">
      <DaryaCalendarIcon color="blue" class="mt-1 shrink-0" />
      <div class="ms-3 w-full">
        <div class="text-2xl font-bold">5</div>
        <div class="text-sm text-gray-500">Training Days</div>
      </div>
    </DBox>
  </div>

  <div class="grid grid-cols-3 gap-4 w-full mb-6">
    <!-- Statistics Overview (Spanning Two Columns) -->
    <DBox class="px-4 py-3 col-span-2">
      <div class="text-lg font-semibold">Statistics Overview</div>
      <div class="text-sm text-gray-500 mt-2">2023-01-26 07:20:55</div>
      <!-- Add your chart component here -->
    </DBox>

    <!-- Current Balance Card -->
    <DBox class="px-4 py-3">
      <div class="text-lg font-semibold">Current Balance</div>
      <div class="text-2xl font-bold mt-1">$13,500.00</div>
      <div class="text-sm text-gray-500">Lowest allowed</div>
      <div class="mt-2">
        <span class="text-green-500">$15000.00 ↑2%</span>
        <span class="text-blue-500 ml-2">Equ</span>
      </div>
      <div class="mt-1">
        <span class="text-orange-500">$15000.00 ☺7%</span>
        <span class="text-blue-500 ml-2">Bala nce</span>
      </div>
    </DBox>
  </div>

  <DBox class="flex flex-col w-full mb-6">
    <span class="font-medium px-4 py-2">Open Positions</span>
    <DBaseTable :columns="columns" :data="tableData" :rowsPerPage="5" />
  </DBox>

  <DBox class="flex flex-col w-full mb-6">
    <DBaseTable
      :columns="columnsOfObjectives"
      :data="tableDataOfObjectives"
      :rowsPerPage="5"
    >
      <template #column-status="{ row }">
        <DChip :label="row.status" :status="row.chipStatus" variant="outline" />
      </template>
    </DBaseTable>
  </DBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const columns = ref([
  { key: 'deposit', label: 'Deposit' },
  { key: 'startDate', label: 'Start Date' },
  { key: 'endDate', label: 'End Date' },
  { key: 'broker', label: 'Broker' },
  { key: 'platform', label: 'Platform' },
  { key: 'winRate', label: 'Win Rate' },
  { key: 'noOfTrades', label: 'No.of Trades' },
  { key: 'noOfLots', label: 'No.of Lots' },
]);
const tableData = ref([
  {
    deposit: 'admin123',
    startDate: '2025-03-15 14:30',
    endDate: 'AdvancedTrader',
    broker: 60,
    platform: 15,
    winRate: '32%',
    noOfTrades: 8,
    noOfLots: 3,
  },
]);

const columnsOfObjectives = ref([
  { key: 'objective', label: 'Objective' },
  { key: 'status', label: 'Status' },
]);
const tableDataOfObjectives = ref([
  {
    objective: 'Minimum profitability after 90 calendar days (> 10%)',
    status: 'Incomplete',
    chipStatus: 'default',
  },
  {
    objective: 'Max equity drawdown (< 10 %)',
    status: 'Complete',
    chipStatus: 'confirmed',
  },
  {
    objective: 'Max trading volume',
    status: 'Complete',
    chipStatus: 'confirmed',
  },
  {
    objective: 'Trading with stop loss',
    status: 'Complete',
    chipStatus: 'confirmed',
  },
]);
</script>
