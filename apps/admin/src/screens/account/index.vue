<template>
  <span class="block mb-4 font-medium"> Account</span>

  <!-- Account Data with State Management -->
  <DataStateWrapper
    :isLoading="accountState.isLoading.value"
    :hasError="accountState.hasError.value"
    :hasNoData="accountState.hasNoData.value"
    :isRefetching="accountState.isRefetching.value"
    :errorConfig="accountState.errorConfig.value"
    :emptyConfig="accountState.emptyConfig.value"
    :onRetry="retryAccountData"
  >
    <DBox class="flex w-full pt-4">
      <DBaseTable
        :columns="columns"
        :data="accountList"
        :enable-pagination="true"
        :enable-search="true"
        :showRowsPerPageSelect="true"
      >
        <template #column-initialBalance="{ row }">
          <span class="block">
            <span class="text-xs">{{ row.accountId }}</span>
          </span>
          <span class="block">
            <span class="text-gray-500 text-xs">{{ row.planName }}</span>
          </span>
          <span class="block">
            <span class="text-xs font-medium"> Initial Balance: </span>
            <span class="text-xs"> {{ row.initialBalance }}</span>
          </span>
          <span class="block">
            <span class="block text-xs font-medium">Trading Platform: </span>
            <span class="text-xs">{{ row.tradingPlatform }}</span>
          </span>
        </template>
        <template #column-fullName="{ row }">
          <span class="block text-xs font-medium">{{ row.fullName }}</span>
          <span class="text-xs">{{ row.email }}</span>
        </template>
        <template #column-balance="{ row }">
          <span class="block text-xs font-medium"
            >Balance: ${{ row.balance }}</span
          >
          <span class="text-xs">Equity: ${{ row.equity }}</span>
        </template>
        <template #column-tradingDays="{ row }">
          <span>{{ row.tradingDays }} Days </span>
        </template>
        <template #column-activeDays="{ row }">
          <span>{{ row.activeDays }} Days </span>
        </template>
        <template #column-growth="{ row }">
          <span>{{ row.growth }} % </span>
        </template>
        <template #column-status="{ row }">
          <DChip
            v-if="row.status !== 0"
            :label="STATUS[row.status].label"
            :status="STATUS[row.status].status"
            variant="outline"
          />
          <span></span>
        </template>
        <template #column-verification="{ row }">
          <DChip
            :label="VERIFICATION[row.verification].label"
            :status="VERIFICATION[row.verification].status"
            variant="outline"
          />
        </template>
        <template #column-actions="{ row }">
          <router-link
            v-slot="{ isActive, navigate }"
            :to="`/account/${row.id}/report`"
            custom
          >
            <DButton
              icon="DaryaReportIcon"
              icon-position="left"
              class="cursor-pointer"
              label="View report"
              @click="navigate"
            />
          </router-link>
        </template>
      </DBaseTable>
    </DBox>
  </DataStateWrapper>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { useGetAllAccount } from '@/modules/account';
import { DButton } from '@libs/darya-design-system';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

// Account data with state management
const { accountList, status, isFetching, isError, error, getAllAccount } =
  useGetAllAccount();

const accountState = useDataState(
  status,
  accountList,
  isFetching,
  isError,
  error,
  'accounts'
);

// Retry function
const retryAccountData = () => getAllAccount();

const columns = ref([
  { key: 'initialBalance', label: 'Account' },
  { key: 'fullName', label: 'User' },
  { key: 'balance', label: 'Current' },
  { key: 'tradingDays', label: 'Trading Days' },
  { key: 'activeDays', label: 'Active Days' },
  { key: 'growth', label: 'Growth' },
  { key: 'status', label: 'Status' },
  { key: 'verifications', label: 'Verification' },
  { key: 'actions', label: 'Action' },
]);

const STATUS = {
  1: { label: 'Active', status: 'confirmed' },
  2: { label: 'Key Pending', status: 'pending' },
  3: { label: 'Breached', status: 'failed' },
  4: { label: 'Upgraded', status: 'upgraded' },
  5: { label: 'Pending', status: 'pending' },
  6: { label: 'Waiting Withdrawal', status: 'default' },
};

const VERIFICATION = {
  true: { label: 'Verified', status: 'confirmed' },
  false: { label: 'Not Verified', color: 'default' },
};
</script>
