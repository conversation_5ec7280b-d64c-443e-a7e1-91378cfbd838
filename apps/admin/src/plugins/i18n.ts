import { createI18n, type I18nOptions } from 'vue-i18n';
import { App } from 'vue';
import messages from '@intlify/unplugin-vue-i18n/messages';

const options: I18nOptions = {
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages,
  datetimeFormats: {
    en: {
      shortDate: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      },
      shortTime: {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      },
      longDate: {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      },
    },
  },
};

const i18n = createI18n<false, typeof options>(options);

const install = (app: App) => {
  app.use(i18n);
};

export { i18n, install };
