<template>
  <aside
    class="bg-lightMood-2 fixed top-0 z-50 h-full py-5 px-3 left-0 overflow-y-scroll"
    :class="modelValue ? 'w-[260px]' : 'w-[85px]'"
  >
    <div class="flex flex-col w-full">
      <div class="flex items-center justify-center gap-3 mb-8">
        <img
          src="@/assets/images/logo.svg"
          alt="Logo"
          width="50"
          height="49"
          aria-label="Application Logo"
        />
        <span
          class="text-xl font-semibold transition-opacity duration-200"
          :class="modelValue ? 'block' : 'hidden'"
        >
          {{ t('sidebar.brand_name') }}
        </span>
      </div>

      <nav class="w-full px-2 space-y-2">
        <div v-for="section in SECTIONS" :key="section.name">
          <div
            class="flex items-center gap-2 cursor-pointer py-2 px-3 rounded-lg hover:bg-primary/15 transition-colors"
            :class="[isSectionActive(section) ? 'bg-primary/15' : '']"
            @click="toggleSection(section)"
          >
            <div class="text-neutral-5">
              <component :is="section.icon" />
            </div>
            <span
              class="font-medium text-sm text-neutral-5 transition-opacity duration-200"
              :class="modelValue ? 'block' : 'hidden'"
            >
              {{ section.name }}
            </span>

            <div
              v-if="section.items.length && modelValue"
              class="ml-auto text-neutral-5"
            >
              <component
                :is="
                  section.expanded
                    ? 'DaryaOutlineArrowUpIcon'
                    : 'DaryaArrowDownIcon'
                "
                size="16"
              />
            </div>
          </div>

          <div v-if="section.expanded && modelValue" class="relative pl-4 mt-1">
            <ul class="space-y-1">
              <router-link
                v-for="(item, index) in section.items"
                :key="index"
                v-slot="{ isActive, navigate }"
                :to="item.path"
                custom
              >
                <li
                  @click="navigate"
                  class="relative px-2 rounded-md cursor-pointer transition-all duration-200"
                >
                  <span class="flex items-center">
                    <span
                      :class="[
                        'absolute left-0 h-full w-[1.5px] rounded-full',
                        isActive ? 'bg-primary-shade3' : 'bg-neutral-12',
                      ]"
                    />
                    <span
                      class="flex items-center justify-center text-sm font-medium h-10 hover:text-primary-shade3"
                      :class="{
                        'text-primary-shade3': isActive,
                        'text-neutral-5': !isActive,
                      }"
                    >
                      {{ item.title }}
                    </span>
                  </span>
                </li>
              </router-link>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

defineProps(['modelValue']);
defineEmits(['update:modelValue']);

const route = useRoute();
const { t } = useI18n();

const SECTIONS = reactive([
  {
    name: t('sidebar.user_management'),
    icon: 'DaryaOutlineAccountIcon',
    items: [
      { title: t('common.users'), path: '/' },
      { title: t('common.verifications'), path: '/verifications' },
      { title: t('common.certificates'), path: '/certificates' },
    ],
    expanded: false,
  },
  {
    name: t('sidebar.plan_management'),
    icon: 'DaryaOutlineRankingIcon',
    items: [
      { title: t('common.plans'), path: '/programs' },
      { title: t('common.breach_rule'), path: '/breachRule' },
      { title: t('common.level_up_rule'), path: '/levelUpRule' },
      { title: t('common.withdraw'), path: '/withdraw' },
    ],
    expanded: false,
  },
  {
    name: t('sidebar.financial_management'),
    icon: 'DaryaOutlineWalletMoneyIcon',
    items: [
      { title: t('common.symbols'), path: '/symbols' },
      { title: t('common.accounts'), path: '/account' },
      { title: t('common.positions'), path: '/positions' },
      { title: t('common.payment'), path: '/payment' },
    ],
    expanded: false,
  },
  {
    name: t('common.competition'),
    icon: 'DaryaOutlineCupIcon',
    items: [{ title: t('common.competition'), path: '/competition' }],
    expanded: false,
  },
  {
    name: t('sidebar.risk_management'),
    icon: 'DaryaOutlineAlarmIcon',
    items: [
      { title: t('sidebar.dashboard1'), path: '/dashboard1' },
      { title: t('sidebar.dashboard2'), path: '/dashboard2' },
    ],
    expanded: false,
  },
  {
    name: t('common.utilities'),
    icon: 'DaryaOutlineDirectIcon',
    items: [],
    expanded: false,
  },
]);

const toggleSection = (section: any) => {
  if (section.items.length) {
    section.expanded = !section.expanded;
  }
};

const isSectionActive = (section: any) => {
  return section.items.some((item: any) => item.path === route.path);
};
</script>

<style scoped>
li {
  margin: 0;
}
</style>
