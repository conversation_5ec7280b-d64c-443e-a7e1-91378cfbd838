<template>
  <div
    :dir="textDirection"
    class="flex h-screen w-full"
    :class="{ 'flex-row-reverse': isRtl }"
  >
    <SideBar v-model="isSidebarExpanded" />

    <div
      class="flex flex-col flex-1 overflow-x-hidden"
      :class="{
        'ml-[260px]': !isRtl && isSidebarExpanded,
        'ml-[85px]': !isRtl && !isSidebarExpanded,
        'mr-[260px]': isRtl && isSidebarExpanded,
        'mr-[85px]': isRtl && !isSidebarExpanded,
      }"
    >
      <Header :isSidebarExpanded="isSidebarExpanded" />
      <main class="flex-1 p-6 mt-16 bg-lightMood-1">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Header from '@/layouts/Header.vue';
import SideBar from '@/layouts/SideBar.vue';

import { useLocale } from '@/composables/useLocale.js';

const isSidebarExpanded = ref(true);

const { isRtl, textDirection } = useLocale();
</script>
