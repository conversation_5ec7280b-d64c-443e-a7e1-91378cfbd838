<template>
  <header
    class="bg-lightMood-1 fixed top-0 h-16 z-50 flex items-center border-b border-gray-200 px-10 flex-row-reverse"
    :class="{
      'w-[calc(100%-260px)]': isSidebarExpanded,
      'w-[calc(100%-85px)]': !isSidebarExpanded,
    }"
  >
    <div class="flex items-center gap-5">
      <div class="h-6 border-l border-gray-200 mx-1"></div>

      <!-- notification button -->
      <div
        aria-label="Notifications"
        class="space-x-2 rounded-full hover:bg-gray-100 transition"
      >
        <DaryaNotificationIcon />
      </div>

      <div class="h-6 border-r border-gray-200 mx-1"></div>
      <DPopper
        v-model="isDropdownOpen"
        placement="bottom-end"
        :arrow="false"
        offset-y="6"
        append-to-body
        @click="isDropdownOpen = !isDropdownOpen"
      >
        <template #default>
          <div class="flex items-center ml-6 space-x-2 cursor-pointer">
            <img
              src="@/assets/images/avatar.svg"
              alt="User avatar"
              class="w-8 h-8 rounded-full object-cover"
            />

            <span class="text-sm font-medium text-gray-700">
              {{ user?.given_name }} {{ user?.family_name }}</span
            >

            <DaryaArrowDownIcon size="16" class="text-gray-500 ml-6" />
          </div>
        </template>

        <template #content>
          <div
            class="bg-white shadow-lg border border-gray-100 rounded-md w-48"
          >
            <ul>
              <li
                class="flex items-center p-3 hover:bg-gray-200 cursor-pointer border-b border-gray-200 gap-2"
                @click="navigateToProfile"
              >
                <span class="text-sm">Profile</span>
              </li>
              <li
                class="flex items-center p-3 hover:bg-gray-200 cursor-pointer gap-2"
                @click="handleLogout"
              >
                <span class="text-red-500 text-sm">Logout</span>
              </li>
            </ul>
          </div>
        </template>
      </DPopper>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue';
import { useAuth, useUserLogoutMutation } from '@/modules/user/index';

defineProps({
  isSidebarExpanded: {
    type: Boolean,
    default: false,
  },
});

const { clearAuth, user } = useAuth();
const { mutateAsync: logout } = useUserLogoutMutation();

const isDropdownOpen = ref(false);

const handleLogout = async (): Promise<void> => {
  try {
    await logout();
  } catch (error) {
    // Handle error if needed
  } finally {
    clearAuth();
    isDropdownOpen.value = false;
  }
};

const navigateToProfile = () => {
  isDropdownOpen.value = false;
};
</script>
