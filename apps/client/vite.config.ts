import path from 'path';
import { defineConfig, type PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue';
import Pages from 'vite-plugin-pages';
import Layouts from 'vite-plugin-vue-layouts';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import tailwindcss from '@tailwindcss/vite';

import vueDevTools from 'vite-plugin-vue-devtools';

/// <reference types="vitest" />
export default defineConfig(({ mode }) => {
  const plugins = [
    tailwindcss(),
    vue(),
    vueDevTools(),
    Pages({
      dirs: 'src/screens',
    }),
    Layouts(),
    VueI18nPlugin({
      include: path.resolve(__dirname, 'src/locales/**'),
    }),
  ];

  return {
    plugins,
    resolve: {
      alias: [
        { find: '@', replacement: path.resolve(__dirname, 'src') },
        { find: './runtimeConfig', replacement: './runtimeConfig.browser' },
      ],
      dedupe: ['vue'],
    },
    server: { port: 3000 },
    build: { sourcemap: true, chunkSizeWarningLimit: 13000 },
  };
});
