# Base layer with Node.js and pnpm
FROM node:18 AS base
RUN npm install -g pnpm && pnpm install

# Libs layer for libraries
FROM base AS libs
RUN pnpx nx run-many --target=build --projects=darya-icon-package,design-system --parallel

# App layer for the client application
FROM base AS app
COPY apps/client ./apps/client
RUN pnpm run build:client

# Production layer for running the application
FROM node:18 AS production
WORKDIR /apps/client
COPY --from=app /apps/client/dist ./dist
EXPOSE 3000
CMD ["pnpm", "run", "serve"]