export interface UserValidateOtp {
  userId: string;
  otp: string;
}

export interface UserCredentials {
  username: string;
  password: string;
}

export interface resetPasswordFormModel {
  token: string;
  newPassword: string;
  confirmNewPassword: string;
  username: string;
}

export interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

export interface userForgetPasswordFormModel {
  userName: string;
}

export interface userFormModel {
  email: string;
  password: string;
}

export interface User {
  jti: string;
  nameid: string;
  email: string;
  given_name: string;
  family_name: string;
  role: string;
  exp: number;
}
