<template>
  <div>
    <DataStateWrapper
      :isLoading="dataState.isLoading.value"
      :hasError="dataState.hasError.value"
      :hasNoData="dataState.hasNoData.value"
      :isRefetching="dataState.isRefetching.value"
      :errorConfig="dataState.errorConfig.value"
      :emptyConfig="dataState.emptyConfig.value"
    >
      <div
        class="flex items-center rounded-2xl border border-neutral-13 px-6 py-4 mt-8 w-4/6"
      >
        <img
          src=" @/assets/images/avatar.svg"
          alt="User Avatar"
          class="w-20 h-20 rounded-full object-cover"
        />

        <div class="flex flex-col ms-4">
          <span class="pb-2 text-base font-medium">{{
            userData.fullName
          }}</span>
          <span class="text-base text-neutral-5">{{ userData.email }}</span>
        </div>
      </div>

      <div class="rounded-2xl border border-neutral-13 px-6 py-4 mt-6">
        <h1 class="text-lg font-bold mb-6">
          {{ t('profile.personal_information') }}
        </h1>
        <div class="grid grid-cols-3 gap-x-24 overflow-hidden">
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.first_name') }}
          </div>
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.last_name') }}
          </div>
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.country') }}
          </div>

          <div class="pb-8 text-base">{{ userData.firstName }}</div>
          <div class="pb-8 text-base">{{ userData.lastName }}</div>
          <div class="pb-8 text-base">{{ userData.country?.fullName }}</div>

          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.phone') }}
          </div>
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.gender') }}
          </div>
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.address') }}
          </div>

          <div class="pb-8 text-base">{{ userData.phoneNumberWithoutExt }}</div>
          <div class="pb-8 text-base">{{ genderText }}</div>
          <div class="pb-8 text-base">{{ userData.address }}</div>

          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.date_of_birth') }}
          </div>
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.postal_code') }}
          </div>
          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.user_name') }}
          </div>

          <div class="pb-8 text-base">
            {{
              userData.dateOfBirth ? d(userData.dateOfBirth, 'shortDate') : ''
            }}
          </div>
          <div class="pb-8 text-base">{{ userData.postalCode }}</div>
          <div class="pb-8 text-base">{{ userData.userName }}</div>

          <div class="pb-2 text-neutral-9 text-xs font-bold">
            {{ t('profile.email_address') }}
          </div>
          <div />
          <div />
          <div class="pb-8 text-base">{{ userData.email }}</div>
          <div />
          <div />
        </div>
      </div>
    </DataStateWrapper>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useUserProfile } from '@/modules/user';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const { t, d } = useI18n();
const { userData, genderText, isError, isFetching, status, error } =
  useUserProfile();

// Data state management - single composable handles everything
const dataState = useDataState(
  status,
  userData,
  isFetching,
  isError,
  error,
  'no-data' // Empty state type
);
</script>
