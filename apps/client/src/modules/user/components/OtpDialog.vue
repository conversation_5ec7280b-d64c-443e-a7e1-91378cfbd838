<template>
  <DDialog
    :open="isOpenOtpDialog"
    @update:open="emit('close')"
    :showCloseIcon="false"
    :closeOnClickOutside="false"
    maxWidth="lg"
  >
    <template #body>
      <!-- Header -->
      <div class="relative px-1 mb-8">
        <div class="flex justify-center">
          <div v-if="hasError" class="bg-error-tint3 p-2 rounded">
            <DaryaBoldCloseCircleIcon size="32" class="text-error" />
          </div>

          <div v-else class="bg-primary-tint8 p-2 rounded">
            <DaryaOutlineMessagesIcon size="32" class="text-primary" />
          </div>
        </div>

        <button
          @click="emit('close')"
          class="absolute top-0 right-4 p-1 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Close"
        >
          <DaryaCloseIcon />
        </button>
      </div>

      <!-- Content -->
      <div class="text-center px-4">
        <h2 class="text-black text-lg font-bold leading-7 font-['Poppins']">
          {{ t('login.verification_code') }}
        </h2>
        <p class="text-zinc-600 text-[10px] font-normal mb-8 font-['Poppins']">
          {{ t('login.we_sent_code_to') }}
          <span class="font-medium">{{ email }}</span>
        </p>

        <!-- OTP Inputs -->
        <div class="flex justify-center space-x-3 mb-8">
          <input
            v-for="(digit, index) in otpLength"
            :key="index"
            :ref="(el) => (otpRefs[index] = el as HTMLInputElement | null)"
            type="number"
            maxlength="1"
            v-model="otpDigits[index]"
            @input="handleInput($event, index)"
            @keydown.backspace="handleBackspace($event, index)"
            inputmode="numeric"
            pattern="[0-9]*"
            :aria-label="`OTP digit ${index + 1}`"
            class="w-16 h-14 text-2xl font-bold text-center border rounded-lg transition-all duration-200"
            :class="[
              'border',
              props.hasError
                ? 'border-error'
                : otpDigits[index]
                  ? 'border-primary-tint2'
                  : 'border-neutral-11',
              'focus:border-primary-tint2 focus:ring-primary-tint2 focus:bg-primary/5 focus:outline-none',
            ]"
          />
        </div>

        <!-- Timer Display -->
        <div v-if="timeLeft > 0" class="flex justify-center items-center mb-4">
          <DaryaOutlineTimerIcon class="text-primary" />
          <!-- Placeholder for TimerIcon -->
          <span class="ms-1 text-sm">{{ formatTime(timeLeft) }}</span>
        </div>

        <!-- Resend Section -->
        <div
          v-if="timeLeft === 0"
          class="text-[10px] text-neutral-5 font-normal flex justify-center items-center gap-1"
        >
          <span v-if="timeLeft === 0">
            {{ t('login.did_not_get_the_code') }}
          </span>

          <button
            @click="resendCode"
            :disabled="timeLeft > 0"
            :aria-disabled="timeLeft > 0"
            class="text-primary text-xs font-bold disabled:text-gray-400"
          >
            {{ t('login.click_to_resend') }}
          </button>
        </div>
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onUnmounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'resend-code'): void;
  (e: 'verify-code', code: string): void;
}>();

const props = defineProps<{
  isOpenOtpDialog: boolean;
  email?: string | string[];
  otpLength?: number;
  hasError: boolean;
}>();

const { t } = useI18n();

const otpLength = props.otpLength ?? 6;
const otpDigits = ref<string[]>(Array(otpLength).fill(''));
const otpRefs = ref<(HTMLInputElement | null)[]>([]);
const code = computed(() => otpDigits.value.join(''));

const timeLeft = ref(0);
let timerInterval: ReturnType<typeof setInterval> | null = null;

function startTimer() {
  if (timerInterval) clearInterval(timerInterval);
  timeLeft.value = 120;
  timerInterval = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--;
    } else {
      if (timerInterval) clearInterval(timerInterval);
      timerInterval = null;
    }
  }, 1000);
}

function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

watch(code, (newCode) => {
  if (newCode.length === otpLength) {
    emit('verify-code', newCode);
  }
});
// Start timer and focus first input when dialog opens
watch(
  () => props.isOpenOtpDialog,
  (isOpen) => {
    if (isOpen) {
      resetOtp();
      startTimer();
      nextTick(() => otpRefs.value[0]?.focus());
    } else {
      if (timerInterval) clearInterval(timerInterval);
    }
  }
);

// Watch for OTP completion
watch(
  otpDigits,
  (val) => {
    const code = val.join('');
    if (code.length === otpLength && !code.includes('')) {
      emit('verify-code', code);
    }
  },
  { deep: true }
);

// OTP Management
function handleInput(event: Event, index: number) {
  const el = event.target as HTMLInputElement;
  const value = el.value.replace(/[^0-9]/g, '').slice(0, 1);

  // Update otpDigits reactively by creating a new array
  const newDigits = [...otpDigits.value];
  newDigits[index] = value;
  otpDigits.value = newDigits;

  if (value && index < otpLength - 1) {
    nextTick(() => otpRefs.value[index + 1]?.focus());
  }
}

function handleBackspace(event: KeyboardEvent, index: number) {
  if (!otpDigits.value[index] && index > 0) {
    nextTick(() => otpRefs.value[index - 1]?.focus());
  }
}

// Resend Logic
function resendCode() {
  if (timeLeft.value > 0) return;

  emit('resend-code');
  resetOtp();
  startTimer();
}

// Reset OTP inputs
function resetOtp() {
  otpDigits.value = Array(otpLength).fill('');
}

// Cleanup timer on unmount
onUnmounted(() => {
  if (timerInterval) clearInterval(timerInterval);
});
</script>

<style scoped>
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}
</style>
