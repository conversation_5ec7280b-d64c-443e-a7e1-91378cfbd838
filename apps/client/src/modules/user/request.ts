import http from '@/services/http';

import { USER_API, USER_MANAGE_API } from '@/configs/env';

export function login(data) {
  return http.post(`${USER_API}/User/loginAsync`, data);
}

export function verifyOtp(data) {
  return http.post(`${USER_API}/User/ValidateOtpAsync`, data);
}

export function verifyForgetPasswordOtp(data) {
  return http.post(`${USER_API}/User/ValidateOtpForgotPasswordAsync`, data);
}

export function createUser(data) {
  return http.post(`${USER_API}/User/CreateUser`, data);
}

export function logoutUser() {
  return http.post(`${USER_API}/User/LogoutAsync`);
}

export function resetPasswordRequest(data) {
  return http.post(`${USER_API}/User/ResetPasswordRequestAsync`, data);
}
export function forgotPasswordRequest(data) {
  return http.put(`${USER_API}/User/ForgotPasswordAsync`, data);
}

export function getAllCountry() {
  return http.get(`${USER_MANAGE_API}/Country/GetAllCountryAsync`);
}

export function getGenderTypeEnum() {
  return http.get(`${USER_API}/User/GetGenderTypeEnums`);
}

export function getUserDetailById(userId) {
  return http.get(`${USER_API}/User/GetByUserIdAsync`, {
    params: { userId: userId },
  });
}
