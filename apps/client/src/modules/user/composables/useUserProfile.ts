import { computed } from 'vue';

import {
  useGetUserDetailById,
  useAuth,
  useGetGenderTypeEnum,
} from '@/modules/user';

export default function useUserProfile() {
  const { user } = useAuth();
  const { userData, isError, isFetching, status, error } = useGetUserDetailById(
    user.value?.nameid,
    true
  );
  const { genderTypeEnum } = useGetGenderTypeEnum();

  const genderText = computed(() => {
    return (
      genderTypeEnum.value.find(
        (item) => item.value === String(userData.value?.genderTypeId)
      )?.text || ''
    );
  });

  return { userData, genderText, isError, isFetching, status, error };
}
