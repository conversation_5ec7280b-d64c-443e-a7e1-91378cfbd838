import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { login } from '../request';

import { UserCredentials } from '@/modules/user';

type LoginResponse = Awaited<ReturnType<typeof login>>;

export interface UseUserLoginMutationOptions
  extends Omit<
    UseMutationOptions<LoginResponse, Error, MaybeRef<UserCredentials>>,
    'mutationFn'
  > {}

export default function useUserLoginMutation(
  options?: UseUserLoginMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    LoginResponse,
    Error,
    MaybeRef<UserCredentials>
  >({
    mutationFn: async (credentials) => {
      const { username, password } = unref(credentials);
      return login({ username, password });
    },
    onError: (err) => {},
    ...options,
  });

  return {
    loginUser: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
