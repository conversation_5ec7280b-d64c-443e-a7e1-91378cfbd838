import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { verifyForgetPasswordOtp } from '../request';

import type { UserValidateOtp } from '@/modules/user';

type OtpResponse = Awaited<ReturnType<typeof verifyForgetPasswordOtp>>;

export interface UseValidateOtpMutationOptions
  extends Omit<
    UseMutationOptions<OtpResponse, Error, MaybeRef<UserValidateOtp>>,
    'mutationFn'
  > {
  onSuccess?: (
    data: OtpResponse,
    variables: MaybeRef<UserValidateOtp>,
    context: unknown
  ) => void;
  onError?: (
    error: Error,
    variables: MaybeRef<UserValidateOtp>,
    context: unknown
  ) => void;
}

export default function useValidateForgetPasswordOtpMutation(
  options?: UseValidateOtpMutationOptions
) {
  const {
    onSuccess: userOnSuccess,
    onError: userOnError,
    ...restOptions
  } = options || {};

  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    OtpResponse,
    Error,
    MaybeRef<UserValidateOtp>
  >({
    mutationFn: async (credentials) => {
      const { userId, otp } = unref(credentials);

      return verifyForgetPasswordOtp({ userId, otp });
    },
    onSuccess: (data, variables, context) => {
      userOnSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      userOnError?.(error, variables, context);
    },
    ...restOptions,
  });

  return {
    verifyForgetPasswordOtp: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
