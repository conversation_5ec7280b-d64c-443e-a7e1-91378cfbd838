import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { resetPasswordRequest } from '../request';

import type { resetPasswordFormModel, ApiResponse } from '@/modules/user';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<resetPasswordFormModel>>,
    'mutationFn'
  > {}

export default function useResetPasswordMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<resetPasswordFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await resetPasswordRequest(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    resetPassword: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
