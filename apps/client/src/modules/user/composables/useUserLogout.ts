import { useMutation } from '@tanstack/vue-query';

import { logoutUser } from '../request';

type LogoutResponse = Awaited<ReturnType<typeof logoutUser>>;

export default function useUserLogoutMutation() {
  const { mutate, mutateAsync, status, isError } = useMutation<
    LogoutResponse,
    Error
  >({
    mutationFn: logoutUser,
    onError: (error) => {},
  });

  return {
    logout: mutate,
    mutateAsync,
    isError,
    status,
  } as const;
}
