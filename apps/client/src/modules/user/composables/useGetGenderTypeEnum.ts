import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getGenderTypeEnum } from '../request';

type GenderTypeResponse = Awaited<ReturnType<typeof getGenderTypeEnum>>;

export default function useGetGenderTypeEnum() {
  const { status, isError, data, refetch } = useQuery<
    GenderTypeResponse,
    Error
  >({
    queryKey: ['genderTypeEnum'],
    queryFn: getGenderTypeEnum,
  });

  const genderTypeEnum = computed(() => data.value?.data?.data ?? []);

  return {
    getGenderType: refetch,
    genderTypeEnum: genderTypeEnum,
    isError,
    status,
  } as const;
}
