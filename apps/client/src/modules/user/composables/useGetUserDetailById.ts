import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getUserDetailById } from '../request';

export default function useGetUserDetailById(
  userId: MaybeRef<string> | undefined,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['userDetail', userIdRef.value]),
    queryFn: () => {
      const userId = userIdRef.value;

      if (!userId) throw new Error('User ID is required');

      return getUserDetailById(userId);
    },
    enabled: shouldFetchRef,
  });

  const userData = computed(() => data.value?.data?.data ?? []);

  return {
    getUserDetailById: refetch,
    userData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
