import { ref, computed } from 'vue';
import { jwtDecode } from 'jwt-decode';

import type { User } from '@/modules/user';

const authToken = ref<string | null>(null);
const currentUser = ref<User | null>(null);

function clearAuth(): void {
  authToken.value = null;
  currentUser.value = null;

  localStorage.removeItem('token');

  window.location.href = '/login';
}

function initializeAuth(): void {
  const token = localStorage.getItem('token');

  if (token) {
    authToken.value = token;

    try {
      currentUser.value = jwtDecode<User>(token);
    } catch (error) {
      clearAuth();
    }
  }
}

initializeAuth();

export default function useAuth() {
  const setToken = (token: string) => {
    authToken.value = token;

    localStorage.setItem('token', token);

    try {
      currentUser.value = jwtDecode<User>(token);
    } catch (error) {
      clearAuth();
    }
  };

  return {
    user: currentUser,
    token: authToken,
    setToken,
    clearAuth,
    isAuthenticated: computed(() => !!currentUser.value),
    userRole: computed(() => currentUser.value?.role ?? ''),
    userId: computed(() => currentUser.value?.nameid ?? ''),
    tokenExpiration: computed(() =>
      currentUser.value?.exp ? new Date(currentUser.value.exp * 1000) : null
    ),
  };
}
