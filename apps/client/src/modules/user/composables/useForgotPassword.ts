import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { forgotPasswordRequest } from '../request';
import type { userForgetPasswordFormModel, ApiResponse } from '@/modules/user';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<
      ApiResponse,
      Error,
      MaybeRef<userForgetPasswordFormModel>
    >,
    'mutationFn'
  > {}

export default function useForgetPasswordRequestMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<userForgetPasswordFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await forgotPasswordRequest(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    forgetPasswordRequest: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
