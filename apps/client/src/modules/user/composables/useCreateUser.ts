import { isAxiosError } from 'axios';
import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { createUser } from '../request';

import type { ApiResponse, userFormModel } from '@/modules/user';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<userFormModel>>,
    'mutationFn'
  > {}

export default function useCreateUserMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<userFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await createUser(unref(formModel));

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    createUser: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
