import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { useAuth, type UserValidateOtp } from '@/modules/user/index';

import { verifyOtp } from '../request';

type OtpResponse = Awaited<ReturnType<typeof verifyOtp>>;

export interface UseValidateOtpMutationOptions
  extends Omit<
    UseMutationOptions<OtpResponse, Error, MaybeRef<UserValidateOtp>>,
    'mutationFn'
  > {
  onSuccess?: (
    data: OtpResponse,
    variables: MaybeRef<UserValidateOtp>,
    context: unknown
  ) => void;
  onError?: (
    error: Error,
    variables: MaybeRef<UserValidateOtp>,
    context: unknown
  ) => void;
}

export default function useValidateOtpMutation(
  options?: UseValidateOtpMutationOptions
) {
  const { setToken } = useAuth();

  const {
    onSuccess: userOnSuccess,
    onError: userOnError,
    ...restOptions
  } = options || {};

  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    OtpResponse,
    Error,
    MaybeRef<UserValidateOtp>
  >({
    mutationFn: async (credentials) => {
      const { userId, otp } = unref(credentials);

      return verifyOtp({ userId, otp });
    },
    onSuccess: (data, variables, context) => {
      const token = data.data?.data?.accessToken;

      try {
        setToken(token);
      } catch (e) {}

      userOnSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      userOnError?.(error, variables, context);
    },
    ...restOptions,
  });

  return {
    verifyOtp: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
