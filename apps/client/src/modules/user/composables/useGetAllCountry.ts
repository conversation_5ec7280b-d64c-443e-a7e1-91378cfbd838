import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllCountry } from '../request';

type CountryResponse = Awaited<ReturnType<typeof getAllCountry>>;

export default function useGetAllCountry() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<CountryResponse, Error>({
    queryKey: ['countryList'],
    queryFn: getAllCountry,
  });

  const countryList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllCountry: refetch,
    countryList: countryList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
