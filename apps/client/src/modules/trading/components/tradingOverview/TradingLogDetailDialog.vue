<template>
  <DDialog
    :open="isOpenTradingLogDetail"
    @update:open="emit('close')"
    :showCloseIcon="false"
    :closeOnClickOutside="false"
    maxWidth="lg"
  >
    <template #body>
      <div class="p-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="bg-primary-tint8 p-2 rounded">
              <!-- your dollar icon -->
              <DaryaOutlineDollarCircleIcon size="32" class="text-primary" />
            </div>
          </div>

          <button
            @click="emit('close')"
            class="p-1 hover:bg-gray-100 rounded-full transition-colors"
            aria-label="Close"
          >
            <DaryaCloseIcon />
          </button>
        </div>

        <!-- Trading Log Detail Data with State Management -->
        <DataStateWrapper
          :isLoading="detailState.isLoading.value"
          :hasError="detailState.hasError.value"
          :hasNoData="detailState.hasNoData.value"
          :isRefetching="detailState.isRefetching.value"
          :errorConfig="detailState.errorConfig.value"
          :emptyConfig="customEmptyConfig"
          :onRetry="retryDetailData"
        >
          <div class="mt-5">
            <span class="font-bold text-lg">
              {{ t('common.details') }} {{ tradingLogDetailData.symbol }}
            </span>
          </div>

          <div class="mt-6">
            <div class="flex items-center w-full pb-4">
              <span class="text-neutral-5 text-xs font-normal pr-2">
                {{ t('trading.target_profit') }}
              </span>
              <div class="flex-1 bg-neutral-11 h-[0.5px]"></div>
            </div>

            <div class="pb-3 flex justify-between text-base">
              <span>{{ t('trading.target_price') }}</span>
              <span>${{ tradingLogDetailData.targetPrice }}</span>
            </div>
            <div class="pb-3 flex justify-between text-base">
              <span>{{ t('trading.profit_aim') }} $</span>
              <span>${{ tradingLogDetailData.profitAim }}</span>
            </div>
            <div class="flex justify-between text-base">
              <span>{{ t('trading.profit_aim') }} %</span>
              <span>{{ tradingLogDetailData.profitAimPercent }}%</span>
            </div>
          </div>

          <!-- Stop Loss section -->
          <div class="mt-5">
            <div class="flex items-center w-full pb-4">
              <span class="text-neutral-5 text-xs font-normal pr-2">
                {{ t('trading.stop_loss') }}
              </span>
              <div class="flex-1 bg-neutral-11 h-[0.5px]"></div>
            </div>

            <div class="pb-3 flex justify-between text-base">
              <span>{{ t('trading.stop_price') }}</span>
              <span>${{ tradingLogDetailData.stopPrice }}</span>
            </div>
            <div class="pb-3 flex justify-between text-base">
              <span>{{ t('trading.risk') }} $</span>
              <span>${{ tradingLogDetailData.risk }}</span>
            </div>
            <div class="flex justify-between text-base">
              <span>{{ t('trading.risk') }} %</span>
              <span>{{ tradingLogDetailData.riskPercent }}%</span>
            </div>
          </div>
        </DataStateWrapper>
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

import { useGetTradingLogDetailById } from '@/modules/account';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

type TradingLogDetailData = {
  symbol: string;
  targetPrice: number;
  profitAim: number;
  profitAimPercent: number;
  stopPrice: number;
  risk: number;
  riskPercent: number;
};

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const props = defineProps<{
  isOpenTradingLogDetail: boolean;
  logDetailId?: number;
}>();

const { t } = useI18n();

// Trading Log Detail Data with state management
const {
  tradingLogDetailData,
  getTradingLogDetailById,
  status,
  isFetching,
  isError,
  error,
} = useGetTradingLogDetailById(props.logDetailId || 0, false);

const detailState = useDataState(
  status,
  tradingLogDetailData,
  isFetching,
  isError,
  error,
  'trading-data'
);

// Override empty state config for trading log details
const customEmptyConfig = computed(() => ({
  title: 'No Trading Log Details',
  message: 'Unable to load trading log details at this time.',
  image: 'empty-state.svg',
  alt: 'No trading log details',
}));

// Retry function
const retryDetailData = () => getTradingLogDetailById();

// Watch for dialog open and fetch data
watch(
  () => props.isOpenTradingLogDetail,
  (isOpen) => {
    if (isOpen && props.logDetailId && props.logDetailId > 0) {
      getTradingLogDetailById();
    }
  }
);
</script>
