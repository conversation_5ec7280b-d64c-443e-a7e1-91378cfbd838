import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllTransAction } from '../request';

type transactionResponse = Awaited<ReturnType<typeof getAllTransAction>>;

export default function useGetAllTransaction() {
  const { data, refetch, isError, status, isFetching, error } = useQuery<
    transactionResponse,
    Error
  >({
    queryKey: ['transactionList'],
    queryFn: getAllTransAction,
  });

  const transactionList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllTransaction: refetch,
    transactionList,
    isError,
    isFetching,
    error,
    status,
  } as const;
}
