import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getTransactionDetailById } from '../request';

export default function useGetTransactionDetailById(
  id: MaybeRef<string>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const idRef = toRef(id);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['transactionDetail', idRef.value]),
    queryFn: () => {
      const id = idRef.value;

      if (!id) throw new Error('ID is required');

      return getTransactionDetailById(id);
    },
    enabled: shouldFetchRef,
  });

  const transActionDetailData = computed(() => data.value?.data?.data ?? []);

  return {
    getTransActionDetailById: refetch,
    transActionDetailData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
