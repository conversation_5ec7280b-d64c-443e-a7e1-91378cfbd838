<template>
  <DDialog
    :open="isOpenTransactionHistoryDetail"
    @update:open="emit('close')"
    :showCloseIcon="false"
    :closeOnClickOutside="false"
    maxWidth="w-full"
  >
    <template #body>
      <div class="flex items-center justify-between px-1 mb-8">
        <div class="flex items-center">
          <div class="bg-primary-tint8 p-2 rounded">
            <DaryaOutlineDollarCircleIcon size="32" class="text-primary" />
          </div>

          <div class="flex flex-col ms-4">
            <span class="pb-1 font-bold text-lg">
              {{ getTypeLabel(transActionDetailData?.transactionType) }}
            </span>
            <span class="text-neutral-5 font-xs">
              {{ t('transaction.invoice_id') }}
              {{ transActionDetailData?.refrenceId }}
            </span>
          </div>
        </div>

        <button
          @click="emit('close')"
          class="p-1 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Close"
        >
          <DaryaCloseIcon />
        </button>
      </div>

      <!-- Transaction Detail Content with State Management -->
      <DataStateWrapper
        :isLoading="dataState.isLoading.value"
        :hasError="dataState.hasError.value"
        :hasNoData="dataState.hasNoData.value"
        :isRefetching="dataState.isRefetching.value"
        :errorConfig="dataState.errorConfig.value"
        :emptyConfig="dataState.emptyConfig.value"
      >
        <div class="space-y-6 px-4">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-sm font-bold">
                <span class="text-neutral-5">
                  {{ transActionDetailData?.amount }}
                </span>
                <span class="text-neutral-2 ms-0.5">
                  {{ transActionDetailData?.currency }}
                </span>
              </span>

              <button
                class="flex items-center border border-success-tint1 bg-success/15 p-2 text-success-shade2 rounded-lg"
              >
                <DaryaBoldTickCircleIcon size="16" />
                <span class="text-xs ps-1">
                  {{ transActionDetailData?.status }}
                </span>
              </button>
            </div>

            <div class="flex justify-between items-center text-sm">
              <span class="text-neutral-8">Discount</span>
              <span>0.00 {{ transActionDetailData?.currency }}</span>
            </div>

            <div class="flex justify-between items-center text-sm">
              <span class="text-neutral-8">Gross Amount</span>
              <span>
                {{ transActionDetailData?.amount }}
                {{ transActionDetailData?.currency }}
              </span>
            </div>

            <div class="flex justify-between items-center text-sm">
              <span class="text-neutral-8">{{ t('common.created') }}</span>
              <span>
                {{
                  transActionDetailData?.createDate
                    ? d(new Date(transActionDetailData?.createDate), 'longDate')
                    : '-'
                }}
              </span>
            </div>

            <div class="flex justify-between items-center text-sm">
              <span class="text-neutral-8">{{ t('common.executed') }}</span>
              <span>
                {{
                  transActionDetailData?.createDate
                    ? d(new Date(transActionDetailData?.createDate), 'longDate')
                    : '-'
                }}
              </span>
            </div>
          </div>

          <button
            class="border border-primary rounded-lg p-3 w-full text-sm font-bold text-primary my-8 cursor-pointer"
          >
            {{ t('common.support') }}
          </button>
        </div>
      </DataStateWrapper>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const props = defineProps<{
  isOpenTransactionHistoryDetail: boolean;
  transActionDetailData: any;
  isLoading?: boolean;
  hasError?: boolean;
  error?: any;
  onRetry?: () => void;
}>();

const { t, d } = useI18n();

// Create mock refs for state management when data is passed as prop
const mockStatus = computed(() => {
  if (props.isLoading) return 'pending';
  if (props.hasError) return 'error';
  return 'success';
});

const mockData = computed(() => props.transActionDetailData);
const mockIsFetching = computed(() => props.isLoading || false);
const mockIsError = computed(() => props.hasError || false);
const mockError = computed(() => props.error);

// Use data state for consistent state management
const dataState = useDataState(
  mockStatus,
  mockData,
  mockIsFetching,
  mockIsError,
  mockError,
  'transactions'
);

function getTypeLabel(type) {
  if (type === 1) return 'Withdraw';
  if (type === 2) return 'Deposit';
}
</script>
