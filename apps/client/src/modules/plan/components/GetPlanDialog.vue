<template>
  <DDialog
    :open="isOpenGetPlanDialog"
    @update:open="handleClose"
    :showCloseIcon="false"
    :closeOnClickOutside="false"
    customWidth="890px"
  >
    <template #body>
      <div class="flex gap-5">
        <!-- Left Column -->
        <div
          class="bg-primary-tint10 border border-primary-tint6 rounded-lg px-6 py-4 w-[420px]"
        >
          <template v-if="planDetailData">
            <div class="flex items-center gap-2 mb-16">
              <img
                src="@/assets/images/plan-standard1.svg"
                alt="plan image"
                class="object-cover w-20 h-20"
              />
              <span class="text-neutral-6 font-medium text-base">
                {{ t('challenge.total_amount') }}
              </span>
              <span class="text-primary-shade3 font-bold text-2xl">
                ${{ planDetailData.price }}
              </span>
            </div>

            <div class="flex flex-col gap-y-6 mb-11">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('challenge.plan') }}
                  </span>
                </div>

                <span class="text-base font-bold">
                  {{ planDetailData.title }}
                  {{ planDetailData.symbolNameCurrency }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('challenge.platform') }}
                  </span>
                </div>
                <span class="text-base font-bold">MT5</span>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('common.server') }}
                  </span>
                </div>

                <span class="text-base font-bold">
                  {{ planDetailData.serverGroups }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('common.account_type') }}
                  </span>
                </div>

                <span class="text-base font-bold">
                  {{ PLAN_TYPE_TEXT[planDetailData.planTypeId] }}</span
                >
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('common.price') }}
                  </span>
                </div>

                <span class="text-base font-bold">
                  ${{ planDetailData.price }}
                </span>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('challenge.discount') }}
                  </span>
                </div>

                <span class="text-base font-bold">-$0</span>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <DaryaFillStreakOnIcon class="text-primary-tint7" />
                  <span class="text-base font-medium text-neutral-5">
                    {{ t('challenge.total_amount') }}
                  </span>
                </div>
                <span class="text-base font-bold">
                  ${{ planDetailData.price }}
                </span>
              </div>
            </div>
          </template>
        </div>

        <!-- Right Column -->
        <div class="right-column w-[420px]">
          <div class="flex justify-between items-center">
            <span class="text-xl font-bold">
              {{
                !selectedMethod
                  ? 'Select Payment Method'
                  : 'Payment information'
              }}
            </span>

            <button
              @click="handleClose"
              class="p-1 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Close"
            >
              <DaryaCloseIcon />
            </button>
          </div>

          <div class="mt-8" v-if="!selectedMethod">
            <!-- Payment Methods with State Management -->
            <DataStateWrapper
              :isLoading="paymentTemplateState.isLoading.value"
              :hasError="paymentTemplateState.hasError.value"
              :hasNoData="paymentTemplateState.hasNoData.value"
              :isRefetching="paymentTemplateState.isRefetching.value"
              :errorConfig="paymentTemplateState.errorConfig.value"
              :emptyConfig="paymentTemplateState.emptyConfig.value"
              :onRetry="retryPaymentTemplates"
            >
              <div class="flex flex-col gap-y-4">
                <div
                  v-for="method in paymentTemplateList"
                  :key="method.id"
                  @click="handlesSelectMethod(method)"
                  :class="[
                    'flex rounded-3xl py-4 px-6 cursor-pointer',
                    selectedMethod === method.id
                      ? 'bg-primary/15'
                      : 'border border-neutral-13 hover:border-primary-tint2',
                  ]"
                >
                  <img
                    src="@/assets/images/bank.svg"
                    :alt="method.name"
                    class="w-14 h-14 me-4"
                  />

                  <div class="flex flex-col">
                    <span class="text-neutral-3 font-bold text-base pb-3">
                      {{ method.name }}
                    </span>
                    <span class="text-sm pb-2">
                      <span class="text-neutral-7 pe-2">
                        {{ t('challenge.processing_time') }}
                      </span>
                      <span class="text-neutral-3" />
                    </span>
                    <span class="text-sm pb-2">
                      <span class="text-neutral-7 pe-2">{{
                        t('common.fee')
                      }}</span>
                      <span class="text-neutral-3" />
                    </span>
                    <span class="text-sm">
                      <span class="text-neutral-7 pe-2">
                        {{ t('common.limits') }}
                      </span>
                      <span class="text-neutral-3" />
                    </span>
                  </div>
                </div>
              </div>
            </DataStateWrapper>
          </div>

          <div v-else class="mt-8 overflow-y-scroll h-[435px]">
            <VeeForm v-slot="{ handleSubmit }">
              <form
                autocomplete="off"
                @submit="handleSubmit($event, onSubmit)"
                class="space-y-3 px-1"
              >
                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="firstName"
                  v-model="formModel.expandData.firstName"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    placeholder="First Name"
                    required
                    :error="errorMessage"
                  />
                </VeeField>

                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="lastName"
                  v-model="formModel.expandData.lastName"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    placeholder="Last Name"
                    required
                    :error="errorMessage"
                  />
                </VeeField>
                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="phoneNumber"
                  v-model="formModel.expandData.phoneNumber"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    placeholder="phoneNumber"
                    required
                    :error="errorMessage"
                  />
                </VeeField>
                <VeeField v-slot="{ field, errorMessage }" name="country">
                  <DSelectBox
                    v-bind="field"
                    v-model="formModel.expandData.countryId"
                    :options="countryListOptions"
                    :placeholder="
                      countryState.isLoading.value
                        ? 'Loading countries...'
                        : 'Country'
                    "
                    :disabled="countryState.isLoading.value"
                    required
                    :error="
                      errorMessage ||
                      (countryState.hasError.value
                        ? 'Failed to load countries'
                        : '')
                    "
                  />
                </VeeField>
                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="address"
                  v-model="formModel.expandData.address"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    placeholder="Address"
                    required
                    :error="errorMessage"
                  />
                </VeeField>
                <div class="grid grid-cols-2 gap-2">
                  <VeeField
                    v-slot="{ field, errorMessage }"
                    name="city"
                    v-model="formModel.expandData.city"
                  >
                    <DInput
                      v-bind="field"
                      type="text"
                      placeholder="City"
                      required
                      :error="errorMessage"
                    />
                  </VeeField>
                  <VeeField
                    v-slot="{ field, errorMessage }"
                    name="zipCode"
                    v-model="formModel.expandData.zipCode"
                  >
                    <DInput
                      v-bind="field"
                      type="text"
                      placeholder="Zip Code"
                      required
                      :error="errorMessage"
                    />
                  </VeeField>
                </div>

                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="Card Number"
                  v-model="formModel.expandData.cardNumber"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    placeholder="Card Number"
                    required
                    :error="errorMessage"
                  />
                </VeeField>

                <div class="grid grid-cols-2 gap-2">
                  <VeeField
                    v-slot="{ field, errorMessage }"
                    name="expiry"
                    v-model="formModel.expandData.expiry"
                  >
                    <DInput
                      v-bind="field"
                      type="text"
                      placeholder="Expiry Date"
                      required
                      :error="errorMessage"
                    />
                  </VeeField>
                  <VeeField
                    v-slot="{ field, errorMessage }"
                    name="cvv"
                    v-model="formModel.expandData.cvv"
                  >
                    <DInput
                      v-bind="field"
                      type="text"
                      placeholder="CVV"
                      required
                      :error="errorMessage"
                    />
                  </VeeField>
                </div>

                <VeeField
                  v-slot="{ field, errorMessage }"
                  name="cardholderName"
                  v-model="formModel.expandData.cardholderName"
                >
                  <DInput
                    v-bind="field"
                    type="text"
                    placeholder="Cardholder Name"
                    required
                    :error="errorMessage"
                  />
                </VeeField>

                <VeeField v-slot="{ field, errorMessage }" name="agreement">
                  <DCheckbox
                    v-bind="field"
                    v-model="formModel.expandData.agreement"
                    :label="' By clicking Pay button, you acknowledge that you have thoroughly read, understood, and agreed to our Terms and Conditions.'"
                    :error="errorMessage"
                    :value="true"
                  />
                </VeeField>

                <div class="flex justify-center gap-2 mt-5">
                  <button
                    type="submit"
                    :disabled="paymentStatus === 'pending'"
                    class="bg-primary text-xs font-bold text-white cursor-pointer rounded-lg border-primary w-full py-3 px-4 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span v-if="paymentStatus === 'pending'"
                      >Processing...</span
                    >
                    <span v-else>Pay ${{ planDetailData.price }}</span>
                  </button>
                </div>

                <!-- Payment Error Display -->
                <div
                  v-if="paymentError && paymentErrorObj"
                  class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg"
                >
                  <p class="text-sm text-red-700">
                    {{
                      paymentErrorObj.message ||
                      'Payment failed. Please try again.'
                    }}
                  </p>
                </div>
              </form>
            </VeeForm>
          </div>
        </div>
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useI18n } from 'vue-i18n';

import {
  useGetAllPaymentTemplate,
  useCreatePaymentMutation,
} from '@/modules/payment';
import { useGetAllCountry } from '@/modules/user';

import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const props = defineProps<{
  isOpenGetPlanDialog: boolean;
  planDetailData: {
    breachRules: null | [];
    description: string;
    drawDownUpdateTime: string;
    firstReward: number;
    id: number;
    initialBalance: number;
    levelUpRules: null;
    newsTrading: false;
    nextPhaseId: number;
    planTypeId: number;
    price: number;
    profitSplitUpto: 0;
    serverGroups: string;
    symbolNameCurrency: string;
    title: string;
    tradeType: number;
  };
}>();

const { t } = useI18n();
const {
  createPayment,
  status: paymentStatus,
  isError: paymentError,
  error: paymentErrorObj,
} = useCreatePaymentMutation();

// Payment Templates with state management
const {
  paymentTemplateList,
  status: paymentTemplateStatus,
  isFetching: paymentTemplateFetching,
  isError: paymentTemplateError,
  error: paymentTemplateErrorObj,
  getAllPaymentTemplate,
} = useGetAllPaymentTemplate();

const paymentTemplateState = useDataState(
  paymentTemplateStatus,
  paymentTemplateList,
  paymentTemplateFetching,
  paymentTemplateError,
  paymentTemplateErrorObj,
  'no-data'
);

const selectedMethod = ref();
const formModel = reactive({
  amount: props.planDetailData.price,
  paymentGatewayId: null,
  planId: props.planDetailData.id,
  expandData: {
    firstName: '',
    lastName: '',
    countryId: '',
    address: '',
    city: '',
    zipCode: '',
    cardNumber: '',
    cardholderName: '',
    phoneNumber: '',
    expiry: '',
    cvv: '',
    agreement: false,
  },
  serviceType: 1,
  paymentTemplateId: null,
});
const PLAN_TYPE = {
  Pro: 2,
  Premium: 3,
  Standard: 4,
};

const handlesSelectMethod = (data) => {
  selectedMethod.value = data.id;
  formModel.paymentGatewayId = data.paymentGatewayId;
  formModel.paymentTemplateId = data.id;
};

// Country List with state management
const {
  countryList,
  status: countryStatus,
  isFetching: countryFetching,
  isError: countryError,
  error: countryErrorObj,
  getAllCountry,
} = useGetAllCountry();

const countryState = useDataState(
  countryStatus,
  countryList,
  countryFetching,
  countryError,
  countryErrorObj,
  'no-data'
);

const countryListOptions = computed(() => {
  return (
    countryList.value?.map((x) => ({ label: x.fullName, value: x.id })) ?? []
  );
});

// Retry functions
const retryPaymentTemplates = () => getAllPaymentTemplate();
const retryCountries = () => getAllCountry();

const PLAN_TYPE_TEXT = Object.fromEntries(
  Object.entries(PLAN_TYPE).map(([key, value]) => [value, key])
);

const handleClose = () => {
  // Emit the close event to the parent component
  emit('close');
};

const onSubmit = () => {
  createPayment(formModel);
  handleClose();
};
</script>
