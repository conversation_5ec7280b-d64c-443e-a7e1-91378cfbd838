<template>
  <div
    class="rounded-3xl border border-neutral-13 px-6 py-4 w-[363px] flex-shrink-0"
  >
    <div class="flex flex-col items-center">
      <span class="font-bold text-xl pt-6 pb-1">
        ${{ plan.initialBalance }}</span
      >
      <span class="text-primary-shade3 text-sm font-medium">
        {{ t('common.fee') }} ${{ plan.price }}
      </span>

      <button
        @click="handleGetPlanDetail(plan.id)"
        class="flex w-full justify-center rounded-lg bg-primary text-white px-4 py-3 text-sm font-bold mt-4"
      >
        {{ t('challenge.get_plan') }}
      </button>
    </div>

    <div class="flex flex-col gap-y-6 mt-6">
      <div
        v-for="detail in planDetails"
        :key="detail.label"
        class="flex justify-between items-center"
      >
        <div class="flex items-center text-sm text-neutral-20">
          <DaryaFillStreakOnIcon class="text-primary-tint7" />
          <span class="text-neutral-7 ps-1">{{ detail.label }}</span>
        </div>

        <div class="text-base font-medium text-right text-neutral-5">
          {{ detail.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  plan: Object,
});

const emit = defineEmits(['getPlanDetail']);

const { t } = useI18n();

const planDetails = computed(() => [
  { label: t('challenge.phase_one'), value: `${props.plan.phase_One_Profit}%` },
  { label: t('challenge.phase_two'), value: `${props.plan.phase_Two_Profit}%` },
  {
    label: t('challenge.maximum_overall_loss'),
    value: `${props.plan.maximumOverallLoss}%`,
  },
  {
    label: t('challenge.maximum_daily_loss'),
    value: `${props.plan.maximumDailyLoss}%`,
  },
  {
    label: t('challenge.news_trading'),
    value: props.plan.newsTrading ? '✔' : '',
  },
  {
    label: t('challenge.profit_split_up_to'),
    value: `${props.plan.profitSplitUpto}%`,
  },
  {
    label: t('challenge.minimum_trading_days'),
    value: `${props.plan.minimumTradingDays} ${t('common.days')}`,
  },
  {
    label: t('challenge.first_reward'),
    value: `${props.plan.firstReward} ${t('common.days')}`,
  },
]);

const handleGetPlanDetail = (id) => {
  emit('getPlanDetail', id);
};
</script>
