import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getPlansByPlanTypeId } from '../request';

export default function useGetPlanByPlanTypeId(
  id: MaybeRef<number>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const idRef = toRef(id);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['planListGroup', idRef.value]),
    queryFn: () => {
      const id = idRef.value;

      if (!id) throw new Error('ID is required');

      return getPlansByPlanTypeId(id);
    },
    enabled: shouldFetchRef,
  });

  const planListGroup = computed(() => data.value?.data?.data ?? []);

  return {
    getPlansByPlanTypeId: refetch,
    planListGroup,
    isError,
    status,
    error,
    isFetching,
  } as const;
}
