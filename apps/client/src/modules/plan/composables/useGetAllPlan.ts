import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllPlan } from '../request';

type planResponse = Awaited<ReturnType<typeof getAllPlan>>;

export default function useGetAllPlan() {
  const { status, isError, data, refetch } = useQuery<planResponse, Error>({
    queryKey: ['planList'],
    queryFn: getAllPlan,
  });

  const planList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllTransaction: refetch,
    planList,
    isError,
    status,
  } as const;
}
