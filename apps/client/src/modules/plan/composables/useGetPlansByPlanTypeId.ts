import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getPlanDetailById } from '../request';

export default function useGetPlanDetailById(
  id: MaybeRef<string>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const idRef = toRef(id);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching } = useQuery({
    queryKey: computed(() => ['planDetail', idRef.value]),
    queryFn: () => {
      const id = idRef.value;

      if (!id) throw new Error('ID is required');

      return getPlanDetailById(id);
    },
    enabled: shouldFetchRef,
  });

  const planDetailData = computed(() => data.value?.data?.data ?? []);

  return {
    getPlanDetailById: refetch,
    planDetailData,
    isError,
    status,
    isFetching,
  } as const;
}
