import http from '@/services/http';

import { PLAN_API } from '@/configs/env';

export function getAllPlan() {
  return http.get(`${PLAN_API}/Plan/GetAll`);
}

export function getPlansByPlanTypeId(planTypeId) {
  return http.get(`${PLAN_API}/Plan/GetPlansByPlanTypeId`, {
    params: { planType: planTypeId },
  });
}

export function getPlanDetailById(id) {
  return http.get(`${PLAN_API}/Plan/GetById`, {
    params: { id: id },
  });
}
