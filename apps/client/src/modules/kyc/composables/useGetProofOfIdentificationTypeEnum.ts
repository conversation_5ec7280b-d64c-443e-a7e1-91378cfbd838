import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import type { ApiResponse, EnumItem, SelectOption } from '../types';
import { getProofOfIdentityTypeEnum } from '../request';
import { QUERY_KEYS } from '../constants';
import { enumItemsToSelectOptions } from '../utils';

export default function useGetProofOfIdentificationTypeEnum() {
  const { status, isError, data, refetch, error, isFetching } = useQuery<
    ApiResponse<EnumItem[]>,
    Error
  >({
    queryKey: QUERY_KEYS.PROOF_OF_IDENTIFICATION_TYPE_ENUM,
    queryFn: getProofOfIdentityTypeEnum,
    staleTime: 1000 * 60 * 60 * 5,
    gcTime: 1000 * 60 * 60 * 5,
  });

  const proofOfIdentificationTypeEnum = computed(
    () => data.value?.data ?? []
  );

  const documentTypeOptions = computed<SelectOption[]>(() =>
    enumItemsToSelectOptions(proofOfIdentificationTypeEnum.value)
  );

  const isLoading = computed(
    () => status.value === 'pending' || isFetching.value
  );

  const getDocumentTypeName = computed(() => (typeId: number) => {
    const item = proofOfIdentificationTypeEnum.value.find(
      (item) => Number(item.value) === typeId
    );
    return item?.text || '';
  });

  return {
    proofOfIdentificationTypeEnum,
    documentTypeOptions,
    isLoading,
    isError,
    error,
    status,
    refetch,
    getDocumentTypeName,
    getProofOfIdentificationType: refetch,
  } as const;
}
