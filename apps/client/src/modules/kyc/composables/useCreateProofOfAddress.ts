import { unref, type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';
import type { ProofOfAddressCreationFormModel, ApiResponse } from '../types';
import { createProofOfAddress } from '../request';

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<
      ApiResponse,
      Error,
      MaybeRef<ProofOfAddressCreationFormModel>
    >,
    'mutationFn'
  > {}

export default function useCreateProofOfAddressMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<ProofOfAddressCreationFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        return await createProofOfAddress(unref(formModel));
      } catch (err: any) {
        const message =
          err.response?.data?.message ||
          err.message ||
          'An unexpected error occurred';
        throw new Error(message);
      }
    },
    ...options,
  });

  return {
    createProofOfAddress: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
