import { computed } from 'vue';

import {
  useGetVerification,
  useGetVerificationStatusTypeEnum,
  useGetKycStepTypeEnum,
} from '@/modules/kyc';

export default function useVerification() {
  const {
    verificationData,
    isError,
    status,
    isFetching,
    error,
    getUserVerificationById
  } = useGetVerification(true);

  const {
    verificationStatusTypeEnum,
    isError: statusEnumError,
    isFetching: statusEnumFetching,
    error: statusEnumErrorObj
  } = useGetVerificationStatusTypeEnum();

  const {
    kycStepTypeEnum,
    isError: stepEnumError,
    isFetching: stepEnumFetching,
    error: stepEnumErrorObj
  } = useGetKycStepTypeEnum();

  const mappedVerificationData = computed(() => {
    const statusEnumArray = Array.isArray(verificationStatusTypeEnum.value)
      ? verificationStatusTypeEnum.value
      : [];
    const stepTypeEnumArray = Array.isArray(kycStepTypeEnum.value)
      ? kycStepTypeEnum.value
      : [];

    return verificationData.value.map((item) => {
      const statusText =
        statusEnumArray.find((status) => status.value === String(item.status))
          ?.text || '';
      const stepTypeText =
        stepTypeEnumArray.find((step) => step.value === String(item.stepType))
          ?.text || '';
      return { ...item, statusText, stepTypeText };
    });
  });

  const identityVerification = computed(() =>
    mappedVerificationData.value.find((item) => item.stepType === 3)
  );

  const addressVerification = computed(() =>
    mappedVerificationData.value.find((item) => item.stepType === 2)
  );

  return {
    identityVerification,
    addressVerification,
    verificationData,
    mappedVerificationData,
    isError,
    status,
    isFetching,
    error,
    getUserVerificationById,
    // Enum loading states
    statusEnumError,
    statusEnumFetching,
    stepEnumError,
    stepEnumFetching,
  };
}
