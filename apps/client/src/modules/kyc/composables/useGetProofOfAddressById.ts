import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import type { ApiResponse, ProofOfAddressFormModel } from '../types';
import { getProofOfAddressById } from '../request';
import { QUERY_KEYS } from '../constants';

export default function useGeProofAddressById(
  actionId: MaybeRef<string> | undefined,
  shouldFetch: MaybeRef<boolean> = false
) {
  const actionIdRef = toRef(actionId);
  const shouldFetchRef = toRef(shouldFetch);

  const queryKey = computed(() =>
    actionIdRef.value
      ? QUERY_KEYS.PROOF_ADDRESS_DATA(actionIdRef.value)
      : ['proofAddressData', null]
  );

  const { data, refetch, isError, status, isFetching, error } = useQuery<
    ApiResponse<ProofOfAddressFormModel>,
    Error
  >({
    queryKey,
    queryFn: () => {
      const actionId = actionIdRef.value;

      if (!actionId) {
        throw new Error('Action ID is required');
      }

      return getProofOfAddressById(actionId);
    },
    enabled: computed(() => shouldFetchRef.value && !!actionIdRef.value),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const proofOfAddressData = computed(() => data.value?.data ?? null);

  const isLoading = computed(
    () => status.value === 'pending' || isFetching.value
  );

  const hasData = computed(() => !!proofOfAddressData.value);

  const isEnabled = computed(() => shouldFetchRef.value && !!actionIdRef.value);

  return {
    proofOfAddressData,
    isLoading,
    isError,
    hasData,
    isEnabled,
    error,
    status,
    isFetching,
    refetch,
    getProofOfAddressData: refetch,
  } as const;
}
