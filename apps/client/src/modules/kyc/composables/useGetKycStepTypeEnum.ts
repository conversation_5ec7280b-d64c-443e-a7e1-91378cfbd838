import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getKycStepTypeEnum } from '../request';

type KycStepTypeResponse = Awaited<ReturnType<typeof getKycStepTypeEnum>>;

export default function useGetKycStepTypeEnum() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    KycStepTypeResponse,
    Error
  >({
    queryKey: ['kycStepTypeEnum'],
    queryFn: getKycStepTypeEnum,
    staleTime: 1000 * 60 * 60 * 5,
    gcTime: 1000 * 60 * 60 * 5,
  });

  const kycStepTypeEnum = computed(() => data.value?.data?.data ?? []);

  return {
    getKycStepType: refetch,
    kycStepTypeEnum,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
