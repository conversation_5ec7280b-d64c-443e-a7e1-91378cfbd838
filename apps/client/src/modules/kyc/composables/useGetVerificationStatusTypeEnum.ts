import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getVerificationStatusTypeEnum } from '../request';

type VerificationStatusTypeResponse = Awaited<
  ReturnType<typeof getVerificationStatusTypeEnum>
>;

export default function useGetVerificationStatusTypeEnum() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    VerificationStatusTypeResponse,
    Error
  >({
    queryKey: ['verificationStatusTypeEnum'],
    queryFn: getVerificationStatusTypeEnum,
    staleTime: 1000 * 60 * 60 * 5,
    gcTime: 1000 * 60 * 60 * 5,
  });

  const verificationStatusTypeEnum = computed(() => data.value?.data ?? []);

  return {
    getVerificationStatusType: refetch,
    verificationStatusTypeEnum,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
