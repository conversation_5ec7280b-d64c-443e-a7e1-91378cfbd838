import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import type {
  ApiResponse,
  ProofOfIdentificationFormModel,
} from '../types';
import { getProofOfIdentityById } from '../request';
import { QUERY_KEYS } from '../constants';

export default function useGetProofOfIdentificationById(
  actionId: MaybeRef<string> | undefined,
  shouldFetch: MaybeRef<boolean> = false
) {
  const actionIdRef = toRef(actionId);
  const shouldFetchRef = toRef(shouldFetch);

  const queryKey = computed(() =>
    actionIdRef.value
      ? QUERY_KEYS.PROOF_IDENTIFICATION_DATA(actionIdRef.value)
      : ['proofIdentificationData', null]
  );

  const { data, refetch, isError, status, isFetching, error } = useQuery<
    ApiResponse<ProofOfIdentificationFormModel>,
    Error
  >({
    queryKey,
    queryFn: () => {
      const actionId = actionIdRef.value;

      if (!actionId) {
        throw new Error('Action ID is required');
      }

      return getProofOfIdentityById(actionId);
    },
    enabled: computed(() => shouldFetchRef.value && !!actionIdRef.value),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const proofOfIdentificationData = computed(() => data.value?.data ?? null);

  const isLoading = computed(
    () => status.value === 'pending' || isFetching.value
  );

  const hasData = computed(() => !!proofOfIdentificationData.value);

  const isEnabled = computed(() => shouldFetchRef.value && !!actionIdRef.value);

  return {
    proofOfIdentificationData,
    isLoading,
    isError,
    hasData,
    isEnabled,
    error,
    status,
    isFetching,
    refetch,
    getProofOfIdentificationData: refetch,
  } as const;
}
