import { unref, type MaybeRef, computed } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';
import type {
  ApiResponse,
  ProofOfIdentificationCreationFormModel,
} from '../types';
import { createProofOfIdentification } from '../request';
import {
  mapImagesWithFileNamesToApiFields,
  extractErrorMessage,
} from '../utils';
import { ERROR_MESSAGES } from '../constants';

export interface UseCreateProofOfIdentificationMutationOptions
  extends Omit<
    UseMutationOptions<
      ApiResponse<any>,
      Error,
      MaybeRef<ProofOfIdentificationCreationFormModel>
    >,
    'mutationFn'
  > {}

export default function useCreateProofOfIdentificationMutation(
  options?: UseCreateProofOfIdentificationMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data, reset } =
    useMutation<
      ApiResponse<any>,
      Error,
      MaybeRef<ProofOfIdentificationCreationFormModel>
    >({
      mutationFn: async (formModel) => {
        try {
          const formData = unref(formModel);

          // Validate required fields
          if (!formData.firstName || !formData.lastName) {
            throw new Error('First name and last name are required');
          }

          if (!formData.proofOfIdentificationTypeId) {
            throw new Error('Document type is required');
          }

          // Map images to correct API fields based on document type
          // const mappedData = mapImagesWithFileNamesToApiFields(formData);

          const response = await createProofOfIdentification(formData);

          // Check for API-level errors
          if (response.data.code === 0) {
            throw new Error(
              response.data.message || ERROR_MESSAGES.SERVER_ERROR
            );
          }

          return response.data;
        } catch (err) {
          const errorMessage = extractErrorMessage(err);

          throw new Error(errorMessage);
        }
      },
      ...options,
    });

  const isLoading = computed(() => status.value === 'pending');

  const isSuccess = computed(() => status.value === 'success');

  const errorMessage = computed(() => {
    return error.value ? extractErrorMessage(error.value) : null;
  });

  return {
    createProofOfIdentification: mutate,
    createProofOfIdentificationAsync: mutateAsync,
    reset,
    isLoading,
    isSuccess,
    isError,
    error,
    errorMessage,
    data,
    status,
    mutateAsync,
  } as const;
}
