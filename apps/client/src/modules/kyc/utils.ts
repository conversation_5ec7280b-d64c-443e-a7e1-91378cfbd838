import {
  ProofOfIdentificationFormModel,
  ProofOfIdentificationCreationFormModel,
  DocumentTypeConfig,
  EnumItem,
  SelectOption,
  VerificationStatusTypeId,
  ProofOfIdentificationTypeId,
  DocumentObject,
} from './types';
import {
  DOCUMENT_TYPE_CONFIGS,
  IMAGE_UPLOAD_CONFIG,
  UPLOAD_ERROR_MESSAGES,
  STATUS_COLOR_MAP,
} from './constants';

export interface UploadFileInfo {
  file: File;
  base64: string;
  preview: string;
  fileName: string;
  fileExtension: string;
}

export function isValidDocumentType(
  value: any
): value is ProofOfIdentificationTypeId {
  return Object.values(ProofOfIdentificationTypeId).includes(value);
}

export function getDocumentTypeConfig(
  typeId: ProofOfIdentificationTypeId
): DocumentTypeConfig {
  const config = DOCUMENT_TYPE_CONFIGS[typeId];
  if (!config) {
    throw new Error(`Invalid document type ID: ${typeId}`);
  }
  return config;
}

export function requiresBackImage(
  typeId: ProofOfIdentificationTypeId
): boolean {
  return getDocumentTypeConfig(typeId).requiresBackImage;
}

export function mapApiFieldsToImages(apiData: ProofOfIdentificationFormModel): {
  imageFront: string;
  imageBack: string;
} {
  const { proofOfIdentificationTypeId } = apiData;

  if (
    !proofOfIdentificationTypeId ||
    !isValidDocumentType(proofOfIdentificationTypeId)
  ) {
    return { imageFront: '', imageBack: '' };
  }

  const config = getDocumentTypeConfig(proofOfIdentificationTypeId);

  // Handle front image - could be DocumentObject or string
  let imageFront = '';
  const frontImageData = apiData[config.frontImageField];
  if (frontImageData) {
    if (isDocumentObject(frontImageData)) {
      imageFront = frontImageData.fileData;
    } else if (typeof frontImageData === 'string') {
      imageFront = frontImageData;
    }
  }

  // Handle back image - could be DocumentObject or string
  let imageBack = '';
  if (config.backImageField) {
    const backImageData = apiData[config.backImageField];
    if (backImageData) {
      if (isDocumentObject(backImageData)) {
        imageBack = backImageData.fileData;
      } else if (typeof backImageData === 'string') {
        imageBack = backImageData;
      }
    }
  }

  return { imageFront, imageBack };
}

export function validateUploadedFile(file: File): {
  isValid: boolean;
  error?: string;
} {
  if (!file) {
    return { isValid: false, error: UPLOAD_ERROR_MESSAGES.NO_FILE_SELECTED };
  }

  if (file.size > IMAGE_UPLOAD_CONFIG.maxSize) {
    return { isValid: false, error: UPLOAD_ERROR_MESSAGES.FILE_TOO_LARGE };
  }

  if (!IMAGE_UPLOAD_CONFIG.allowedTypes.includes(file.type)) {
    return { isValid: false, error: UPLOAD_ERROR_MESSAGES.INVALID_FILE_TYPE };
  }

  return { isValid: true };
}

export async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // Remove data URL prefix
      const base64 = result.replace(/^data:.*;base64,/, '');
      resolve(base64);
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

/**
 * Process uploaded file and return upload info
 */
export async function processUploadedFile(file: File): Promise<UploadFileInfo> {
  const validation = validateUploadedFile(file);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  const base64 = await fileToBase64(file);
  const preview = URL.createObjectURL(file);

  // Extract file extension from file name
  const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';

  return {
    file,
    base64,
    preview,
    fileName: file.name,
    fileExtension,
  };
}

/**
 * Map images with file names to API fields based on document type
 */
export function mapImagesWithFileNamesToApiFields(
  formData: ProofOfIdentificationFormModel
): ProofOfIdentificationCreationFormModel {
  const { proofOfIdentificationTypeId, imageFront, imageBack } = formData;

  if (
    !proofOfIdentificationTypeId ||
    !isValidDocumentType(proofOfIdentificationTypeId)
  ) {
    throw new Error('Invalid document type');
  }

  const config = getDocumentTypeConfig(proofOfIdentificationTypeId);
  const mappedData: ProofOfIdentificationCreationFormModel = {
    // Only include API-specific fields
    firstName: formData.firstName,
    lastName: formData.lastName,
    genderTypeId: formData.genderTypeId,
    dateOfBirth: formData.dateOfBirth,
    birthCountryId: formData.birthCountryId,
    proofOfIdentificationTypeId: formData.proofOfIdentificationTypeId,
    verificationStatusTypeId: formData.verificationStatusTypeId,
    // Clear all image fields first
    frontOfIdCard: undefined,
    frontOfIdCardFileName: undefined,
    backOfIdCard: undefined,
    backOfIdCardFileName: undefined,
    passport: undefined,
    passportFileName: undefined,
    frontOfDrivingLicense: undefined,
    frontOfDrivingLicenseFileName: undefined,
    backOfDrivingLicense: undefined,
    backOfDrivingLicenseFileName: undefined,
  };

  if (imageFront && config.frontImageField) {
    // Send just the fileData as string instead of DocumentObject
    mappedData[config.frontImageField] = imageFront as any;
    // Set file name field
    const fileNameField =
      `${config.frontImageField}FileName` as keyof ProofOfIdentificationFormModel;
    mappedData[fileNameField] = formData[fileNameField] as any;
  }

  // Map back image if required
  if (imageBack && config.backImageField && config.requiresBackImage) {
    // Send just the fileData as string instead of DocumentObject
    mappedData[config.backImageField] = imageBack as any;
    // Set file name field
    const fileNameField =
      `${config.backImageField}FileName` as keyof ProofOfIdentificationFormModel;
    mappedData[fileNameField] = formData[fileNameField] as any;
  }

  return mappedData;
}

export function enumItemsToSelectOptions(
  items: EnumItem[] | null | undefined
): SelectOption[] {
  if (!items || !Array.isArray(items)) {
    return [];
  }

  return items.map((item) => ({
    label: item.text,
    value: Number(item.value),
  }));
}

/**
 * Check if a document is in the new object format
 */
export function isDocumentObject(document: any): document is DocumentObject {
  return (
    document &&
    typeof document === 'object' &&
    typeof document.fileData === 'string' &&
    typeof document.fileName === 'string' &&
    typeof document.fileExtension === 'string'
  );
}

/**
 * Convert document object to base64 data URL for preview
 */
export function documentObjectToDataUrl(document: DocumentObject): string {
  if (!document.fileData) return '';

  // Determine MIME type from file extension
  const mimeType = getMimeTypeFromExtension(document.fileExtension);

  // Create data URL
  return `data:${mimeType};base64,${document.fileData}`;
}

/**
 * Get MIME type from file extension
 */
export function getMimeTypeFromExtension(extension: string): string {
  const ext = extension.toLowerCase().replace('.', '');

  const mimeTypes: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    pdf: 'application/pdf',
    gif: 'image/gif',
    webp: 'image/webp',
  };

  return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * Extract preview URL from document (handles both string and object formats)
 */
export function getDocumentPreviewUrl(
  document: DocumentObject | string | undefined
): string | null {
  if (!document) return null;

  if (typeof document === 'string') {
    // Legacy format - assume it's base64 image data
    if (document.startsWith('data:')) {
      return document;
    }
    // If it's just base64 without data URL prefix, assume it's an image
    return `data:image/jpeg;base64,${document}`;
  }

  if (isDocumentObject(document)) {
    return documentObjectToDataUrl(document);
  }

  return null;
}

/**
 * Create document object from file upload
 */
export function createDocumentObject(
  base64: string,
  fileName: string
): DocumentObject {
  // Extract file extension from filename
  const fileExtension = fileName.split('.').pop() || '';

  return {
    fileData: base64,
    fileName,
    fileExtension,
  };
}

export function findEnumItemByValue(
  items: EnumItem[] | null | undefined,
  value: string | number
): EnumItem | undefined {
  if (!items || !Array.isArray(items)) {
    return undefined;
  }
  return items.find((item) => item.value === String(value));
}

export function getStatusColor(status: VerificationStatusTypeId): string {
  return STATUS_COLOR_MAP[status] || 'neutral';
}

export function getStatusText(
  statusItems: EnumItem[] | null | undefined,
  statusId: VerificationStatusTypeId
): string {
  const item = findEnumItemByValue(statusItems, statusId);
  return item?.text || '';
}

export function extractErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'An unexpected error occurred';
}
