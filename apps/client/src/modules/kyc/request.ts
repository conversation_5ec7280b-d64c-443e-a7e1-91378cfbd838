import http from '@/services/http';

import type {
  ApiResponse,
  ProofOfIdentificationFormModel,
  ProofOfIdentificationCreationFormModel,
  ProofOfAddressFormModel,
  ProofOfAddressCreationFormModel,
} from './types';
import { KYC_API } from '@/configs/env';
import { KYC_ENDPOINTS } from './constants';

export function getVerifications() {
  return http.get(`${KYC_API}${KYC_ENDPOINTS.GET_VERIFICATIONS}`);
}

export function getProofOfAddressById(
  actionId: string
): Promise<ApiResponse<ProofOfAddressFormModel>> {
  if (!actionId) {
    throw new Error('Action ID is required');
  }

  return http
    .get(`${KYC_API}${KYC_ENDPOINTS.GET_PROOF_OF_ADDRESS}`, {
      params: { actionId },
    })
    .then((response) => response.data);
}

export function getProofOfIdentityById(
  actionId: string
): Promise<ApiResponse<ProofOfIdentificationFormModel>> {
  if (!actionId) {
    throw new Error('Action ID is required');
  }

  return http
    .get(`${KYC_API}${KYC_ENDPOINTS.GET_PROOF_OF_IDENTIFICATION}`, {
      params: { actionId },
    })
    .then((response) => response.data);
}

export function getProofOfIdentityTypeEnum() {
  return http
    .get(`${KYC_API}${KYC_ENDPOINTS.GET_PROOF_OF_IDENTIFICATION_TYPE_ENUMS}`)
    .then((response) => response.data);
}

export function getVerificationStatusTypeEnum() {
  return http
    .get(`${KYC_API}${KYC_ENDPOINTS.GET_VERIFICATION_STATUS_TYPE_ENUMS}`)
    .then((response) => response.data);
}

export function getKycStepTypeEnum() {
  return http
    .get(`${KYC_API}${KYC_ENDPOINTS.GET_KYC_STEP_TYPE_ENUMS}`)
    .then((response) => response.data);
}

export function createProofOfIdentification(
  data: ProofOfIdentificationCreationFormModel
) {
  if (!data) {
    throw new Error('Proof of identification data is required');
  }

  return http
    .post(`${KYC_API}${KYC_ENDPOINTS.CREATE_PROOF_OF_IDENTIFICATION}`, data)
    .then((response) => response.data);
}

export function createProofOfAddress(data: ProofOfAddressCreationFormModel) {
  if (!data) {
    throw new Error('Proof of address data is required');
  }

  return http
    .post(`${KYC_API}${KYC_ENDPOINTS.CREATE_PROOF_OF_ADDRESS}`, data)
    .then((response) => response.data);
}
