<template>
  <DDialog
    :open="isOpenSuccessSubmitDialog"
    @update:open="handleClose"
    :closeOnClickOutside="false"
    :showCloseIcon="false"
  >
    <template #body>
      <div class="flex flex-col items-center">
        <div class="p-1 rounded-lg bg-success-tint3">
          <DaryaBoldTickCircleIcon size="32" class="text-success-shade1" />
        </div>

        <span class="text-lg font-bold pt-5"> Personal Information </span>
        <span class="text-[10px] text-neutral-5 pt-1 w-64 text-center">
          Your Personal Information has been submitted. we will review them
          shortly and notify you once verification is complete
        </span>

        <DButton
          label="Ok"
          styleType="success"
          @click="handleClose"
          class="mt-4 w-24"
        />
      </div>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
defineProps<{
  isOpenSuccessSubmitDialog: boolean;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const handleClose = () => {
  emit('close');
};
</script>
