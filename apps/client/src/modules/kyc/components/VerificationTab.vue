<template>
  <div class="flex flex-col items-center mt-6">
    <img
      src="@/assets/images/ic-scanner-document.svg"
      alt="Scan Document"
      class="w-44 h-44 object-cover"
    />
    <span class="pt-5 text-lg font-bold">
      {{ t('kyc.complete_kyc_verification') }}</span
    >
    <span class="pt-2 text-sm text-neutral-6 font-medium">
      {{ t('kyc.to_make_withdrawals') }}
    </span>

    <div class="flex flex-col items-center">
      <!-- Identity Verification Card -->
      <!-- Verification Cards with State Management -->
      <DataStateWrapper
        :isLoading="verificationState.isLoading.value"
        :hasError="verificationState.hasError.value"
        :hasNoData="verificationState.hasNoData.value"
        :isRefetching="verificationState.isRefetching.value"
        :errorConfig="verificationState.errorConfig.value"
        :emptyConfig="verificationState.emptyConfig.value"
      >
        <router-link
          v-slot="{ navigate }"
          :to="{
            path: '/profile/personal-information',
            query: { actionId: identityVerification?.actionId },
          }"
          class="flex w-[492px] justify-between items-center px-6 py-8 mt-12 rounded-2xl border border-neutral-13 cursor-pointer hover:border-primary transition-colors"
        >
          <div class="flex flex-col items-start" @click="navigate">
            <div class="flex w-full">
              <span class="text-base font-bold text-neutral-7 pe-4">
                {{ t('kyc.proof_of_identification') }}
              </span>
              <DChip
                v-if="identityVerification?.status"
                :label="identityVerification?.statusText || ''"
                :color="getColor(identityVerification?.status)"
                variant="outline"
              />
            </div>

            <span class="text-sm text-neutral-7 pt-3">
              {{
                identityVerification?.message ||
                t('kyc.identity_verification_description')
              }}
            </span>
          </div>
          <DaryaArrowRightIcon class="text-primary" />
        </router-link>

        <!-- Address Verification Card -->
        <router-link
          v-slot="{ navigate }"
          :to="{
            path: '/profile/address-information',
            query: { actionId: addressVerification?.actionId },
          }"
          class="flex w-[492px] justify-between items-center px-6 py-8 mt-4 rounded-2xl border border-neutral-13 cursor-pointer hover:border-primary transition-colors"
        >
          <div class="flex flex-col items-start" @click="navigate">
            <div class="flex w-full">
              <span class="text-base font-bold text-neutral-7 pe-4">
                {{ t('kyc.proof_of_address') }}
              </span>
              <DChip
                v-if="addressVerification?.status"
                :label="addressVerification?.statusText || ''"
                :color="getColor(addressVerification?.status)"
                variant="outline"
              />
            </div>

            <span class="text-sm text-neutral-7 pt-3">
              {{
                addressVerification?.message ||
                t('kyc.address_verification_description')
              }}
            </span>
          </div>
          <DaryaArrowRightIcon class="text-primary" />
        </router-link>
      </DataStateWrapper>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

import { useVerification } from '@/modules/kyc';

const { t } = useI18n();

// Get verification data with state management
const {
  identityVerification,
  addressVerification,
  verificationData,
  status,
  isFetching,
  isError,
  error,
  getUserVerificationById,
} = useVerification();

// Create data state for verification data
const verificationState = useDataState(
  status,
  verificationData,
  isFetching,
  isError,
  error,
  'no-data'
);

// Retry function
const retryVerification = () => getUserVerificationById();

const STATUS_COLOR_MAP = {
  4: 'success',
  3: 'warning',
  2: 'primary',
  5: 'error',
  1: 'neutral',
};

const getColor = computed(() => (status: number) => {
  return STATUS_COLOR_MAP[status] || 'neutral';
});
</script>
