export enum ProofOfIdentificationTypeId {
  ID_CARD = 1,
  DRIVING_LICENSE = 2,
  PASSPORT = 3,
}

export enum VerificationStatusTypeId {
  PENDING = 1,
  IN_REVIEW = 2,
  UNDER_REVIEW = 3,
  APPROVED = 4,
  REJECTED = 5,
}

export enum GenderTypeId {
  MALE = 1,
  FEMALE = 2,
}

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface EnumItem {
  value: string;
  text: string;
}

export interface SelectOption {
  label: string;
  value: number;
}

export interface ProofOfIdentificationFormModel {
  firstName?: string;
  lastName?: string;
  genderTypeId?: GenderTypeId;
  dateOfBirth?: string;
  birthCountryId?: number | null;
  proofOfIdentificationTypeId?: ProofOfIdentificationTypeId | null;
  verificationStatusTypeId?: VerificationStatusTypeId;
  imageFront?: string;
  imageBack?: string;
  frontOfIdCard?: DocumentObject | string;
  frontOfIdCardFileName?: string;
  backOfIdCard?: DocumentObject | string;
  backOfIdCardFileName?: string;
  passport?: DocumentObject | string;
  passportFileName?: string;
  frontOfDrivingLicense?: DocumentObject | string;
  frontOfDrivingLicenseFileName?: string;
  backOfDrivingLicense?: DocumentObject | string;
  backOfDrivingLicenseFileName?: string;
  message?: string;
}
export interface ProofOfIdentificationCreationFormModel {
  firstName?: string;
  lastName?: string;
  genderTypeId?: GenderTypeId;
  dateOfBirth?: string;
  birthCountryId?: number | null;
  proofOfIdentificationTypeId?: ProofOfIdentificationTypeId | null;
  verificationStatusTypeId?: VerificationStatusTypeId;
  // API-specific image fields (send as strings, not DocumentObjects)
  frontOfIdCard?: string;
  frontOfIdCardFileName?: string;
  backOfIdCard?: string;
  backOfIdCardFileName?: string;
  passport?: string;
  passportFileName?: string;
  frontOfDrivingLicense?: string;
  frontOfDrivingLicenseFileName?: string;
  backOfDrivingLicense?: string;
  backOfDrivingLicenseFileName?: string;
  message?: string;
}
export interface DocumentObject {
  fileData: string;
  fileName: string;
  fileExtension: string;
}

export interface ProofOfAddressFormModel {
  city?: string;
  postalCode?: string;
  address?: string;
  residenceCountryId?: number | null;
  verificationStatusTypeId?: VerificationStatusTypeId;
  document?: DocumentObject | string;
  message?: string;
}
export interface ProofOfAddressCreationFormModel {
  city?: string;
  postalCode?: string;
  address?: string;
  residenceCountryId?: number | null;
  verificationStatusTypeId?: VerificationStatusTypeId;
  document?: string;
  documentFileName?: string;
}

export interface DocumentTypeConfig {
  id: ProofOfIdentificationTypeId;
  name: string;
  requiresBackImage: boolean;
  frontImageField: keyof ProofOfIdentificationFormModel;
  backImageField?: keyof ProofOfIdentificationFormModel;
}

export interface ImageUploadConfig {
  maxSize: number;
  allowedTypes: string[];
  quality?: number;
}

export type StatusColorMap = {
  [key in VerificationStatusTypeId]:
    | 'success'
    | 'warning'
    | 'primary'
    | 'error'
    | 'neutral';
};
