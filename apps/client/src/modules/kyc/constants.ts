import type {
  DocumentTypeConfig,
  ImageUploadConfig,
  StatusColorMap,
  SelectOption,
} from './types';
import {
  ProofOfIdentificationTypeId,
  VerificationStatusTypeId,
  GenderTypeId,
} from './types';

export const KYC_ENDPOINTS = {
  GET_VERIFICATIONS: '/GetVerifications',
  GET_PROOF_OF_ADDRESS: '/GetProofOfAddress',
  GET_PROOF_OF_IDENTIFICATION: '/GetProofOfIdentification',
  GET_PROOF_OF_IDENTIFICATION_TYPE_ENUMS: '/GetProofOfIdentificationTypeEnums',
  GET_VERIFICATION_STATUS_TYPE_ENUMS: '/GetVerificationStatusTypeEnums',
  GET_KYC_STEP_TYPE_ENUMS: '/GetKycStepTypeTypeEnums',
  CREATE_PROOF_OF_IDENTIFICATION: '/ProofOfIdentification',
  CREATE_PROOF_OF_ADDRESS: '/ProofOfAddress',
} as const;

export const QUERY_KEYS = {
  PROOF_OF_IDENTIFICATION_TYPE_ENUM: ['proofOfIdentificationTypeEnum'],
  VERIFICATION_STATUS_TYPE_ENUM: ['verificationStatusTypeEnum'],
  KYC_STEP_TYPE_ENUM: ['kycStepTypeEnum'],
  VERIFICATIONS: ['verifications'],
  PROOF_IDENTIFICATION_DATA: (actionId: string) => [
    'proofIdentificationData',
    actionId,
  ],
  PROOF_ADDRESS_DATA: (actionId: string) => ['proofOfAddressData', actionId],
} as const;

export const DOCUMENT_TYPE_CONFIGS: Record<
  ProofOfIdentificationTypeId,
  DocumentTypeConfig
> = {
  [ProofOfIdentificationTypeId.ID_CARD]: {
    id: ProofOfIdentificationTypeId.ID_CARD,
    name: 'ID Card',
    requiresBackImage: true,
    frontImageField: 'frontOfIdCard',
    backImageField: 'backOfIdCard',
  },
  [ProofOfIdentificationTypeId.DRIVING_LICENSE]: {
    id: ProofOfIdentificationTypeId.DRIVING_LICENSE,
    name: 'Driving License',
    requiresBackImage: true,
    frontImageField: 'frontOfDrivingLicense',
    backImageField: 'backOfDrivingLicense',
  },
  [ProofOfIdentificationTypeId.PASSPORT]: {
    id: ProofOfIdentificationTypeId.PASSPORT,
    name: 'Passport',
    requiresBackImage: false,
    frontImageField: 'passport',
  },
};

export const IMAGE_UPLOAD_CONFIG: ImageUploadConfig = {
  maxSize: 5242880, // 5 MB
  allowedTypes: ['image/png', 'image/jpeg', 'application/pdf'],
  quality: 0.8,
};

export const UPLOAD_ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 5MB',
  INVALID_FILE_TYPE:
    'Invalid file type. Please upload PNG, JPEG, or PDF files only',
  UPLOAD_FAILED: 'Failed to upload file. Please try again',
  NO_FILE_SELECTED: 'Please select a file to upload',
} as const;

export const STATUS_COLOR_MAP: StatusColorMap = {
  [VerificationStatusTypeId.PENDING]: 'neutral',
  [VerificationStatusTypeId.IN_REVIEW]: 'primary',
  [VerificationStatusTypeId.UNDER_REVIEW]: 'warning',
  [VerificationStatusTypeId.APPROVED]: 'success',
  [VerificationStatusTypeId.REJECTED]: 'error',
};

export const GENDER_OPTIONS: SelectOption[] = [
  { label: 'Male', value: GenderTypeId.MALE },
  { label: 'Female', value: GenderTypeId.FEMALE },
];

export const ERROR_MESSAGES = {
  GENERIC: 'An unexpected error occurred',
  NETWORK: 'Network error. Please check your connection',
  VALIDATION: 'Please check your input and try again',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  NOT_FOUND: 'The requested resource was not found',
  SERVER_ERROR: 'Server error. Please try again later',
} as const;
