import { computed, ComputedRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { ExchangeRatesResponse, ExchangeData } from '@/modules/calculator';
import { getAllExchangeData } from '../request';

export default function useGetAllExchangeData() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    ExchangeRatesResponse,
    Error
  >({
    queryKey: ['exchangeDataList'],
    queryFn: getAllExchangeData,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000
  });

  const exchangeDataList: ComputedRef<Record<string, ExchangeData>> = computed(() => data.value?.data ?? {});

  return {
    getAllExchangeData: refetch,
    exchangeDataList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
