import { ExchangeRates } from '@/modules/calculator';

export default function useForexUtils(){
  const LOT_SIZES: Record<string, number> = {
    'XAU': 100,
    'XAG': 5000,
    'XTI': 1000,
    'XBR': 1000,

    'BTC': 1,
    'ETH': 1,
    'LTC': 1,
    'XRP': 1,
    'BCH': 1,
    'USDT': 100000,

    DEFAULT: 100000
  };

  const PIP_SIZES: Record<string, number> = {
    'JPY': 0.01,

    'XAU': 0.01,
    'XAG': 0.001,
    'XTI': 0.01,
    'XBR': 0.01,

    'BTC': 0.01,
    'ETH': 0.01,
    'LTC': 0.01,
    'XRP': 0.0001,
    'BCH': 0.0001,
    'USDT': 0.0001,

    'DEFAULT': 0.0001,
  };

  const formatNumber = (number: number, decimals: number = 2): string => {
    return Number(number).toLocaleString('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };

  const isNumeric = (value: string | number): boolean => {
    return !isNaN(parseFloat(value.toString())) && isFinite(Number(value));
  };

  const getStandardLotSize = (instrument: string): number => {
    const baseSymbol = instrument.trim().toUpperCase().substring(0, instrument.length - 3);
    return LOT_SIZES[baseSymbol] ?? LOT_SIZES.DEFAULT;
  };

  const getPipSize = (instrument: string): number => {
    const quoteSymbol = instrument.trim().toUpperCase().substring(instrument.length - 3);
    return PIP_SIZES[quoteSymbol] ?? PIP_SIZES.DEFAULT;
  };

  const getBaseQuoteCurrency = (instrument: string): Record<string, string> => {
    const quoteCurrency = instrument.trim().toUpperCase().substring(instrument.length - 3);
    const baseCurrency = instrument.trim().toUpperCase().substring(0, instrument.length - 3);

    return {
      baseCurrency,
      quoteCurrency,
    }
  }

  const calculateCurrencyConvert = (
    { baseCurrency,
      quoteCurrency
    }: {baseCurrency: string, quoteCurrency: string},
    exchangeRates: ExchangeRates
  ): number => {
    let rate: number;
    if (baseCurrency === 'USD') {
      rate = parseFloat(exchangeRates[quoteCurrency]?.rate || '1')
    } else if (quoteCurrency === 'USD') {
      rate = 1 / parseFloat(exchangeRates[baseCurrency]?.rate || '1')
    } else {
      const fromToUsd = 1 / parseFloat(exchangeRates[baseCurrency]?.rate || '1')
      const usdToTarget = parseFloat(exchangeRates[quoteCurrency]?.rate || '1')
      rate = fromToUsd * usdToTarget
    }

    return rate
  }

  const calculatePipValue = (
    { lotSize, currencyPair, accountCurrency }: { lotSize: number; currencyPair: string; accountCurrency: string },
    exchangeRates: ExchangeRates
  ): number => {
    currencyPair = currencyPair.trim().toUpperCase();
    const {baseCurrency, quoteCurrency} = getBaseQuoteCurrency(currencyPair);

    const standardLot = getStandardLotSize(currencyPair)
    const pipSize = PIP_SIZES[baseCurrency] || PIP_SIZES.DEFAULT
    const pipValueInQuoteCurrency = standardLot * pipSize * lotSize

    if (quoteCurrency === accountCurrency) {
      return pipValueInQuoteCurrency
    }

    const quoteToUsdRate = parseFloat(exchangeRates[quoteCurrency]?.rate || '1')
    const accountToUsdRate = parseFloat(exchangeRates[accountCurrency]?.rate || '1')

    const pipValueInUsd = pipValueInQuoteCurrency / quoteToUsdRate
    return accountCurrency === 'USD'
      ? pipValueInUsd
      : pipValueInUsd * accountToUsdRate
  }

  const calculatePositionValue = (
    instrument: string,
    accountCurrency: string,
    positionSize: number,
    rates: ExchangeRates,
  ): number => {
    const { baseCurrency, quoteCurrency } = getBaseQuoteCurrency(instrument);

    if (baseCurrency === accountCurrency) {
      return positionSize;
    } else if (quoteCurrency === accountCurrency) {
      return (
        positionSize *
        calculateCurrencyConvert({ baseCurrency, quoteCurrency }, rates)
      );
    } else {
      return (
        positionSize *
        calculateCurrencyConvert(
          { baseCurrency, quoteCurrency: accountCurrency },
          rates
        )
      );
    }
  }

  const getCurrencySymbol = (currency: string): string => {
    const symbols: Record<string, string> = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'AUD': 'A$',
      'CAD': 'C$',
      'CHF': 'Fr',
      'CNY': '¥',
      'INR': '₹',
      'BRL': 'R$',
      'RUB': '₽',
      'KRW': '₩',
      'NZD': 'NZ$',
      'MXN': 'Mex$',
      'TRY': '₺'
    };
    return symbols[currency] || currency + ' ';
  };

  const lotOptions = [
    { value: '0.01', label: 'Micro' },
    { value: '0.1', label: 'Mini' },
    { value: '1', label: 'Standard' },
    { value: 'custom', label: 'Custom' }
  ];

  return {
    formatNumber,
    isNumeric,
    getStandardLotSize,
    calculatePipValue,
    getCurrencySymbol,
    calculateCurrencyConvert,
    calculatePositionValue,
    getPipSize,
    getBaseQuoteCurrency,
    lotOptions
  }
}
