import { computed, ComputedRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { ExchangePairs, exchangePairsResponse } from '@/modules/calculator';
import { getAllExchangePairs } from '../request';

export default function useGetAllExchangePairs() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    exchangePairsResponse,
    Error
  >({
    queryKey: ['exchangePairsList'],
    queryFn: getAllExchangePairs,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000
  });

  const exchangePairsList: ComputedRef<ExchangePairs> = computed(() => data.value?.data ?? []);

  return {
    getAllExchangePairs: refetch,
    exchangePairsList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
