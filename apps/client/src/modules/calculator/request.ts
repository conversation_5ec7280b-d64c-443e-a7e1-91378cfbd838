import http from '../../services/http/index';
import {
  ExchangeDataApiResponse,
  ExchangePairsApiResponse,
} from '@/modules/calculator';

const EXCHANGE_API = 'https://agent.invest-hub.com/webhook/v1/finance';

export function getAllExchangeData(): Promise<ExchangeDataApiResponse> {
  return http
    .get(`${EXCHANGE_API}/exchange`)
    .then((response) => response.data);
};

export function getAllExchangePairs(): Promise<ExchangePairsApiResponse> {
  return http
    .get(`${EXCHANGE_API}/pairs`)
    .then((response) => response.data);
};
