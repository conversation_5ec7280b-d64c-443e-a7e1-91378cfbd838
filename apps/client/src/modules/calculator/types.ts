import { getAllExchangeData, getAllExchangePairs } from '@/modules/calculator/request.ts';

export interface ExchangeData {
  rate: string;
  name: string;
  code: string;
}

export interface ExchangeRates {
  [key: string]: ExchangeData;
}

export type ExchangePairs = string[];

export interface BaseApiResponse {
  success: boolean;
  metadata: {
    timestamp: string;
  };
}

export interface ExchangeDataApiResponse extends BaseApiResponse {
  data: Record<string, ExchangeData>;
  metadata: BaseApiResponse['metadata'] & {
    total_currencies: number;
  };
}

export interface ExchangePairsApiResponse extends BaseApiResponse {
  data: ExchangePairs;
  metadata: BaseApiResponse['metadata'] & {
    count: number;
  };
}

export type ExchangeRatesResponse = Awaited<
  ReturnType<typeof getAllExchangeData>
>;

export type exchangePairsResponse = Awaited<
  ReturnType<typeof getAllExchangePairs>
>;
