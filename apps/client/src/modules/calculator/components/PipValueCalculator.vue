<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">
        {{ t('calculator.pipValue.title') }}
      </h2>
      <p class="text-sm text-neutral-6">
        {{ t('calculator.pipValue.description') }}
      </p>
    </div>

    <VeeForm class="space-y-6">
      <div class="flex gap-4">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="currencyPair"
          :rule="validateCurrencyPair"
          class="flex-1"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.currencyPair"
            :options="currencyPairsOptions"
            :label="t('calculator.pipValue.currencyPair')"
            :placeholder="t('calculator.pipValue.currencyPair')"
            required
            :error="errorMessage"
          />
        </VeeField>

        <!-- Account Currency Selection -->
        <VeeField
          v-slot="{ field, errorMessage }"
          name="accountCurrency"
          v-model="formData.accountCurrency"
          :rules="validateAccountCurrency"
          class="flex-1"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.accountCurrency"
            :options="currencyExchangeObject"
            :label="t('calculator.pipValue.accountCurrency')"
            :placeholder="t('calculator.pipValue.accountCurrency')"
            required
            :error="errorMessage"
          />
        </VeeField>
      </div>

      <div class="space-y-4">
        <label class="block text-sm font-medium text-neutral-2">
          {{ t('calculator.pipValue.lotSize') }}
        </label>

        <div class="flex space-y-3 gap-4 items-stretch flex-wrap">
          <div
            class="flex bg-lightMood-3 rounded-lg gap-1 p-1 flex-1 mb-0 self-center"
          >
            <DButton
              v-for="lotOption in lotOptions"
              :key="lotOption.value"
              :label="lotOption.label"
              class="lot-tab flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
              :styleType="
                selectedLotTab === lotOption.value ? 'primary' : 'secondary'
              "
              @click="setLotSize(lotOption.value)"
            />
          </div>

          <VeeField
            v-slot="{ field, errorMessage }"
            name="lotSize"
            v-model="formData.lotSize"
            :rules="validateLotSize"
          >
            <DInput
              v-bind="field"
              type="number"
              step="0.01"
              min="0.01"
              :error="errorMessage"
              class="w-full flex-1 self-center"
              :disabled="selectedLotTab !== 'custom'"
            />
          </VeeField>
        </div>
      </div>

      <div v-if="showResult" class="space-y-4">
        <DBox class="p-6 rounded-lg">
          <div class="text-sm font-medium text-neutral-6 mb-2">
            {{ t('calculator.pipValue.pipValue') }}:
          </div>
          <div class="text-3xl font-bold text-success mb-4">
            {{ formResults.pipValue }}
          </div>
          <DBaseTable :columns="tableHeaders" :data="tableRows" />
        </DBox>
      </div>
    </VeeForm>
  </DBox>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >;
  currencyPairs: string[];
}>();

const { t } = useI18n();

const {
  formatNumber,
  calculatePipValue,
  getCurrencySymbol,
  lotOptions
} = useForexUtils();

interface FormData {
  currencyPair: string;
  accountCurrency: string;
  lotSize: number;
}

const formData: FormData = reactive({
  currencyPair: 'EURUSD',
  accountCurrency: 'USD',
  lotSize: 0.01,
});

const isCalculating = ref(false);
const showResult = ref(false);
const selectedLotTab = ref('0.01');

interface Results {
  pipValue: string;
  pipMovements: Array<{ pips: number; value: string }>;
}

const formResults: Results = reactive({
  pipValue: '-',
  pipMovements: [],
});

const validateCurrencyPair = (value: string) => {
  if (!value) {
    return t('calculator.validation.validCurrency');
  }
  return true;
};

const validateAccountCurrency = (value: string) => {
  if (!value) {
    return t('calculator.validation.validAccountCurrency');
  }
  return true;
};

const validateLotSize = (value: string) => {
  const numValue = Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) {
    return t('calculator.validation.validLotSize');
  }
  return true;
};

const currencyPairsOptions = computed(() => {
  return props.currencyPairs.map((item) => ({
    label: item,
    value: item,
  }));
});

const currencyExchangeObject = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key,
    rate: value.rate,
    name: value.name,
    code: value.code,
  }));
});

const isFormValid = computed(() => {
  return (
    formData.currencyPair && formData.accountCurrency && formData.lotSize > 0
  );
});

const tableHeaders = computed(() => [
  { label: t('calculator.pipValue.pipMovement'), key: 'pips' },
  { label: t('calculator.pipValue.value'), key: 'value' },
]);

const tableRows = computed(() =>
  formResults.pipMovements.map((movement) => ({
    pips: movement.pips,
    value: movement.value,
  }))
);

// Methods
const setLotSize = (value: string) => {
  selectedLotTab.value = value;
  if (value !== 'custom') {
    formData.lotSize = parseFloat(value);
    if (isFormValid.value) {
      pipValueCalculator();
    }
  }
};

const pipValueCalculator = () => {
  isCalculating.value = true;

  try {
    const pipValue = calculatePipValue(formData, props.exchangeData);
    const currencySymbol = getCurrencySymbol(formData.accountCurrency);
    formResults.pipValue = `${currencySymbol}${formatNumber(pipValue, 2)}`;

    const movements = [1, 5, 10, 25, 50, 100];
    formResults.pipMovements = movements.map((pips) => ({
      pips,
      value: `${currencySymbol}${formatNumber(pipValue * pips, 2)}`,
    }));
    showResult.value = true;
  } catch (error) {
    console.error(error);
  } finally {
    isCalculating.value = false;
  }
};

watch(
  [
    () => formData.currencyPair,
    () => formData.accountCurrency,
    () => formData.lotSize,
  ],
  () => {
    if (isFormValid.value) {
      pipValueCalculator();
    }
  }
);

watch(
  () => props.exchangeData,
  (newRates) => {
    if (newRates && Object.keys(newRates).length > 0 && isFormValid.value) {
      pipValueCalculator();
    }
  },
  { immediate: true, once: true }
);
</script>
