<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">
        {{ t('calculator.margin.title') }}
      </h2>
      <p class="text-sm text-neutral-6">
        {{ t('calculator.margin.description') }}
      </p>
    </div>

    <VeeForm class="space-y-6">
      <!-- Currency Pair Selection -->
      <div class="flex gap-2">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="currencyPair"
          v-model="formData.currencyPair"
          :rules="validateCurrencyPair"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.currencyPair"
            :options="currencyPairOptions"
            :label="t('calculator.margin.currencyPair')"
            :placeholder="t('calculator.margin.selectCurrencyPair')"
            required
            :error="errorMessage"
          />
        </VeeField>

        <VeeField
          v-slot="{ field, errorMessage }"
          name="accountCurrency"
          v-model="formData.accountCurrency"
          :rules="validateAccountCurrency"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.accountCurrency"
            :options="currencyExchangeObject"
            :label="t('calculator.margin.accountCurrency')"
            :placeholder="t('calculator.margin.selectAccountCurrency')"
            required
            :error="errorMessage"
          />
        </VeeField>
      </div>

      <div>
        <label class="block text-sm font-medium text-neutral-2 mb-2">
          {{ t('calculator.margin.lotSize') }}
        </label>
        <div class="flex gap-2 flex-wrap">
          <div
            class="flex bg-lightMood-3 rounded-lg gap-1 p-1 flex-1 mb-0 self-center"
          >
            <DButton
              v-for="lotOption in lotOptions"
              :key="lotOption.value"
              :label="lotOption.label"
              class="lot-tab flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
              :styleType="
                selectedLotTab === lotOption.value ? 'primary' : 'secondary'
              "
              @click="setLotSize(lotOption.value)"
            />
          </div>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="lotSize"
            v-model="formData.lotSize"
            :rules="validateLotSize"
          >
            <DInput
              v-bind="field"
              type="number"
              step="0.01"
              min="0.01"
              :error="errorMessage"
              required
              class="w-full flex-1"
              :disabled="selectedLotTab !== 'custom'"
            />
          </VeeField>
        </div>
      </div>

      <div>
        <label class="block text-sm font-medium text-neutral-2 mb-2">
          {{ t('calculator.margin.leverage') }}
        </label>
        <div class="flex gap-2 flex-wrap">
          <div
            class="flex bg-lightMood-3 rounded-lg gap-1 p-1 flex-1 mb-0 self-center"
          >
            <DButton
              v-for="leverageOption in leverageOptions"
              :key="leverageOption.value"
              :label="leverageOption.label"
              class="lot-tab flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
              :styleType="
                selectedLeverageTab === leverageOption.value
                  ? 'primary'
                  : 'secondary'
              "
              @click="setLeverage(leverageOption.value)"
            />
          </div>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="leverage"
            v-model="formData.leverage"
            :rules="validateLeverage"
          >
            <DInput
              v-bind="field"
              type="number"
              step="1"
              min="1"
              max="500"
              @input="onLeverageChange"
              :error="errorMessage"
              class="w-full flex-1"
              :disabled="selectedLeverageTab !== 'custom'"
            />
          </VeeField>
        </div>
      </div>

      <!-- Results -->
      <div v-if="showResult">
        <DBox class="p-6 rounded-lg space-y-2">
          <div class="border-b border-gray-200">
            <div class="text-sm font-medium text-neutral-6 mb-2">
              {{ t('calculator.margin.result') }}:
            </div>
            <div class="text-3xl font-bold text-success mb-2">
              {{ formResults.marginRequired }}
            </div>
          </div>
          <div>
            <span class="text-sm text-neutral-6 inline-block">
              {{ t('calculator.margin.positionSize') }}
            </span>
            <span
              class="text-lg font-semibold text-neutral-2 inline-block float-right"
            >
              {{ formResults.positionSize }}
            </span>
          </div>
          <div>
            <span class="text-sm text-neutral-6 inline-block">
              {{ t('calculator.margin.positionValue') }}
            </span>
            <span
              class="text-lg font-semibold text-neutral-2 inline-block float-right"
            >
              {{ formResults.positionValue }}
            </span>
          </div>
          <div>
            <span class="text-sm text-neutral-6 inline-block">
              {{ t('calculator.margin.leverageUsed') }}
            </span>
            <span
              class="text-lg font-semibold text-neutral-2 inline-block float-right"
            >
              {{ formData.leverage }}:1
            </span>
          </div>
        </DBox>
      </div>
    </VeeForm>
  </DBox>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref, type ComputedRef } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >;
  currencyPairs: string[];
}>();

const { t } = useI18n();
const {
  formatNumber,
  getCurrencySymbol,
  getStandardLotSize,
  getBaseQuoteCurrency,
  calculatePositionValue,
  lotOptions,
} = useForexUtils();

interface ExchangeRates {
  [p: string]: {
    rate: string;
    name: string;
    code: string;
  };
}

// Form data
interface FormData {
  currencyPair: string;
  accountCurrency: string;
  lotSize: number;
  leverage: number;
}

const formData: FormData = reactive({
  currencyPair: 'EURUSD',
  accountCurrency: 'USD',
  lotSize: 0.01,
  leverage: 100,
});

// UI state
const isCalculating = ref(false);
const showResult = ref(false);
const selectedLeverageTab = ref('100');
const selectedLotTab = ref('0.01');

interface Results {
  marginRequired: string;
  positionSize: string;
  positionValue: string;
}

const formResults: Results = reactive({
  marginRequired: '-',
  positionSize: '-',
  positionValue: '-',
});

const leverageOptions = [
  { value: '50', label: '50:1' },
  { value: '100', label: '100:1' },
  { value: '200', label: '200:1' },
  { value: '400', label: '400:1' },
  { value: 'custom', label: t('common.custom') },
];

// Validation rules
const validateCurrencyPair = (value: unknown) => {
  if (!value || typeof value !== 'string') {
    return t('calculator.validation.validCurrency');
  }
  return true;
};

const validateAccountCurrency = (value: unknown) => {
  if (!value || typeof value !== 'string') {
    return t('calculator.validation.validAccountCurrency');
  }
  return true;
};

const validateLotSize = (value: unknown) => {
  const numValue = Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) {
    return t('calculator.validation.validLotSize');
  }
  return true;
};

const validateLeverage = (value: unknown) => {
  const numValue = Number(value);
  if (!value || isNaN(numValue) || numValue < 1 || numValue > 2000) {
    return t('calculator.validation.validLeverage');
  }
  return true;
};

const currencyPairOptions = computed(() => {
  return props.currencyPairs.map((symbol) => ({
    label: symbol,
    value: symbol,
  }));
});

const currencyExchangeObject = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key,
    rate: value.rate,
    name: value.name,
    code: value.code,
  }));
});

const isFormValid = computed(() => {
  return (
    formData.currencyPair &&
    formData.accountCurrency &&
    formData.lotSize > 0 &&
    formData.leverage >= 1 &&
    formData.leverage <= 500
  );
});

const setLeverage = (value: string) => {
  selectedLeverageTab.value = value;
  if (value !== 'custom') {
    formData.leverage = parseFloat(value);
    if (isFormValid.value) {
      calculateMargin();
    }
  }
};

const setLotSize = (value: string) => {
  selectedLotTab.value = value;
  if (value !== 'custom') {
    formData.lotSize = parseFloat(value);
    if (isFormValid.value) {
      calculateMargin();
    }
  }
};

const onLeverageChange = () => {
  const leverageValues = ['50', '100', '200', '400'];
  const foundTab = leverageValues.find(
    (tab) => parseFloat(tab) === formData.leverage
  );
  selectedLeverageTab.value = foundTab || 'custom';

  if (isFormValid.value) {
    calculateMargin();
  }
};

const handleCalculate = () => {
  if (isFormValid.value) {
    calculateMargin();
  }
};

const calculateMargin = () => {
  isCalculating.value = true;

  try {
    const marginData = calculateMarginLogic(formData, props.exchangeData);

    const currencySymbol = getCurrencySymbol(formData.accountCurrency);
    formResults.marginRequired = `${currencySymbol}${formatNumber(marginData.marginRequired, 2)}`;
    formResults.positionSize = `${formatNumber(marginData.positionSize, 2)} ${marginData.baseCurrency}`;
    formResults.positionValue = `${currencySymbol}${formatNumber(marginData.positionValue, 2)}`;

    showResult.value = true;
  } catch (error) {
    console.error('Error calculating margin:', error);
  } finally {
    isCalculating.value = false;
  }
};

const calculateMarginLogic = (
  { currencyPair, accountCurrency, lotSize, leverage }: FormData,
  rates: ExchangeRates
) => {
  const { baseCurrency } = getBaseQuoteCurrency(currencyPair);

  const positionSize = lotSize * getStandardLotSize(currencyPair);

  const positionValue = calculatePositionValue(
    currencyPair,
    accountCurrency,
    positionSize,
    rates
  );
  const marginRequired = positionValue / leverage;

  return {
    positionSize,
    positionValue,
    marginRequired,
    baseCurrency,
  };
};

watch(
  [
    () => formData.currencyPair,
    () => formData.accountCurrency,
    () => formData.lotSize,
    () => formData.leverage,
  ],
  () => {
    if (isFormValid.value) {
      calculateMargin();
    }
  }
);

watch(
  () => props.exchangeData,
  (newRates) => {
    if (newRates && Object.keys(newRates).length > 0 && isFormValid.value) {
      calculateMargin();
    }
  },
  { immediate: true }
);
</script>
