<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">
        {{ t('calculator.positionSize.title') }}
      </h2>
      <p class="text-sm text-neutral-6">
        {{ t('calculator.positionSize.description') }}
      </p>
    </div>

    <VeeForm class="space-y-6">
      <div class="flex gap-2">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="accountBalance"
          v-model="formData.accountBalance"
          :rules="validateAccountBalance"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.01"
            min="0.01"
            :label="t('calculator.positionSize.accountBalance')"
            :placeholder="t('calculator.positionSize.accountBalance')"
            required
            :error="errorMessage"
            class="flex-1"
          />
        </VeeField>

        <VeeField
          v-slot="{ field, errorMessage }"
          name="accountCurrency"
          v-model="formData.accountCurrency"
          :rules="validateAccountCurrency"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.accountCurrency"
            :options="currencyExchangeObject"
            :label="t('calculator.positionSize.accountCurrency')"
            :placeholder="t('calculator.positionSize.accountCurrency')"
            required
            :error="errorMessage"
            class="flex-1"
          />
        </VeeField>
      </div>

      <!-- Risk Percentage and Stop Loss -->
      <div class="flex gap-2">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="riskPercentage"
          v-model="formData.riskPercentage"
          :rules="validateRiskPercentage"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.01"
            min="0.01"
            max="100"
            :label="t('calculator.positionSize.riskPercentage')"
            :placeholder="t('calculator.positionSize.riskPercentage')"
            required
            :error="errorMessage"
            class="w-full flex-1"
          />
        </VeeField>

        <VeeField
          v-slot="{ field, errorMessage }"
          name="stopLossPips"
          v-model="formData.stopLossPips"
          :rules="validateStopLossPips"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.1"
            min="0.1"
            :label="t('calculator.positionSize.stopLossPips')"
            :placeholder="t('calculator.positionSize.stopLossPips')"
            required
            :error="errorMessage"
            class="w-full flex-1"
          />
        </VeeField>
      </div>

      <!-- Currency Pair and Entry Price -->
      <div class="flex gap-2">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="currencyPair"
          v-model="formData.currencyPair"
          :rules="validateCurrencyPair"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.currencyPair"
            :options="currencyPairOptions"
            :label="t('calculator.positionSize.currencyPair')"
            :placeholder="t('calculator.positionSize.currencyPair')"
            required
            :error="errorMessage"
            class="flex-1"
          />
        </VeeField>

        <VeeField
          v-slot="{ field, errorMessage }"
          name="entryPrice"
          v-model="formData.entryPrice"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.00001"
            min="0"
            :label="t('calculator.positionSize.entryPrice')"
            :placeholder="t('calculator.positionSize.entryPrice')"
            :error="errorMessage"
            class="flex-1"
          />
        </VeeField>
      </div>

      <div v-if="showResult">
        <DBox class="p-6 bg-lightMood-2 rounded-lg">
          <div class="text-sm font-medium text-neutral-6 mb-2">
            {{ t('calculator.positionSize.amountAtRisk') }}:
          </div>
          <div class="text-2xl font-bold text-danger mb-4">
            {{ formResults.amountAtRisk }}
          </div>

          <div class="text-sm font-medium text-neutral-6 mb-2">
            {{ t('calculator.positionSize.recommendedPosition') }}:
          </div>
          <div class="text-3xl font-bold text-success mb-4">
            {{ formResults.positionSize }}
          </div>

          <ul>
            <li
              class="info-item flex justify-between items-center p-3 bg-white rounded-lg"
            >
              <span class="info-label text-sm text-neutral-6"
                >{{ t('calculator.positionSize.standardLots') }}:</span
              >
              <span class="info-value text-sm font-semibold text-neutral-4">{{
                formResults.standardLots
              }}</span>
            </li>
            <li
              class="info-item flex justify-between items-center p-3 bg-white rounded-lg"
            >
              <span class="info-label text-sm text-neutral-6"
                >{{ t('calculator.positionSize.miniLots') }}:</span
              >
              <span class="info-value text-sm font-semibold text-neutral-4">{{
                formResults.miniLots
              }}</span>
            </li>
            <li
              class="info-item flex justify-between items-center p-3 bg-white rounded-lg"
            >
              <span class="info-label text-sm text-neutral-6"
                >{{ t('calculator.positionSize.microLots') }}:</span
              >
              <span class="info-value text-sm font-semibold text-neutral-4">{{
                formResults.microLots
              }}</span>
            </li>
          </ul>
        </DBox>
      </div>
    </VeeForm>
  </DBox>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >;
  currencyPairs: string[];
}>();

const { t } = useI18n();
const { formatNumber, getCurrencySymbol, calculatePipValue } = useForexUtils();

interface FormData {
  accountBalance: number;
  accountCurrency: string;
  riskPercentage: number;
  stopLossPips: number;
  currencyPair: string;
  entryPrice?: number | null;
}

const formData: FormData = reactive({
  accountBalance: 1000,
  accountCurrency: 'USD',
  riskPercentage: 1,
  stopLossPips: 50,
  currencyPair: props.currencyPairs?.[0] || 'EURUSD',
  entryPrice: null,
});

const showResult = ref(false);
const isCalculating = ref(false);

interface Results {
  amountAtRisk: string;
  positionSize: string;
  standardLots: string;
  miniLots: string;
  microLots: string;
}

const formResults: Results = reactive({
  amountAtRisk: '-',
  positionSize: '-',
  standardLots: '-',
  miniLots: '-',
  microLots: '-',
});

const currencyPairOptions = computed(() => {
  return props.currencyPairs.map((symbol) => ({
    label: symbol,
    value: symbol,
  }));
});

const currencyExchangeObject = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key,
    rate: value.rate,
    name: value.name,
    code: value.code,
  }));
});

const validateAccountBalance = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) {
    return t('calculator.validation.validAmount');
  }
  return true;
};

const validateAccountCurrency = (value: unknown): true | string => {
  if (!value || typeof value !== 'string') {
    return t('calculator.validation.validAccountCurrency');
  }
  return true;
};

const validateRiskPercentage = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0 || numValue > 100) {
    return t('calculator.validation.validRiskPercentage');
  }
  return true;
};

const validateStopLossPips = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) {
    return t('calculator.validation.validStopLossPips');
  }
  return true;
};

const validateCurrencyPair = (value: unknown): true | string => {
  if (!value || typeof value !== 'string') {
    return t('calculator.validation.validCurrency');
  }
  return true;
};

const isFormValid = computed(() => {
  return (
    formData.accountBalance > 0 &&
    formData.accountCurrency &&
    formData.riskPercentage > 0 &&
    formData.riskPercentage <= 100 &&
    formData.stopLossPips > 0 &&
    formData.currencyPair
  );
});

const calculatePositionSize = () => {
  isCalculating.value = true;
  try {
    const amountAtRiskValue =
      formData.accountBalance * (formData.riskPercentage / 100);

    const pipValue = calculatePipValue(
      {
        lotSize: 1,
        currencyPair: formData.currencyPair,
        accountCurrency: formData.accountCurrency,
      },
      props.exchangeData
    );

    const standardLotsValue = amountAtRiskValue / (formData.stopLossPips * pipValue);
    const miniLotsValue = standardLotsValue * 10;
    const microLotsValue = standardLotsValue * 100;

    const currencySymbol = getCurrencySymbol(formData.accountCurrency);
    formResults.amountAtRisk = `${currencySymbol}${formatNumber(amountAtRiskValue)}`;
    formResults.positionSize = `${formatNumber(standardLotsValue, 2)} ${t('calculator.common.lots')}`;
    formResults.standardLots = formatNumber(standardLotsValue, 2);
    formResults.miniLots = formatNumber(miniLotsValue, 2);
    formResults.microLots = formatNumber(microLotsValue, 2);
    showResult.value = true;
  } catch (error) {
    console.error('Error calculating position size:', error);
  } finally {
    isCalculating.value = false;
  }
};

watch(
  [
    () => formData.accountBalance,
    () => formData.accountCurrency,
    () => formData.riskPercentage,
    () => formData.stopLossPips,
    () => formData.currencyPair,
  ],
  () => {
    if (isFormValid.value) {
      calculatePositionSize();
    }
  }
);

watch(
  () => props.exchangeData,
  (newRates) => {
    if (newRates && Object.keys(newRates).length > 0 && isFormValid.value) {
      calculatePositionSize();
    }
  },
  { immediate: true }
);
</script>
