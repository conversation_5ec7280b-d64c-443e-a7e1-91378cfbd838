<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">{{ t('calculator.profitLoss.title') }}</h2>
      <p class="text-sm text-neutral-6">{{ t('calculator.profitLoss.description') }}</p>
    </div>

    <VeeForm class="space-y-6">
      <div class="flex gap-1 bg-lightMood-3 rounded-lg p-1">
        <DButton
          :label="t('calculator.profitLoss.buy')"
          type="button"
          class="flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
          :styleType="tradeType === 'buy' ? 'primary' : 'secondary'"
          @click="setTradeType('buy')"
        />
        <DButton
          :label="t('calculator.profitLoss.sell')"
          class="flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
          :styleType="tradeType === 'sell' ? 'primary' : 'secondary'"
          @click="setTradeType('sell')"
        >
          {{ t('calculator.profitLoss.sell') }}
        </DButton>
      </div>

      <div class="form-row grid grid-cols-1 md:grid-cols-2 gap-4">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="currencyPair"
          :rules="validateCurrencyPair"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.currencyPair"
            :options="currencyPairOptions"
            :label="t('calculator.profitLoss.currencyPair')"
            :placeholder="t('calculator.profitLoss.currencyPair')"
            required
            :error="errorMessage"
          />
        </VeeField>

        <VeeField
          v-slot="{ field, errorMessage }"
          name="accountCurrency"
          :rules="validateAccountCurrency"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.accountCurrency"
            :options="currencyOptions"
            :label="t('calculator.profitLoss.accountCurrency')"
            :placeholder="t('calculator.profitLoss.accountCurrency')"
            required
            :error="errorMessage"
          />
        </VeeField>
      </div>

      <div class="flex space-y-3 gap-4 items-stretch flex-wrap">
        <div class="flex bg-lightMood-3 rounded-lg gap-1 p-1 flex-1 self-center mb-0">
          <DButton
            v-for="lotOption in lotOptions"
            :key="lotOption.value"
            :label="lotOption.label"
            class="lot-tab flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
            :styleType="selectedLotTab === lotOption.value ? 'primary' : 'secondary'"
            @click="setLotSize(lotOption.value)"
          />
        </div>
        <VeeField
          v-slot="{ field, errorMessage }"
          name="lotSize"
          :rules="validateLotSize"
          v-model="formData.lotSize"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.01"
            min="0.01"
            :label="t('calculator.profitLoss.lotSize')"
            :placeholder="t('calculator.profitLoss.lotSize')"
            :error="errorMessage"
            required
            class="w-full flex-1"
            :disabled="selectedLotTab !== 'custom'"
          />
        </VeeField>
      </div>

      <div class="form-row grid grid-cols-1 md:grid-cols-2 gap-4">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="entryPrice"
          :rules="validateEntryPrice"
          v-model="formData.entryPrice"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.00001"
            min="0"
            :label="t('calculator.profitLoss.entryPrice')"
            :placeholder="t('calculator.profitLoss.entryPrice')"
            required
            :error="errorMessage"
          />
        </VeeField>

        <VeeField
          v-slot="{ field, errorMessage }"
          name="exitPrice"
          :rules="validateExitPrice"
          v-model="formData.exitPrice"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.00001"
            min="0"
            :label="t('calculator.profitLoss.exitPrice')"
            :placeholder="t('calculator.profitLoss.exitPrice')"
            required
            :error="errorMessage"
          />
        </VeeField>
      </div>

      <div v-if="showResult" class="result-container">
        <DBox class="p-6 bg-lightMood-2 rounded-lg">
          <div class="result-section text-center mb-6">
            <div class="text-sm font-medium text-neutral-6 mb-2">{{ t('calculator.profitLoss.result') }}:</div>
            <div class="text-3xl font-bold" :class="formResults.resultClass">{{ formResults.profitLossResult }}</div>
          </div>
          <div class="result-section">
            <div class="text-lg font-semibold text-neutral-4 mb-4">{{ t('calculator.profitLoss.tradeDetails') }}:</div>
            <div class="space-y-3">
              <div class="info-item flex justify-between items-center p-3 bg-white rounded-lg">
                <span class="info-label text-sm text-neutral-6">{{ t('calculator.profitLoss.direction') }}:</span>
                <span class="info-value text-sm font-semibold text-neutral-4">{{ formResults.tradeDirection }}</span>
              </div>
              <div class="info-item flex justify-between items-center p-3 bg-white rounded-lg">
                <span class="info-label text-sm text-neutral-6">{{ t('calculator.profitLoss.pipMovement') }}:</span>
                <span class="info-value text-sm font-semibold text-neutral-4">{{ formResults.pipMovement }}</span>
              </div>
              <div class="info-item flex justify-between items-center p-3 bg-white rounded-lg">
                <span class="info-label text-sm text-neutral-6">{{ t('calculator.profitLoss.pipValue') }}:</span>
                <span class="info-value text-sm font-semibold text-neutral-4">{{ formResults.pipValue }}</span>
              </div>
              <div class="info-item flex justify-between items-center p-3 bg-white rounded-lg">
                <span class="info-label text-sm text-neutral-6">{{ t('calculator.profitLoss.roi') }}:</span>
                <span class="info-value text-sm font-semibold text-neutral-4">{{ formResults.roi }}</span>
              </div>
            </div>
          </div>
        </DBox>
      </div>
    </VeeForm>
  </DBox>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >,
  currencyPairs: string[],
}>();

const { t } = useI18n();
const {
  formatNumber,
  getCurrencySymbol,
  calculatePipValue,
  getStandardLotSize,
  getPipSize,
  calculatePositionValue,
  lotOptions
} = useForexUtils();

const tradeType = ref<'buy' | 'sell'>('buy');
const selectedLotTab = ref('0.01');
const showResult = ref(false);
const isCalculating = ref(false);

interface FormData {
  currencyPair: string;
  accountCurrency: string;
  lotSize: number;
  entryPrice: number;
  exitPrice: number;
}

const formData: FormData = reactive({
  currencyPair: 'EURUSD',
  accountCurrency: 'USD',
  lotSize: 0.01,
  entryPrice: 1.10000,
  exitPrice: 1.11000,
});

interface Results {
  profitLossResult: string;
  tradeDirection: string;
  pipMovement: string;
  pipValue: string;
  roi: string;
  resultClass: string;
}

const formResults: Results = reactive({
  profitLossResult: '-',
  tradeDirection: '-',
  pipMovement: '-',
  pipValue: '-',
  roi: '-',
  resultClass: 'text-success',
});

const currencyOptions = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key
  }));
});

const currencyPairOptions = computed(() => {
  return props.currencyPairs.map(symbol => ({
    label: symbol,
    value: symbol
  }));
});

const validateCurrencyPair = (value: unknown): true | string => {
  if (!value || typeof value !== 'string') return t('calculator.validation.validCurrency');
  return true;
};
const validateAccountCurrency = (value: unknown): true | string => {
  if (!value || typeof value !== 'string') return t('calculator.validation.validAccountCurrency');
  return true;
};
const validateLotSize = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) return t('calculator.validation.validLotSize');
  return true;
};
const validateEntryPrice = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) return t('calculator.validation.validEntryPrice');
  return true;
};
const validateExitPrice = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) return t('calculator.validation.validExitPrice');
  return true;
};

const isFormValid = computed(() => {
  return (
    tradeType.value &&
    formData.currencyPair &&
    formData.accountCurrency &&
    formData.lotSize > 0 &&
    formData.entryPrice > 0 &&
    formData.exitPrice > 0
  );
});

const setTradeType = (type: 'buy' | 'sell') => {
  tradeType.value = type;
  if (isFormValid.value) {
    calculateProfitLoss();
  }
};

const setLotSize = (value: string) => {
  selectedLotTab.value = value;
  if (value !== 'custom') {
    formData.lotSize = parseFloat(value);
    if (isFormValid.value) {
      calculateProfitLoss();
    }
  }
};

const calculateProfitLoss = () => {
  if (!isFormValid.value) return;
  isCalculating.value = true;
  try {
    const pipMovementValue =
      (formData.exitPrice - formData.entryPrice)
      / getPipSize(formData.currencyPair);

    const pipValue = calculatePipValue(formData, props.exchangeData);

    const profitLoss = (tradeType.value === 'buy' ? 1 : -1) * pipMovementValue * pipValue;

    const currencySymbol = getCurrencySymbol(formData.accountCurrency);

    const positionSize = formData.lotSize * getStandardLotSize(formData.currencyPair);

    const positionValue = calculatePositionValue(
      formData.currencyPair,
      formData.accountCurrency,
      positionSize,
      props.exchangeData
    );

    const marginRequirement = positionValue * 0.01;
    const roiValue = (profitLoss / marginRequirement) * 100;

    formResults.profitLossResult = `${profitLoss >= 0 ? '+' : ''}${currencySymbol}${formatNumber(profitLoss, 2)}`;
    formResults.resultClass = profitLoss >= 0 ? 'text-success' : 'text-danger';
    formResults.tradeDirection = tradeType.value === 'buy' ? t('calculator.profitLoss.buy') : t('calculator.profitLoss.sell');
    formResults.pipMovement = `${Math.abs(pipMovementValue).toFixed(1)} ${t('calculator.common.pips')} ${pipMovementValue >= 0 ? t('calculator.common.up') : t('calculator.common.down')}`;
    formResults.pipValue = `${currencySymbol}${formatNumber(pipValue, 2)} ${t('calculator.common.perPip')}`;
    formResults.roi = `${roiValue >= 0 ? '+' : ''}${formatNumber(roiValue, 2)}%`;

    showResult.value = true;

  } catch (error) {
    console.error('Error calculating profit/loss:', error);
  } finally {
    isCalculating.value = false;
  }
};

watch([
  () => tradeType.value,
  () => formData.currencyPair,
  () => formData.accountCurrency,
  () => formData.lotSize,
  () => formData.entryPrice,
  () => formData.exitPrice
], () => {
  if (isFormValid.value) {
    calculateProfitLoss();
  }
});

watch(() => props.exchangeData, (newRates) => {
  if (newRates && Object.keys(newRates).length > 0 && isFormValid.value) {
    calculateProfitLoss();
  }
}, { immediate: true });
</script>
