<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">
        {{ t('calculator.currencyConverter.title') }}
      </h2>
      <p class="text-sm text-neutral-6">
        {{ t('calculator.currencyConverter.description') }}
      </p>
    </div>

    <VeeForm @submit="handleConvert" class="space-y-8">
      <VeeField
        v-slot="{ field, errorMessage }"
        name="amount"
        v-model="formData.amount"
        :rules="validateAmount"
      >
        <DInput
          v-bind="field"
          type="number"
          step="0.01"
          min="0.01"
          :label="t('calculator.currencyConverter.amount')"
          :placeholder="t('calculator.currencyConverter.amount')"
          required
          :error="errorMessage"
        />
      </VeeField>

      <div class="flex gap-2 items-end relative">
        <VeeField v-slot="{ field, errorMessage }" name="fromCurrency">
          <DSelectBox
            v-bind="field"
            v-model="formData.baseCurrency"
            :options="currencyExchangeObject"
            :label="t('calculator.currencyConverter.from')"
            :placeholder="t('calculator.currencyConverter.from')"
            required
            :error="errorMessage"
            :searchable="true"
            search-placeholder="Search currencies..."
            no-results-text="No currency found"
          />
        </VeeField>
        <DButton
          type="button"
          styleType="secondary"
          @click="swapCurrencies"
          variant="outline"
          size="small"
          icon="DaryaArrowSwapHorizontalIcon"
          class="w-8 h-8"
        />
        <VeeField
          v-slot="{ field, errorMessage }"
          name="toCurrency"
          v-model="formData.quoteCurrency"
          :rules="validateCurrency"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.quoteCurrency"
            :options="currencyExchangeObject"
            :label="t('calculator.currencyConverter.to')"
            :placeholder="t('calculator.currencyConverter.to')"
            required
            :error="errorMessage"
            :searchable="true"
            search-placeholder="Search currencies..."
            no-results-text="No currency found"
          />
        </VeeField>
      </div>

      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">

      </div>

      <div v-if="showResult">
        <DBox class="p-6 rounded-lg">
          <div class="text-center">
            <div class="text-sm font-medium text-neutral-6 mb-2">
              {{ t('calculator.currencyConverter.result') }}:
            </div>
            <div class="text-3xl font-bold text-success mb-4">
              {{ formResults.convertedAmount }}
              {{ getCurrencySymbol(formData.quoteCurrency) }}
            </div>
            <div class="text-xs text-neutral-5 mt-2">
              1 {{ formData.baseCurrency }} = {{ formResults.exchangeRate }}
              {{ formData.quoteCurrency }}
            </div>
          </div>
        </DBox>
      </div>
    </VeeForm>
  </DBox>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >;
}>();

const { t } = useI18n();
const { formatNumber, getCurrencySymbol, calculateCurrencyConvert } =
  useForexUtils();

interface FormData {
  amount: number;
  baseCurrency: string;
  quoteCurrency: string;
}

const formData: FormData = reactive({
  amount: 100,
  baseCurrency: 'USD',
  quoteCurrency: 'EUR',
});

const isCalculating = ref(false);
const showResult = ref(false);

interface Results {
  convertedAmount: string;
  exchangeRate: string;
}

const formResults: Results = reactive({
  convertedAmount: '-',
  exchangeRate: '-',
});

const validateAmount = (value: unknown) => {
  const numValue = Number(value);
  if (!value || isNaN(numValue) || numValue <= 0) {
    return t('calculator.validation.validAmount');
  }
  return true;
};

const validateCurrency = (value: unknown) => {
  if (!value || typeof value !== 'string') {
    return t('calculator.validation.validCurrency');
  }
  return true;
};

const currencyExchangeObject = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key,
    rate: value.rate,
    name: value.name,
    code: value.code,
  }));
});

const isFormValid = computed(() => {
  return (
    formData.amount > 0 &&
    formData.baseCurrency &&
    formData.quoteCurrency &&
    formData.baseCurrency !== formData.quoteCurrency
  );
});

const swapCurrencies = () => {
  const temp = formData.baseCurrency;
  formData.baseCurrency = formData.quoteCurrency;
  formData.quoteCurrency = temp;

  if (isFormValid.value) {
    convertCurrency();
  }
};

const handleConvert = () => {
  if (isFormValid.value) {
    convertCurrency();
  }
};

const convertCurrency = () => {
  isCalculating.value = true;

  try {
    const rates = props.exchangeData;

    let rate = calculateCurrencyConvert(formData, rates);

    const convertedValue = formData.amount * rate;

    formResults.convertedAmount = formatNumber(convertedValue, 2);
    formResults.exchangeRate = formatNumber(rate, 6);

    showResult.value = true;
  } catch (error) {
    console.error('Error converting currency:', error);
  } finally {
    isCalculating.value = false;
  }
};

watch(
  [
    () => formData.amount,
    () => formData.baseCurrency,
    () => formData.quoteCurrency,
  ],
  () => {
    if (isFormValid.value) {
      convertCurrency();
    }
  }
);

watch(
  () => props.exchangeData,
  (newRates) => {
    if (newRates && Object.keys(newRates).length > 0 && isFormValid.value) {
      convertCurrency();
    }
  },
  { immediate: true }
);
</script>

<style scoped></style>
