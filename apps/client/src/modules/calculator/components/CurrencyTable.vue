<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">
        {{ t('calculator.currencyTable.title') }}
      </h2>
    </div>
    <form class="space-y-6 mb-8">
      <div class="flex gap-4">
        <DField
          :label="t('calculator.currencyTable.amount')"
          required
          class="flex-1"
        >
          <template #input>
            <DInput
              v-model="FormData.amount"
              type="number"
              step="0.01"
              min="0.01"
              :placeholder="t('calculator.currencyTable.amount')"
            />
          </template>
        </DField>

        <DField
          :label="t('calculator.currencyTable.baseCurrency')"
          required
          class="flex-1"
        >
          <template #input>
            <DSelectBox
              v-model="FormData.baseCurrency"
              :options="currencyOptions"
              :placeholder="t('calculator.currencyTable.baseCurrency')"
              :searchable="true"
              search-placeholder="Search currencies..."
              no-results-text="No currency found"
            />
          </template>
        </DField>
      </div>
    </form>

    <!-- Results -->
    <div
      class="flex justify-between items-center mb-4 pb-4 border-b border-b-neutral-8"
    >
      <h3 class="text-xl font-semibold text-neutral-2">
        {{ t('calculator.currencyTable.exchangeRates') }}
      </h3>
    </div>
    <DBaseTable
      :data="sortedCurrencies"
      :columns="tableColumns"
      :loading="isLoading"
      @sort="handleSort"
      class="w-full"
    />
  </DBox>
</template>

<script setup lang="ts">
import { computed, ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const { t } = useI18n();
const forexUtils = useForexUtils();

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >;
}>();

const FormData = reactive({
  amount: 1,
  baseCurrency: 'USD',
})

const isLoading = ref<boolean>(false);
const sortColumn = ref<string>('currency');
const sortDirection = ref<'asc' | 'desc'>('asc');

const currencyOptions = computed(() =>
  currencyExchangeData.value.map((item) => ({
    label: item.label,
    value: item.code,
  }))
);

const currencyData = computed(() => {
  const baseRate = parseFloat(
    props.exchangeData[FormData.baseCurrency]?.rate || '1'
  );
  const amountValue = parseFloat(FormData.amount.toString()) || 1;
  return currencyExchangeData.value
    .filter((item) => item.code !== FormData.baseCurrency)
    .map((item) => {
      const rate =
        FormData.baseCurrency !== 'USD'
          ? parseFloat(item.rate) / baseRate
          : parseFloat(item.rate);
      return {
        code: item.code,
        currency: item.name,
        rate: rate,
        convertedAmount: amountValue * rate,
      };
    });
});

const sortedCurrencies = computed(() => {
  return [...currencyData.value].sort((a, b) => {
    let comparison = 0;
    if (sortColumn.value === 'currency') {
      comparison = a.currency.localeCompare(b.currency);
    } else if (sortColumn.value === 'code') {
      comparison = a.code.localeCompare(b.code);
    } else if (sortColumn.value === 'rate') {
      comparison = a.rate - b.rate;
    } else if (sortColumn.value === 'convertedAmount') {
      comparison = a.convertedAmount - b.convertedAmount;
    }
    return sortDirection.value === 'asc' ? comparison : -comparison;
  });
});

const currencyExchangeData = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key,
    rate: value.rate,
    name: value.name,
    code: value.code,
  }));
});

const tableColumns = computed(() => [
  {
    key: 'currency',
    label: t('calculator.currencyTable.currency'),
    sortable: true,
    width: '35%',
  },
  {
    key: 'code',
    label: t('calculator.currencyTable.code'),
    sortable: true,
    width: '15%',
  },
  {
    key: 'rate',
    label: t('calculator.currencyTable.rate'),
    sortable: true,
    width: '25%',
    formatter: (value: number) => formatRate(value),
  },
  {
    key: 'convertedAmount',
    label: t('calculator.currencyTable.convertedAmount'),
    sortable: true,
    width: '25%',
    formatter: (value: number) => formatCurrency(value),
  },
]);

const handleSort = (column: string) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortColumn.value = column;
    sortDirection.value = 'asc';
  }
};

const formatRate = (value: number) => {
  return parseFloat(value.toString()).toFixed(6);
};

const formatCurrency = (value: number) => {
  return forexUtils.formatNumber(value, 2);
};
</script>
