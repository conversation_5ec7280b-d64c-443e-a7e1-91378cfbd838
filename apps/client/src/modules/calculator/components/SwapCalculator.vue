<template>
  <DBox class="p-4 rounded-lg md:p-8">
    <div class="text-center mb-6">
      <h2 class="text-2xl font-bold text-neutral-2 mb-2">
        {{ t('calculator.swap.title') }}
      </h2>
      <p class="text-sm text-neutral-6">
        {{ t('calculator.swap.description') }}
      </p>
    </div>

    <VeeForm class="space-y-6">
      <div class="flex gap-1 bg-lightMood-3 rounded-lg p-1">
        <DButton
          :label="t('calculator.swap.buy')"
          type="button"
          class="flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
          :styleType="tradeType === 'buy' ? 'primary' : 'secondary'"
          @click="setTradeType('buy')"
        />
        <DButton
          :label="t('calculator.swap.sell')"
          type="button"
          class="flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
          :styleType="tradeType === 'sell' ? 'primary' : 'secondary'"
          @click="setTradeType('sell')"
        />
      </div>

      <div class="form-row grid grid-cols-1 md:grid-cols-2 gap-4">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="currencyPair"
          :rules="validateCurrencyPair"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.currencyPair"
            :options="currencyPairOptions"
            :label="t('calculator.swap.currencyPair')"
            :placeholder="t('calculator.swap.currencyPair')"
            required
            :error="errorMessage"
          />
        </VeeField>
        <VeeField
          v-slot="{ field, errorMessage }"
          name="accountCurrency"
          :rules="validateAccountCurrency"
        >
          <DSelectBox
            v-bind="field"
            v-model="formData.accountCurrency"
            :options="currencyOptions"
            :label="t('calculator.swap.accountCurrency')"
            :placeholder="t('calculator.swap.accountCurrency')"
            required
            :error="errorMessage"
          />
        </VeeField>
      </div>

      <!-- Lot Size -->
      <div class="flex space-y-3 gap-4 items-stretch flex-wrap">
        <div
          class="flex bg-lightMood-3 rounded-lg gap-1 p-1 flex-1 self-center mb-0"
        >
          <DButton
            v-for="tab in lotOptions"
            :key="tab.value"
            :label="tab.label"
            class="lot-tab flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
            :styleType="selectedLotTab === tab.value ? 'primary' : 'secondary'"
            @click="setLotSize(tab.value)"
          />
        </div>
        <VeeField
          v-slot="{ field, errorMessage }"
          name="lotSize"
          :rules="validateLotSize"
          v-model="formData.lotSize"
        >
          <DInput
            v-bind="field"
            type="number"
            step="0.01"
            min="0.01"
            :label="t('calculator.swap.lotSize')"
            :placeholder="t('calculator.swap.lotSize')"
            :error="errorMessage"
            required
            class="w-full flex-1"
            :disabled="selectedLotTab !== 'custom'"
          />
        </VeeField>
      </div>

      <div class="form-row grid grid-cols-1 md:grid-cols-2 gap-4">
        <VeeField
          v-slot="{ field, errorMessage }"
          name="swapLong"
          :rules="validateSwapLong"
          v-model="formData.swapLong"
        >
          <div class="input-with-addon relative">
            <DInput
              v-bind="field"
              type="number"
              step="0.01"
              :label="t('calculator.swap.swapLongRate')"
              :placeholder="t('calculator.swap.swapLongRate')"
              :error="errorMessage"
              required
            />
          </div>
        </VeeField>
        <VeeField
          v-slot="{ field, errorMessage }"
          name="swapShort"
          :rules="validateSwapShort"
          v-model="formData.swapShort"
        >
          <div class="input-with-addon relative">
            <DInput
              v-bind="field"
              type="number"
              step="0.01"
              :label="t('calculator.swap.swapShortRate')"
              :placeholder="t('calculator.swap.swapShortRate')"
              :error="errorMessage"
              required
            />
          </div>
        </VeeField>
      </div>

      <div class="space-y-3">
        <div class="flex items-end middle max-w-xs mx-auto">
          <DButton
            type="button"
            class="w-10 h-10 hover:bg-primary-shade4 rounded-none rounded-l-lg font-medium transition-colors"
            @click="decrementDays"
            :label="'-'"
          />
          <VeeField
            v-slot="{ field, errorMessage }"
            name="days"
            :rules="validateDays"
            v-model="formData.days"
          >
            <DInput
              v-bind="field"
              type="number"
              min="1"
              :label="t('calculator.swap.days')"
              :placeholder="t('calculator.swap.days')"
              :error="errorMessage"
              required
              class="text-center border-l-0 border-r-0 rounded-none w-full"
            />
          </VeeField>
          <DButton
            type="button"
            class="w-10 h-10 hover:bg-primary-shade4 rounded-none rounded-r-lg font-medium transition-colors"
            @click="incrementDays"
            :label="'+'"
          />
        </div>
        <div
          class="flex bg-lightMood-3 rounded-lg gap-1 p-1 flex-1 self-center mb-0"
        >
          <DButton
            v-for="preset in dayPresets"
            :key="preset.value"
            :label="preset.label"
            class="lot-tab flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors"
            :styleType="
              selectedWeekOption === preset.value ? 'primary' : 'secondary'
            "
            @click="setDays(preset.value)"
          />
        </div>
      </div>

      <div v-if="showResult" class="result-container">
        <DBox class="p-6 bg-lightMood-2 rounded-lg">
          <div class="result-section text-center mb-6">
            <div class="text-sm font-medium text-neutral-6 mb-2">
              {{ t('calculator.swap.result') }}:
            </div>
            <div class="text-3xl font-bold" :class="formResults.resultClass">
              {{ formResults.swapResult }}
            </div>
          </div>
          <div class="result-section">
            <div class="text-lg font-semibold text-neutral-4 mb-4">
              {{ t('calculator.swap.calculationDetails') }}:
            </div>
            <div class="space-y-1">
              <div class="flex justify-between text-md p-3">
                <span class="text-neutral-6"
                  >{{ t('calculator.swap.position') }}:</span
                >
                <span>{{ formResults.positionInfo }}</span>
              </div>
              <div class="flex justify-between p-3">
                <span class="text-neutral-6"
                  >{{ t('calculator.swap.swapRate') }}:</span
                >
                <span>{{ formResults.swapRateInfo }}</span>
              </div>
              <div class="flex justify-between p-3">
                <span class="text-neutral-6"
                  >{{ t('calculator.swap.dailySwap') }}:</span
                >
                <span>{{ formResults.dailySwapInfo }}</span>
              </div>
              <div class="flex justify-between p-3">
                <span class="text-neutral-6"
                  >{{ t('calculator.swap.tripleSwapDay') }}:</span
                >
                <span>{{ t('calculator.swap.tripleSwapNote') }}</span>
              </div>
            </div>
          </div>
        </DBox>
      </div>
    </VeeForm>
  </DBox>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref, ComputedRef } from 'vue';
import { useI18n } from 'vue-i18n';
import { useForexUtils } from '@/modules/calculator';

const props = defineProps<{
  exchangeData: Record<
    string,
    {
      rate: string;
      name: string;
      code: string;
    }
  >;
  currencyPairs: string[];
}>();

const { t } = useI18n();
const {
  formatNumber,
  calculatePipValue,
  getCurrencySymbol,
  lotOptions
} = useForexUtils();

const tradeType = ref<'buy' | 'sell'>('buy');
const selectedLotTab = ref('0.01');
const selectedWeekOption = ref('');
const showResult = ref(false);
const isCalculating = ref(false);

interface FormData {
  currencyPair: string;
  accountCurrency: string;
  lotSize: number;
  swapLong: number;
  swapShort: number;
  days: number;
}

const formData: FormData = reactive({
  currencyPair: props.currencyPairs?.[0] || 'EURUSD',
  accountCurrency: 'USD',
  lotSize: 0.01,
  swapLong: -2.5,
  swapShort: 0.5,
  days: 1,
});

interface Results {
  swapResult: string;
  positionInfo: string;
  swapRateInfo: string;
  dailySwapInfo: string;
  resultClass: string;
}

const formResults: Results = reactive({
  swapResult: '-',
  positionInfo: '-',
  swapRateInfo: '-',
  dailySwapInfo: '-',
  resultClass: 'text-success',
});

const dayPresets = computed(() => [
  { value: '1', label: t('calculator.swap.presets.oneDay') },
  { value: '5', label: t('calculator.swap.presets.workWeek') },
  { value: '7', label: t('calculator.swap.presets.oneWeek') },
  { value: '30', label: t('calculator.swap.presets.oneMonth') },
]);

const currencyOptions = computed(() => {
  return Object.entries(props.exchangeData).map(([key, value]) => ({
    label: `${value.name} (${key})`,
    value: key,
  }));
});

const currencyPairOptions = computed(() => {
  return props.currencyPairs.map((symbol) => ({
    label: symbol,
    value: symbol,
  }));
});

const validateCurrencyPair = (value: unknown): true | string => {
  if (!value || typeof value !== 'string')
    return t('calculator.validation.validCurrency');
  return true;
};
const validateAccountCurrency = (value: unknown): true | string => {
  if (!value || typeof value !== 'string')
    return t('calculator.validation.validAccountCurrency');
  return true;
};
const validateLotSize = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue <= 0)
    return t('calculator.validation.validLotSize');
  return true;
};
const validateSwapLong = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue))
    return t('calculator.validation.validSwapLong');
  return true;
};
const validateSwapShort = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue))
    return t('calculator.validation.validSwapShort');
  return true;
};
const validateDays = (value: unknown): true | string => {
  const numValue = typeof value === 'number' ? value : Number(value);
  if (!value || isNaN(numValue) || numValue < 1)
    return t('calculator.validation.validDays');
  return true;
};

const isFormValid = computed(() => {
  return (
    tradeType.value &&
    formData.currencyPair &&
    formData.accountCurrency &&
    formData.lotSize > 0 &&
    formData.swapLong !== null &&
    formData.swapShort !== null &&
    formData.days >= 1
  );
});

const setTradeType = (type: 'buy' | 'sell') => {
  tradeType.value = type;
  if (isFormValid.value) {
    calculateSwap();
  }
};

const setLotSize = (value: string) => {
  selectedLotTab.value = value;
  if (value !== 'custom') {
    formData.lotSize = parseFloat(value);
    if (isFormValid.value) {
      calculateSwap();
    }
  }
};

const setDays = (value: string) => {
  selectedWeekOption.value = value;
  formData.days = parseInt(value);
  if (isFormValid.value) {
    calculateSwap();
  }
};

const decrementDays = () => {
  if (formData.days > 1) {
    formData.days = formData.days - 1;
    selectedWeekOption.value = '';
    if (isFormValid.value) {
      calculateSwap();
    }
  }
};

const incrementDays = () => {
  formData.days = formData.days + 1;
  selectedWeekOption.value = '';
  if (isFormValid.value) {
    calculateSwap();
  }
};

const calculateSwap = () => {
  if (!isFormValid.value) return;
  isCalculating.value = true;
  try {

    const pipValue = calculatePipValue(formData, props.exchangeData);

    const swapRate =
      tradeType.value === 'buy' ? formData.swapLong : formData.swapShort;

    const dailySwapValue = swapRate * pipValue;

    const wednesdaysCount = Math.floor(formData.days / 7);
    const regularDaysCount = formData.days - wednesdaysCount;
    const totalSwap =
      regularDaysCount * dailySwapValue + wednesdaysCount * 3 * dailySwapValue;

    const currencySymbol =
      getCurrencySymbol(formData.accountCurrency);

    formResults.swapResult = `${totalSwap >= 0 ? '+' : ''}${currencySymbol}${formatNumber(totalSwap, 2)}`;
    formResults.resultClass = totalSwap >= 0 ? 'text-success' : 'text-danger';
    formResults.positionInfo = `${formData.lotSize} ${t('calculator.common.lots')} ${tradeType.value === 'buy' ? t('calculator.common.long') : t('calculator.common.short')} ${formData.currencyPair}`;
    formResults.swapRateInfo = `${swapRate} ${t('calculator.common.pips')} ${t('calculator.common.perDay')} (${tradeType.value === 'buy' ? t('calculator.common.long') : t('calculator.common.short')})`;
    formResults.dailySwapInfo = `${currencySymbol}${formatNumber(dailySwapValue, 2)} ${t('calculator.common.perDay')}`;
    showResult.value = true;
  } catch (error) {
    console.error('Error calculating swap:', error);
  } finally {
    isCalculating.value = false;
  }
};

watch(
  [
    () => tradeType.value,
    () => formData.currencyPair,
    () => formData.accountCurrency,
    () => formData.lotSize,
    () => formData.swapLong,
    () => formData.swapShort,
    () => formData.days,
  ],
  () => {
    if (isFormValid.value) {
      calculateSwap();
    }
  }
);

watch(
  () => props.exchangeData,
  (newRates) => {
    if (newRates && Object.keys(newRates).length > 0 && isFormValid.value) {
      calculateSwap();
    }
  },
  { immediate: true }
);
</script>
