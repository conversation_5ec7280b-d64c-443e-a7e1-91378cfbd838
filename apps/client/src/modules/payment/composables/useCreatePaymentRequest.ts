import { isAxiosError } from 'axios';
import { type MaybeRef } from 'vue';
import { useMutation, type UseMutationOptions } from '@tanstack/vue-query';

import { createCheckoutPaymentRequest } from '../request';

export interface paymentFormModel {
  amount: number | null;
  paymentGatewayId: number | null;
  planId: number | null;
  expandData: string | object | null;
  serviceType: number | null;
  paymentTemplateId: number | null;
}

interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

function paymentFactory(formModel) {
  return {
    amount: formModel.amount,
    paymentGatewayId: formModel.paymentGatewayId,
    planId: formModel.planId,
    expandData: JSON.stringify(formModel.expandData) || '{}',
    serviceType: formModel.serviceType,
    paymentTemplateId: formModel.paymentTemplateId,
  };
}

export interface UseCreateMutationOptions
  extends Omit<
    UseMutationOptions<ApiResponse, Error, MaybeRef<paymentFormModel>>,
    'mutationFn'
  > {}

export default function useCreatePaymentMutation(
  options?: UseCreateMutationOptions
) {
  const { mutate, mutateAsync, status, isError, error, data } = useMutation<
    ApiResponse,
    Error,
    MaybeRef<paymentFormModel>
  >({
    mutationFn: async (formModel) => {
      try {
        const response = await createCheckoutPaymentRequest(
          paymentFactory(formModel)
        );

        return response.data;
      } catch (err) {
        if (isAxiosError(err)) {
          const message = err.response?.data?.message || 'Error';

          throw new Error(message);
        }

        throw new Error('An unexpected error occurred');
      }
    },
    ...options,
  });

  return {
    createPayment: mutate,
    mutateAsync,
    status,
    isError,
    error,
    data,
  } as const;
}
