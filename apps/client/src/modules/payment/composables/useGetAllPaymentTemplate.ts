import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllPaymentTemplate } from '../request';

type paymentTemplateResponse = Awaited<
  ReturnType<typeof getAllPaymentTemplate>
>;

export default function useGetAllPaymentTemplate() {
  const { status, isError, data, refetch, isFetching, error } = useQuery<
    paymentTemplateResponse,
    Error
  >({
    queryKey: ['transactionList'],
    queryFn: getAllPaymentTemplate,
  });

  const paymentTemplateList = computed(() => data.value?.data?.data ?? []);

  return {
    getAllPaymentTemplate: refetch,
    paymentTemplateList,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
