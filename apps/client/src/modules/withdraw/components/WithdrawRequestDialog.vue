<template>
  <DDialog
    :open="isOpenWithdrawRequestDialog"
    @update:open="emit('close')"
    :showCloseIcon="false"
    :closeOnClickOutside="false"
    maxWidth="lg"
  >
    <template #body>
      <div class="flex items-center justify-between px-1 mb-8">
        <div class="flex items-center">
          <div class="bg-primary-tint8 p-2 rounded">
            <DaryaOutlineCardSendIcon size="32" class="text-primary" />
          </div>

          <div class="flex flex-col ms-4">
            <span class="pb-1 font-bold text-lg">
              {{ t('withdraw.request_a_withdrawal') }}
            </span>
            <span class="text-neutral-5 font-[10px]">
              {{ t('withdraw.make_sure_information') }}
            </span>
          </div>
        </div>

        <button
          @click="emit('close')"
          class="p-1 hover:bg-gray-100 rounded-full transition-colors"
          aria-label="Close"
        >
          <DaryaCloseIcon />
        </button>
      </div>

      <div class="mt-8 mb-5 px-1">
        <span class="text-primary-shade1 text-3xl font-semibold">$84.00</span>
      </div>

      <VeeForm v-slot="{ handleSubmit }">
        <form
          autocomplete="off"
          @submit="handleSubmit(onSubmit)"
          class="space-y-3 px-1"
        >
          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.firstName"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="First Name"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.lastName"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="Last Name"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.phoneNumber"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="phoneNumber"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField v-slot="{ field, errorMessage }" name="country">
            <DSelectBox
              v-bind="field"
              v-model="formModel.countryId"
              :options="countryListOptions"
              placeholder="Country"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.address"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="Address"
              required
              :error="errorMessage"
            />
          </VeeField>

          <div class="grid grid-cols-2 gap-2">
            <VeeField
              v-slot="{ field, errorMessage }"
              name="name"
              v-model="formModel.city"
            >
              <DInput
                v-bind="field"
                type="text"
                placeholder="City"
                required
                :error="errorMessage"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="name"
              v-model="formModel.zipCode"
            >
              <DInput
                v-bind="field"
                type="text"
                placeholder="Zip Code"
                required
                :error="errorMessage"
              />
            </VeeField>
          </div>

          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.accountNumber"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="Account Number"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.iban"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="IBAN"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="name"
            v-model="formModel.swift"
          >
            <DInput
              v-bind="field"
              type="text"
              placeholder="Swift"
              required
              :error="errorMessage"
            />
          </VeeField>
          <VeeField v-slot="{ field, errorMessage }" name="agreement">
            <DCheckbox
              v-bind="field"
              v-model="formModel.agreement"
              :label="'I confirm the above banking information is accurate'"
              :error="errorMessage"
              :value="true"
            />
          </VeeField>

          <div class="flex justify-center gap-2 mt-5">
            <button
              @click="emit('close')"
              class="w-32 rounded-lg border border-primary p-3 font-bold text-sm text-primary cursor-pointer"
            >
              {{ t('common.cancel') }}
            </button>
            <button
              type="submit"
              class="rounded-lg bg-primary text-white p-3 font-bold font-sm cursor-pointer"
            >
              {{ t('withdraw.submit_withdrawal') }}
            </button>
          </div>
        </form>
      </VeeForm>
    </template>
  </DDialog>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, reactive } from 'vue';
import { useI18n } from 'vue-i18n';

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const props = defineProps<{
  isOpenWithdrawRequestDialog: boolean;
}>();

const { t } = useI18n();
const formModel = reactive({
  firstName: '',
  lastName: '',
  phoneNumber: '',
  countryId: 0,
  address: '',
  city: '',
  zipCode: '',
  accountNumber: 0,
  iban: '',
  swift: '',
  agreement: false,
});

const countryListOptions = [];
const onSubmit = () => {};
</script>
