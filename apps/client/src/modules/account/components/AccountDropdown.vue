<template>
  <DPopper
    v-model="isDropdownOpen"
    placement="bottom"
    :arrow="false"
    offset-y="22"
    append-to-body
    @click="isDropdownOpen = !isDropdownOpen"
  >
    <template #default>
      <button class="w-56 px-4 py-1.5 rounded-lg border border-neutral-9 mr-3">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <DaryaAccountsIcon size="16" />
            <span class="ps-1">
              {{ t('account.login_id') }}: # {{ route.params.id }}</span
            >
          </div>

          <DaryaArrowDownIcon size="16" />
        </div>
      </button>
    </template>

    <template #content>
      <div class="flex flex-col w-96 bg-white shadow-md rounded-lg p-2">
        <div class="h-[298px] overflow-y-scroll">
          <template v-for="section in sectionsToDisplay" :key="section.type">
            <div class="flex items-center w-full mt-2">
              <span class="text-neutral-5 text-xs font-normal pr-2">
                {{ section.label }}
              </span>
              <div class="flex-1 bg-neutral-13 h-[0.5px]"></div>
            </div>

            <template
              v-for="(account, index) in section.accounts"
              :key="account.id"
            >
              <router-link
                v-if="index < MAX_ACCOUNTS_PER_SECTION"
                v-slot="{ isActive, navigate }"
                :to="getAccountRoute(account.id)"
                custom
              >
                <div
                  class="flex items-center mt-2 rounded-lg px-2 py-3 border border-neutral-11 hover:border-primary-tint2 cursor-pointer"
                  @click="navigate"
                  :class="{ 'bg-primary-tint9/20 border-none': isActive }"
                >
                  <DaryaAccountsIcon class="text-primary-tint9 mr-1" />
                  <span class="pr-2 text-neutral-2 text-base font-normal">
                    # {{ account.id }}
                  </span>

                  <div class="space-x-2 h-3 border-r border-gray-200"></div>

                  <span class="pl-2 text-neutral-5 text-base font-normal">
                    {{ account.type || 'Trial 5K' }}
                  </span>
                </div>
              </router-link>
            </template>
          </template>
        </div>

        <div class="mt-2 flex justify-center w-full">
          <router-link v-slot="{ navigate }" to="/" custom>
            <button class="px-3 py-2 rounded-lg bg-primary" @click="navigate">
              <span class="text-white">
                {{ t('account.manage_accounts') }}
              </span>
            </button>
          </router-link>
        </div>
      </div>
    </template>
  </DPopper>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

import {
  useGetAllAccountByUserId,
  useGetAccountStatusTypeEnums,
} from '@/modules/account';

const props = defineProps<{
  userId?: string;
}>();

const route = useRoute();
const { t } = useI18n();

const isDropdownOpen = ref(false);

const { allAccountData } = useGetAllAccountByUserId(props.userId, true);

const ACCOUNT_SECTION_CONFIG = [
  { type: '1', label: 'Active' },
  { type: '3', label: 'Breached' },
];

const sectionsToDisplay = computed(() => {
  return ACCOUNT_SECTION_CONFIG.map((config) => ({
    ...config,
    accounts: allAccountData.value.filter(
      (account) => String(account.status) === config.type
    ),
  })).filter((section) => section.accounts.length > 0);
});

const MAX_ACCOUNTS_PER_SECTION = 3;

const getAccountRoute = (accountId: string) => {
  const isTradingRoute = route.path.endsWith('trading-overview');

  return isTradingRoute
    ? `/account/${accountId}/trading-overview`
    : `/account/${accountId}/account-overview`;
};
</script>
