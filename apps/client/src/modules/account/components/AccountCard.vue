<template>
  <div
    class="flex w-80 flex-col gap-6 px-6 py-4 rounded-3xl border border-primary-tint4 bg-lightMood-2"
  >
    <div class="flex items-center">
      <div
        class="w-20 bg-primary-tint8 text-primary-tint9 rounded-lg py-6 px-5"
      >
        <component :is="iconComponent" size="36" />
      </div>

      <div class="ms-4">
        <span class="block">
          <span class="text-lg font-bold">{{ account.planTypeName }}</span>
          <span class="text-lg text-primary-shade1 font-bold">
            {{ account.planName }}
          </span>
        </span>
        <span class="text-sm font-normal text-neutral-6">
          #{{ account.metaAccountId }}
        </span>
      </div>
    </div>

    <!-- Account Details -->
    <div class="flex flex-col">
      <div class="flex justify-between mb-2">
        <span class="text-xs text-neutral-5">
          {{ t('common.account_type') }}
        </span>
        <span class="text-xs font-bold text-neutral-4">
          {{ account.accountType }}
        </span>
      </div>
      <div class="flex justify-between mb-2">
        <span class="text-xs text-neutral-5">
          {{ t('common.server_type') }}
        </span>
        <span class="text-xs font-bold text-neutral-4">
          {{ account.tradingPlatform }}
        </span>
      </div>

      <div class="flex justify-between mb-2">
        <span class="text-xs text-neutral-5">{{ t('common.balance') }}</span>
        <span class="text-xs font-bold text-neutral-4">
          ${{ account.initialBalance }}
        </span>
      </div>

      <div class="flex justify-between mb-2">
        <span class="text-xs text-neutral-5">{{ t('common.equity') }}</span>
        <span class="text-xs font-bold text-neutral-4">
          ${{ account.equity }}
        </span>
      </div>
    </div>

    <!-- Navigation Button -->
    <router-link
      v-slot="{ navigate }"
      :to="`/account/${account.id}/account-overview`"
      custom
    >
      <button
        @click="navigate"
        class="flex py-2 rounded-lg w-full justify-center items-center text-primary border border-primary cursor-pointer"
      >
        <span class="me-2 font-bold text-sm">
          {{ t('home.view_dashboard') }}
        </span>
        <DaryaOutlineArrowRightIcon size="20" />
      </button>
    </router-link>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  account: {
    id: string;
    planTypeName: string;
    planName: string;
    metaAccountId: string;
    accountType: string;
    tradingPlatform: string;
    initialBalance: number;
    equity: number;
    planTypeId: number;
  };
}>();

const { t } = useI18n();

const iconComponent = computed(() => {
  if (props.account.planTypeId === 1) return 'DaryaFillCourseIcon';

  switch (props.account.planName) {
    case 'Standard':
      return 'DaryaFillLeaderBoardIcon';
    case 'Pro':
      return 'DaryaFillRocketIcon';
    default:
      return 'DaryaFillFlameIcon';
  }
});
</script>
