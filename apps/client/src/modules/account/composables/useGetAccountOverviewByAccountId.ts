import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAccountOverviewById } from '../request';

export default function useGetAccountOverviewById(
  accountId: MaybeRef<number | undefined>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const accountIdRef = toRef(accountId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['accountOverviewData', accountIdRef.value]),
    queryFn: () => {
      const id = accountIdRef.value;

      if (!id) throw new Error('account ID is required');

      return getAccountOverviewById(id);
    },
    enabled: shouldFetchRef,
  });

  const accountOverviewData = computed(() => data.value?.data?.data ?? {});

  return {
    getAccountOverviewById: refetch,
    accountOverviewData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
