import { computed } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAccountStatusTypeEnums } from '../request';

type AccountTypeEnumsResponse = Awaited<
  ReturnType<typeof getAccountStatusTypeEnums>
>;

export default function useGetAccountStatusTypeEnums() {
  const { status, isError, data, refetch } = useQuery<
    AccountTypeEnumsResponse,
    Error
  >({
    queryKey: ['getAccountStatusTypeList'],
    queryFn: getAccountStatusTypeEnums,
  });

  const accountTypeList = computed(() => data.value?.data?.data ?? []);

  return {
    getAccountStatusTypeEnums: refetch,
    accountTypeList,
    isError,
    status,
  } as const;
}
