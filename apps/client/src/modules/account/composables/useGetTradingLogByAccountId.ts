import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getTradingLogsAccountId } from '../request';

export default function useGetTradingLogByAccountId(
  accountId?: MaybeRef<number | undefined>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const accountIdRef = toRef(accountId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['tradingLogsData', accountIdRef.value]),
    queryFn: () => {
      const id = accountIdRef.value;

      if (!id) throw new Error('account ID is required');

      return getTradingLogsAccountId(id);
    },
    enabled: shouldFetchRef,
  });

  const tradingLogsData = computed(() => data.value?.data?.data ?? []);

  return {
    getTradingLogsByAccountId: refetch,
    tradingLogsData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
