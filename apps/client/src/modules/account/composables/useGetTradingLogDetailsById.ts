import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getTradingLogsDetailById } from '../request';

export default function useGetTradingLogDetailById(
  id: MaybeRef<number>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const idRef = toRef(id);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['tradingLogDetailData', idRef.value]),
    queryFn: () => {
      const id = idRef.value;

      if (!id) throw new Error('ID is required');

      return getTradingLogsDetailById(id);
    },
    enabled: shouldFetchRef,
  });

  const tradingLogDetailData = computed(() => data.value?.data?.data ?? []);

  return {
    getTradingLogDetailById: refetch,
    tradingLogDetailData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
