import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllAccountByUserId } from '../request';

export default function useGetAllAccountByUserId(
  userId: MaybeRef<string> | undefined,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['allAccountData', userIdRef.value]),
    queryFn: () => {
      const id = userIdRef.value;

      if (!id) throw new Error('user ID is required');

      return getAllAccountByUserId(id);
    },
    enabled: shouldFetchRef,
  });

  const allAccountData = computed(() => data.value?.data?.data ?? []);

  return {
    getAllAccount: refetch,
    allAccountData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
