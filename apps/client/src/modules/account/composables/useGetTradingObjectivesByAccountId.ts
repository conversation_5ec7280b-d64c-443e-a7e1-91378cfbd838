import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getTradingObjectiveById } from '../request';

export default function useGetTradingObjectiveById(
  accountId: MaybeRef<number | undefined>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const accountIdRef = toRef(accountId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['tradingObjectiveData', accountIdRef.value]),
    queryFn: () => {
      const id = accountIdRef.value;

      if (!id) throw new Error('account ID is required');

      return getTradingObjectiveById(id);
    },
    enabled: shouldFetchRef,
  });

  const tradingObjectiveData = computed(() => data.value?.data?.data ?? {});

  return {
    getTradingObjectiveById: refetch,
    tradingObjectiveData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
