import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getDailyTradeByAccountId } from '../request';

export default function useGetDailyTradeById(
  accountId: MaybeRef<number | undefined>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const accountIdRef = toRef(accountId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['accountDailyTradeData', accountIdRef.value]),
    queryFn: () => {
      const id = accountIdRef.value;

      if (!id) throw new Error('account ID is required');

      return getDailyTradeByAccountId(id);
    },
    enabled: shouldFetchRef,
  });

  const accountDailyTradeData = computed(() => data.value?.data?.data ?? []);

  return {
    getDailyTradeById: refetch,
    accountDailyTradeData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
