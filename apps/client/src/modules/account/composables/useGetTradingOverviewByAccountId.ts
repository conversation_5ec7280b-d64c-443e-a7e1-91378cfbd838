import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getTradingOverviewById } from '../request';

export default function useGetTradingOverviewById(
  accountId: MaybeRef<number | undefined>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const accountIdRef = toRef(accountId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['tradingOverviewData', accountIdRef.value]),
    queryFn: () => {
      const id = accountIdRef.value;

      if (!id) throw new Error('account ID is required');

      return getTradingOverviewById(id);
    },
    enabled: shouldFetchRef,
  });

  const tradingOverviewData = computed(() => data.value?.data?.data ?? []);

  return {
    getTradingOverviewById: refetch,
    tradingOverviewData,
    isError,
    status,
    isFetching,
    error,
  } as const;
}
