import http from '@/services/http';

import { ACCOUNT_API } from '@/configs/env';

export function getAccountOverviewById(accountId) {
  return http.get(`${ACCOUNT_API}/Report/GetAccountOverview`, {
    params: { accountId: accountId },
  });
}

export function getTradingObjectiveById(accountId) {
  return http.get(`${ACCOUNT_API}/Report/GetTradingObjectives`, {
    params: { accountId: accountId },
  });
}
export function getTradingOverviewById(accountId) {
  return http.get(`${ACCOUNT_API}/Report/GetTradingOverview`, {
    params: { accountId: accountId },
  });
}
export function getDailyTradeByAccountId(accountId) {
  return http.get(`${ACCOUNT_API}/Report/GetDailyTrade`, {
    params: { accountId: accountId },
  });
}
export function getTradingLogsAccountId(accountId) {
  return http.get(`${ACCOUNT_API}/Report/GetTradingLogs`, {
    params: { accountId: accountId },
  });
}
export function getTradingLogsDetailById(id) {
  return http.get(`${ACCOUNT_API}/Report/GetTradingLogsDetails`, {
    params: { id: id },
  });
}
export function getAllAccountByUserId(userId) {
  return http.get(`${ACCOUNT_API}/Account/GetAllAccountByUserId`, {
    params: { userId: userId },
  });
}

export function getAccountStatusTypeEnums() {
  return http.get(`${ACCOUNT_API}/Account/GetAccountStatusTypeEnums`);
}
