import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getAllCertificateByUserId } from '../request';

export default function useGeAllCertificateByUserId(
  id: MaybeRef<string>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const idRef = toRef(id);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching, error } = useQuery({
    queryKey: computed(() => ['certificateData', idRef.value]),
    queryFn: () => {
      const id = idRef.value;

      if (!id) throw new Error('User ID is required');

      return getAllCertificateByUserId(id);
    },
    enabled: shouldFetchRef,
  });

  const certificateData = computed(() => data.value?.data?.data ?? []);

  return {
    getAllCertificateByUserId: refetch,
    certificateData,
    isError,
    status,
    error,
    isFetching,
  } as const;
}
