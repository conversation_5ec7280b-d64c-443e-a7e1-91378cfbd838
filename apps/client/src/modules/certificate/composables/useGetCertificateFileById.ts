import { computed, toRef, type MaybeRef } from 'vue';
import { useQuery } from '@tanstack/vue-query';

import { getCertificateFileByUserId } from '../request';

export default function useGetCertificateFileById(
  userId?: MaybeRef<string>,
  certificateId?: MaybeRef<string>,
  shouldFetch: MaybeRef<boolean> = false
) {
  const userIdRef = toRef(userId);
  const certificateIdRef = toRef(certificateId);
  const shouldFetchRef = toRef(shouldFetch);

  const { data, refetch, isError, status, isFetching } = useQuery({
    queryKey: computed(() => [
      'certificateFile',
      userIdRef.value,
      certificateIdRef.value,
    ]),
    queryFn: () => {
      if (!userIdRef.value || !certificateIdRef.value)
        throw new Error('User ID And CertificateID is required');

      return getCertificateFileByUserId({
        userId: userIdRef.value,
        certificateId: certificateIdRef.value,
      });
    },
    enabled: shouldFetchRef,
  });

  const testData = computed(() => data.value?.data.data ?? {});
  return {
    getCertificateFileById: refetch,
    isError,
    testData,
    status,
    isFetching,
  } as const;
}
