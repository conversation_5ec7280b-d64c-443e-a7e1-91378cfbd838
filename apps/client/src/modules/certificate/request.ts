import http from '@/services/http';

import { CERTIFICATE_API } from '@/configs/env';

export function getAllCertificateByUserId(userId) {
  return http.get(`${CERTIFICATE_API}/Certificate/GetAllCertificateByUser`, {
    params: {
      userId: userId,
    },
  });
}

export function getCertificateFileByUserId(data) {
  return http.get(`${CERTIFICATE_API}/Certificate/GetCertificateFile`, {
    params: {
      certificateId: data.certificateId,
      userId: data.userId,
    },
  });
}
