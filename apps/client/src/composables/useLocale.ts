import { computed, watch } from 'vue';
import { useStorage } from '@vueuse/core';
import { useI18n } from 'vue-i18n';

export type SupportedLocale =
  | 'en'
  | 'ar'
  | 'fr'
  | 'es'
  | 'ja'
  | 'pt'
  | 'sr'
  | 'ru';

const rtlLanguages: SupportedLocale[] = ['ar'];

export const useLocale = () => {
  const { locale } = useI18n();
  const storedLocale = useStorage<SupportedLocale>(
    'user-language-preference',
    'en'
  );

  watch(
    storedLocale,
    (newLocale) => {
      if (
        !(
          ['en', 'ar', 'fr', 'es', 'ja', 'pt', 'sr', 'ru'] as SupportedLocale[]
        ).includes(newLocale)
      ) {
        storedLocale.value = 'en';
      }

      if (locale.value !== storedLocale.value) {
        locale.value = storedLocale.value;
      }
    },
    { immediate: true }
  );

  const isRtl = computed(() => rtlLanguages.includes(storedLocale.value));
  const textDirection = computed(() => (isRtl.value ? 'rtl' : 'ltr'));

  const updateLocale = (newLocale: SupportedLocale) => {
    if (storedLocale.value !== newLocale) {
      storedLocale.value = newLocale;
    }
  };

  return {
    locale: storedLocale,
    isRtl,
    textDirection,
    updateLocale,
  };
};
