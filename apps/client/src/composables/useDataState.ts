import { computed, type ComputedRef, type Ref } from 'vue';
import {
  getEmptyStateConfig,
  type EmptyStateType,
} from '@/composables/emptyStateConfig';

export type QueryStatus = 'pending' | 'error' | 'success' | 'idle';

export interface ErrorInfo {
  type?: 'network' | 'server' | 'client' | 'generic';
  status?: number;
  message?: string;
}

export interface StateConfig {
  type?: EmptyStateType;
  title?: string;
  message?: string;
  image?: string;
  imageWidth?: string;
  imageHeight?: string;
}

export interface DataStateResult {
  isLoading: ComputedRef<boolean>;
  hasError: ComputedRef<boolean>;
  hasNoData: ComputedRef<boolean>;
  hasData: ComputedRef<boolean>;
  isRefetching: ComputedRef<boolean>;
  errorInfo: ComputedRef<ErrorInfo | undefined>;
  errorConfig: ComputedRef<StateConfig>;
  emptyConfig: ComputedRef<StateConfig>;
}

/**
 * Analyze error to determine type and appropriate configuration
 */
function analyzeError(error: any): ErrorInfo {
  if (!error) return { type: 'generic' };

  // Check for HTTP response errors first (these have priority)
  if (error?.response?.status) {
    const status = error.response.status;

    // Server errors (500+)
    if (status >= 500) {
      return {
        type: 'server',
        status: status,
        message:
          'We were unable to process your request. Please try again later.',
      };
    }

    // Client errors (400-499, excluding 401 which is handled by axios)
    if (status >= 400 && status !== 401) {
      return {
        type: 'client',
        status: status,
        message:
          error?.response?.data?.message ||
          'There was a problem with your request.',
      };
    }
  }

  // Network errors (no response received)
  if (
    !navigator.onLine ||
    error?.code === 'NETWORK_ERROR' ||
    (error?.request && !error?.response)
  ) {
    return {
      type: 'network',
      message: 'Please check your internet connection and try again.',
    };
  }

  // Generic error
  return {
    type: 'generic',
    message: error?.message || 'Something went wrong. Please try again.',
  };
}

/**
 * Get error configuration based on error info
 */
function getErrorConfig(errorInfo?: ErrorInfo): StateConfig {
  if (!errorInfo) {
    return {
      type: 'custom',
      title: 'Something Went Wrong',
      message: 'An error occurred while loading the data. Please try again.',
      image: 'server-error.svg',
    };
  }

  switch (errorInfo.type) {
    case 'network':
      return {
        type: 'custom',
        title: 'Seems there is an issue with your network connection',
        message: 'Please check your internet.',
        image: 'network-error.svg',
      };
    case 'server':
      return {
        type: 'custom',
        title: 'Something went wrong',
        message: 'We are working to fix it , Please try again.',
        image: 'server-error.svg',
      };
    case 'client':
      return {
        type: 'custom',
        title: 'We were unable to process your request',
        message: errorInfo.message || 'Please try again',
        image: 'error.svg',
      };
    default:
      return {
        type: 'custom',
        title: 'Something Went Wrong',
        message:
          errorInfo.message ||
          'An unexpected error occurred. Please try again.',
        image: 'error.svg',
      };
  }
}

/**
 * Mapping from preset names to EmptyStateType
 */
const emptyStateTypeMap = {
  accounts: 'account' as const,
  transactions: 'transaction' as const,
  'no-data': 'no-data' as const,
  'trading-data': 'trading-data' as const,
};

/**
 * Main composable for handling loading, error, and empty states
 *
 * @param status - Query status from useQuery
 * @param data - Data from the query
 * @param isFetching - Whether currently fetching
 * @param isError - Whether there's an error
 * @param error - Error object (optional)
 * @param emptyType - Type of empty state ('accounts', 'transactions', 'data')
 * @returns Object with all state management properties
 */
export function useDataState(
  status: Ref<QueryStatus>,
  data: Ref<any>,
  isFetching: Ref<boolean>,
  isError: Ref<boolean>,
  error?: Ref<any>,
  emptyType: keyof typeof emptyStateTypeMap = 'no-data'
): DataStateResult {
  const isLoading = computed(() => {
    return status.value === 'pending' || isFetching.value;
  });

  const hasError = computed(() => {
    return status.value === 'error' || isError.value;
  });

  const hasNoData = computed(() => {
    if (status.value !== 'success') return false;

    const dataValue = data.value;
    if (Array.isArray(dataValue)) return dataValue.length === 0;
    if (typeof dataValue === 'object' && dataValue !== null)
      return Object.keys(dataValue).length === 0;
    return !dataValue;
  });

  const hasData = computed(() => {
    return status.value === 'success' && !hasNoData.value;
  });

  const isRefetching = computed(() => {
    return status.value === 'success' && isFetching.value;
  });

  const errorInfo = computed(() => {
    if (!hasError.value || !error?.value) return undefined;
    return analyzeError(error.value);
  });

  const errorConfig = computed(() => {
    return getErrorConfig(errorInfo.value);
  });

  const emptyConfig = computed(() => {
    const emptyStateType = emptyStateTypeMap[emptyType];
    const config = getEmptyStateConfig(emptyStateType);
    return {
      type: emptyStateType,
      title: config.title,
      message: config.message,
      image: config.image,
      alt: config.alt,
    };
  });

  return {
    isLoading,
    hasError,
    hasNoData,
    hasData,
    isRefetching,
    errorInfo,
    errorConfig,
    emptyConfig,
  };
}
