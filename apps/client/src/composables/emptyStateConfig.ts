/**
 * Centralized empty state configurations
 * Single source of truth for all empty state types
 */

export interface EmptyStateConfig {
  image: string;
  title: string;
  message: string;
  alt: string;
}

export type EmptyStateType =
  | 'no-data'
  | 'transaction'
  | 'account'
  | 'trading-data'
  | 'custom';

/**
 * Predefined empty state configurations
 * Used by both EmptyState.vue and useDataState.ts
 */
export const emptyStateConfigs: Record<EmptyStateType, EmptyStateConfig> = {
  'no-data': {
    image: 'empty-state.svg',
    title: 'No Trading Data Available Yet',
    message: 'Your reports will appear once trading begins.',
    alt: 'No data illustration',
  },

  transaction: {
    image: 'transaction-empty-state.svg',
    title: 'No Transactions Found',
    message: "You haven't made any transactions yet.",
    alt: 'Transaction illustration',
  },

  account: {
    image: 'account-empty-state.svg',
    title: 'No Accounts Yet',
    message: 'Start your trading journey by creating your first account.',
    alt: 'Account illustration',
  },

  'trading-data': {
    image: 'empty-state.svg',
    title: 'No Trading Data Available Yet',
    message: 'Your reports will appear once trading begins.',
    alt: 'Trading data illustration',
  },

  custom: {
    image: 'empty-state.svg',
    title: '',
    message: '',
    alt: 'Custom illustration',
  },
};

/**
 * Get empty state configuration by type
 */
export function getEmptyStateConfig(type: EmptyStateType): EmptyStateConfig {
  return emptyStateConfigs[type] || emptyStateConfigs['no-data'];
}
