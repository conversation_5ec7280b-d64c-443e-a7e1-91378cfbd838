<template>
  <div
    :dir="textDirection"
    class="flex h-screen w-full"
    :class="{ 'flex-row-reverse': isRtl }"
  >
    <SideBar />

    <div
      class="flex flex-col flex-1 overflow-x-hidden"
      :class="{ 'mr-[240px]': isRtl, 'ml-[240px]': !isRtl }"
    >
      <Header />

      <main class="flex-1 p-6 mt-16 bg-lightMood-1 pl-[1.75rem] pr-32">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from '@/layouts/Header.vue';
import SideBar from '@/layouts/SideBar.vue';

import { useLocale } from '@/composables/useLocale';

const { isRtl, textDirection } = useLocale();
</script>
