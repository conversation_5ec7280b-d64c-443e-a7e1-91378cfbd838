<template>
  <div class="sidebar-section">
    <!-- Loading State -->
    <div v-if="isLoading" class="sidebar-section__loading">
      <div
        v-for="i in 3"
        :key="`skeleton-${i}`"
        class="sidebar-section__skeleton"
      />
    </div>

    <!-- Menu Items -->
    <ul v-else class="sidebar-section__list" role="menu">
      <template
        v-for="(item, index) in visibleItems"
        :key="getItemKey(item, index)"
      >
        <!-- Divider -->
        <li v-if="isDivider(item)" class="sidebar-section__divider" role="separator" />

        <!-- Menu Item -->
        <SidebarMenuItem
          v-else
          :item="item"
          @navigate="handleNavigate"
          @keydown="handleKeydown"
        />
      </template>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { MenuItemOrDivider, MenuItem } from '../types/sidebar';
import { isDivider, isMenuItem } from '../types/sidebar';
import { getVisibleMenuItems } from '../config/menuConfig';
import SidebarMenuItem from './SidebarMenuItem.vue';

interface Props {
  items: MenuItemOrDivider[];
  isLoading?: boolean;
}

interface Emits {
  (e: 'navigate', item: MenuItem): void;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
});

const emit = defineEmits<Emits>();

// Computed
const visibleItems = computed(() => getVisibleMenuItems(props.items));

// Methods
const getItemKey = (item: MenuItemOrDivider, index: number): string => {
  if (isDivider(item)) {
    return item.id || `divider-${index}`;
  }
  return item.id || item.to || `item-${index}`;
};

const handleNavigate = (item: MenuItem) => {
  emit('navigate', item);
};

const handleKeydown = (event: KeyboardEvent, item: MenuItem) => {
  // Handle keyboard navigation
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    handleNavigate(item);
  }
};
</script>

<style scoped>
.sidebar-section {
  @apply w-full;
}

.sidebar-section__loading {
  @apply space-y-3;
}

.sidebar-section__skeleton {
  @apply h-10 bg-neutral-12 rounded-lg animate-pulse;
}

.sidebar-section__list {
  @apply space-y-1;
}

.sidebar-section__divider {
  @apply border-b border-neutral-12 my-3 mx-2;
}
</style>
