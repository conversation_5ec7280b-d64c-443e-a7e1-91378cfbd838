<template>
  <li class="sidebar-menu-item" role="none">
    <router-link
      v-if="item.to && !item.disabled"
      :to="item.to"
      custom
      v-slot="{ isActive, navigate }"
    >
      <button
        :class="menuItemClasses(isActive)"
        :aria-current="isActive ? 'page' : undefined"
        :aria-disabled="item.disabled"
        :aria-label="item.label"
        role="menuitem"
        tabindex="0"
        @click="handleClick(navigate)"
        @keydown="handleKeydown"
      >
        <MenuItemContent :item="item" :is-active="isActive" />
      </button>
    </router-link>

    <!-- Disabled or no route item -->
    <div
      v-else
      :class="menuItemClasses(false, true)"
      :aria-disabled="true"
      :aria-label="item.label"
      role="menuitem"
      tabindex="-1"
    >
      <MenuItemContent :item="item" :is-active="false" :is-disabled="true" />
    </div>
  </li>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useLocale } from '@/composables/useLocale';
import type { MenuItem } from '../types/sidebar';
import MenuItemContent from './MenuItemContent.vue';

interface Props {
  item: MenuItem;
}

interface Emits {
  (e: 'navigate', item: MenuItem): void;
  (e: 'keydown', event: KeyboardEvent, item: MenuItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { isRtl } = useLocale();

// Computed
const menuItemClasses = computed(() => (isActive: boolean, isDisabled = false) => [
  'sidebar-menu-item__button',
  {
    'sidebar-menu-item__button--active': isActive,
    'sidebar-menu-item__button--disabled': isDisabled || props.item.disabled,
    'sidebar-menu-item__button--rtl': isRtl.value,
  },
]);

// Methods
const handleClick = (navigate?: () => void) => {
  if (props.item.disabled) return;
  
  if (navigate) {
    navigate();
  }
  
  emit('navigate', props.item);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event, props.item);
};
</script>

<style scoped>
.sidebar-menu-item {
  @apply w-full;
}

.sidebar-menu-item__button {
  @apply w-full px-3 py-2.5 text-neutral-5 rounded-lg transition-all duration-200 cursor-pointer;
  @apply hover:text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/20;
  @apply flex items-center text-left;
}

.sidebar-menu-item__button--active {
  @apply bg-primary/15 text-primary;
}

.sidebar-menu-item__button--disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

.sidebar-menu-item__button--rtl {
  @apply text-right;
}

.sidebar-menu-item__button:focus-visible {
  @apply ring-2 ring-primary/30 ring-offset-2;
}
</style>
