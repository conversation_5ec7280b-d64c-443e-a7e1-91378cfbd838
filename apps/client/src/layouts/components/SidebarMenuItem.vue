<template>
  <li class="w-full" role="none">
    <router-link
      v-if="item.to && !item.disabled"
      :to="item.to"
      custom
      v-slot="{ isActive, navigate }"
    >
      <button
        :class="getMenuItemClasses(isActive, false)"
        :aria-current="isActive ? 'page' : undefined"
        :aria-disabled="item.disabled"
        :aria-label="item.label"
        role="menuitem"
        tabindex="0"
        @click="handleClick(navigate)"
        @keydown="handleKeydown"
      >
        <MenuItemContent :item="item" :is-active="isActive" />
      </button>
    </router-link>

    <!-- Disabled or no route item -->
    <div
      v-else
      :class="getMenuItemClasses(false, true)"
      :aria-disabled="true"
      :aria-label="item.label"
      role="menuitem"
      tabindex="-1"
    >
      <MenuItemContent :item="item" :is-active="false" :is-disabled="true" />
    </div>
  </li>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useLocale } from '@/composables/useLocale';
import type { MenuItem } from '../types/sidebar';
import MenuItemContent from './MenuItemContent.vue';

interface Props {
  item: MenuItem;
}

interface Emits {
  (e: 'navigate', item: MenuItem): void;
  (e: 'keydown', event: KeyboardEvent, item: MenuItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { isRtl } = useLocale();

// Methods
const getMenuItemClasses = (isActive: boolean, isDisabled = false) => {
  const baseClasses = 'w-full px-3 py-2.5 text-neutral-5 rounded-lg transition-all duration-200 cursor-pointer hover:text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/20 flex items-center focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2';
  const activeClasses = isActive ? 'bg-primary/15 text-primary' : '';
  const disabledClasses = (isDisabled || props.item.disabled) ? 'opacity-50 cursor-not-allowed pointer-events-none' : '';
  const rtlClasses = isRtl.value ? 'text-right' : 'text-left';

  return `${baseClasses} ${activeClasses} ${disabledClasses} ${rtlClasses}`.trim();
};

const handleClick = (navigate?: () => void) => {
  if (props.item.disabled) return;

  if (navigate) {
    navigate();
  }

  emit('navigate', props.item);
};

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event, props.item);
};
</script>
