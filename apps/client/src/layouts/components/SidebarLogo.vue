<template>
  <div class="mb-8">
    <router-link
      to="/"
      class="flex items-center justify-center gap-3 px-1 transition-opacity duration-200 hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
      aria-label="Go to homepage"
    >
      <img
        src="@/assets/images/logo.svg"
        alt="Etirda Logo"
        class="flex-shrink-0"
        width="50"
        height="49"
      />
      <span class="text-xl font-semibold text-neutral-1">Etirda</span>
    </router-link>
  </div>
</template>

<script setup lang="ts">
// No props needed for this simple component
</script>
