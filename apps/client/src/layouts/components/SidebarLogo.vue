<template>
  <div class="sidebar-logo">
    <router-link
      to="/"
      class="sidebar-logo__link"
      aria-label="Go to homepage"
    >
      <img
        src="@/assets/images/logo.svg"
        alt="Etirda Logo"
        class="sidebar-logo__image"
        width="50"
        height="49"
      />
      <span class="sidebar-logo__text">Etirda</span>
    </router-link>
  </div>
</template>

<script setup lang="ts">
// No props needed for this simple component
</script>

<style scoped>
.sidebar-logo {
  @apply mb-8;
}

.sidebar-logo__link {
  @apply flex items-center justify-center gap-3 px-1 transition-opacity duration-200 hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg;
}

.sidebar-logo__image {
  @apply flex-shrink-0;
}

.sidebar-logo__text {
  @apply text-xl font-semibold text-neutral-1;
}
</style>
