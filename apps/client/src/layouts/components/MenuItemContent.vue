<template>
  <div class="flex items-center w-full">
    <!-- Icon -->
    <div :class="getIconWrapperClasses()">
      <component
        v-if="item.icon"
        :is="item.icon"
        :size="iconSize"
        :class="getIconColorClasses()"
      />
    </div>

    <!-- Label -->
    <span :class="getLabelClasses()">
      {{ item.label }}
    </span>

    <!-- Badge (if present) -->
    <div
      v-if="item.badge"
      class="ml-auto flex-shrink-0"
    >
      <span :class="getBadgeClasses()">
        {{ item.badge }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useLocale } from '@/composables/useLocale';
import type { MenuItem } from '../types/sidebar';

interface Props {
  item: MenuItem;
  isActive?: boolean;
  isDisabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  isDisabled: false,
});

const { isRtl } = useLocale();

// Constants
const iconSize = 20;

// Methods
const getIconWrapperClasses = () => {
  const baseClasses = 'flex-shrink-0';
  const spacingClasses = isRtl.value ? 'ml-3' : 'mr-3';
  return `${baseClasses} ${spacingClasses}`;
};

const getIconColorClasses = () => {
  const baseClasses = 'transition-colors duration-200';
  let colorClasses = '';

  if (props.isActive) {
    colorClasses = 'text-primary';
  } else if (props.isDisabled) {
    colorClasses = 'text-neutral-8';
  } else {
    colorClasses = 'text-neutral-5';
  }

  return `${baseClasses} ${colorClasses}`;
};

const getLabelClasses = () => {
  const baseClasses = 'text-base font-medium transition-colors duration-200 flex-1';
  let colorClasses = '';
  let alignClasses = '';

  if (props.isActive) {
    colorClasses = 'text-primary';
  } else if (props.isDisabled) {
    colorClasses = 'text-neutral-8';
  }

  if (isRtl.value) {
    alignClasses = 'text-right';
  }

  return `${baseClasses} ${colorClasses} ${alignClasses}`.trim();
};

const getBadgeClasses = () => {
  const baseClasses = 'text-xs font-medium px-2 py-1 rounded-full transition-colors duration-200';
  const colorClasses = props.isActive
    ? 'bg-primary/20 text-primary'
    : 'bg-neutral-12 text-neutral-5';

  return `${baseClasses} ${colorClasses}`;
};
</script>
