import type { MenuItem, MenuItemOrDivider } from '../types/sidebar';

interface MenuConfigParams {
  t: (key: string) => string;
  firstActiveAccount: any;
}

export const createMenuConfig = ({ t, firstActiveAccount }: MenuConfigParams) => {
  const mainMenuItems: MenuItemOrDivider[] = [
    // Account Management Section
    {
      id: 'accounts',
      to: '/',
      icon: 'DaryaAccountsIcon',
      label: t('common.accounts'),
    },
    {
      id: 'account-overview',
      to: firstActiveAccount
        ? `/account/${firstActiveAccount.id}/account-overview`
        : '',
      icon: 'DaryaOutlineCategoryIcon',
      label: t('sidebar.account_overview'),
      condition: !!firstActiveAccount,
      disabled: !firstActiveAccount,
    },
    {
      id: 'trading-overview',
      to: firstActiveAccount
        ? `/account/${firstActiveAccount.id}/trading-overview`
        : '',
      icon: 'DaryaOutlineCandleIcon',
      label: t('sidebar.trading_overview'),
      condition: !!firstActiveAccount,
      disabled: !firstActiveAccount,
    },

    // Financial Section
    {
      id: 'transactions',
      to: '/transactions',
      icon: 'DaryaOutlineBitcoinConvertIcon',
      label: t('sidebar.transactions'),
    },
    {
      id: 'withdrawal',
      to: '/withdraw',
      icon: 'DaryaOutlineCardIcon',
      label: t('sidebar.withdrawal'),
    },

    // Divider
    { divider: true, id: 'divider-1' },

    // Competition Section
    {
      id: 'challenges',
      to: '/challenges',
      icon: 'DaryaOutlineRankingIcon',
      label: t('sidebar.challenges'),
    },
    {
      id: 'competition',
      to: '/competition',
      icon: 'DaryaOutlineCupIcon',
      label: t('sidebar.competition'),
    },
    {
      id: 'certifications',
      to: '/certifications',
      icon: 'DaryaOutlineMedalStarIcon',
      label: t('sidebar.certifications'),
    },

    // Divider
    { divider: true, id: 'divider-2' },

    // Tools Section
    {
      id: 'tools',
      to: '/tools',
      icon: 'DaryaOutlineColorSwatchIcon',
      label: t('sidebar.tools'),
    },
    {
      id: 'calculator',
      to: '/calculator',
      icon: 'DaryaOutlineCalculatorIcon',
      label: t('sidebar.calculator'),
    },
  ];

  const bottomMenuItems: MenuItemOrDivider[] = [
    {
      id: 'settings',
      to: '/setting',
      icon: 'DaryaOutlineSetting2Icon',
      label: t('sidebar.setting'),
    },
    {
      id: 'faq',
      to: '/faq',
      icon: 'DaryaOutlineMessageQuestionIcon',
      label: t('sidebar.faq'),
    },
  ];

  return {
    mainMenuItems,
    bottomMenuItems,
  };
};

// Menu item validation
export const validateMenuItem = (item: MenuItem): boolean => {
  if (!item.label) return false;
  if (item.to && typeof item.to !== 'string') return false;
  if (item.icon && typeof item.icon !== 'string') return false;
  return true;
};

// Filter visible menu items
export const getVisibleMenuItems = (items: MenuItemOrDivider[]): MenuItemOrDivider[] => {
  return items.filter((item) => {
    if ('divider' in item) return true;
    return item.condition !== false;
  });
};
