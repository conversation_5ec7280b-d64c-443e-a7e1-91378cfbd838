export interface MenuItem {
  id?: string;
  to?: string;
  icon?: string;
  label: string;
  condition?: boolean;
  disabled?: boolean;
  badge?: string | number;
  children?: MenuItem[];
  external?: boolean;
  target?: '_blank' | '_self';
}

export interface MenuDivider {
  divider: true;
  id?: string;
}

export type MenuItemOrDivider = MenuItem | MenuDivider;

export interface MenuSection {
  id: string;
  title?: string;
  items: MenuItemOrDivider[];
}

export interface SidebarConfig {
  mainSections: MenuSection[];
  bottomSection: MenuSection;
}

export interface NavigationEvent {
  item: MenuItem;
  event: Event;
}

// Type guards
export const isMenuItem = (item: MenuItemOrDivider): item is MenuItem => {
  return !('divider' in item);
};

export const isDivider = (item: MenuItemOrDivider): item is MenuDivider => {
  return 'divider' in item && item.divider === true;
};

// Menu item states
export interface MenuItemState {
  isActive: boolean;
  isDisabled: boolean;
  isLoading?: boolean;
}

// Accessibility props
export interface MenuItemAccessibility {
  role: string;
  'aria-current'?: 'page' | 'step' | 'location' | 'date' | 'time' | 'true' | 'false';
  'aria-disabled'?: boolean;
  'aria-label'?: string;
  'aria-describedby'?: string;
  tabindex?: number;
}
