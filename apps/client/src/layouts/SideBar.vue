<template>
  <aside
    class="bg-lightMood-2 fixed top-0 w-[240px] z-50 h-full py-5 px-3"
    :class="{ 'right-0': isRtl, 'left-0': !isRtl }"
  >
    <div class="flex flex-col w-full h-full">
      <div class="flex items-center justify-center gap-3 px-1 mb-8">
        <img
          src="@/assets/images/logo.svg"
          alt="Logo"
          width="50"
          height="49"
          aria-label="Application Logo"
        />
        <span class="text-xl">Etirda</span>
      </div>

      <nav class="flex flex-col w-full flex-1">
        <ul class="space-y-3">
          <template v-for="(item, index) in menuItems" :key="item.to || `divider-${index}`">
            <template v-if="item.divider">
              <div class="border-b border-neutral-12 my-2"></div>
            </template>
            <template v-else>
              <router-link
                :to="item.to"
                custom
                v-slot="{ isActive, navigate }"
                v-if="!item.condition || item.condition"
              >
                <li
                  @click="navigate"
                  role="menuitem"
                  class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary hover:bg-transparent"
                  :class="{
                    'bg-primary/20': isActive,
                    'bg-transparent': !isActive,
                  }"
                >
                  <div class="flex items-center">
                    <component :is="item.icon" />
                    <span
                      class="text-base font-medium"
                      :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                    >
                      {{ item.label }}
                    </span>
                  </div>
                </li>
              </router-link>
            </template>
          </template>
        </ul>
      </nav>

      <div class="mt-auto">
        <ul>
          <template v-for="item in bottomMenuItems" :key="item.to">
            <router-link :to="item.to" custom v-slot="{ isActive, navigate }">
              <li
                @click="navigate"
                role="menuitem"
                class="px-1 py-2 text-neutral-5 rounded-lg transition-colors duration-200 cursor-pointer hover:text-primary hover:bg-transparent"
                :class="{
                  'bg-primary/20': isActive,
                  'bg-transparent': !isActive,
                }"
              >
                <div class="flex items-center">
                  <component :is="item.icon" />
                  <span
                    class="text-base font-medium"
                    :class="{ 'mr-3': isRtl, 'ml-3': !isRtl }"
                  >
                    {{ item.label }}
                  </span>
                </div>
              </li>
            </router-link>
          </template>
        </ul>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useLocale } from '@/composables/useLocale';
import { useGetAllAccountByUserId } from '@/modules/account';
import { useAuth } from '@/modules/user';

const { user } = useAuth();
const { allAccountData } = useGetAllAccountByUserId(
  user.value?.nameid ?? '',
  true
);

const firstActiveAccount = computed(() => {
  return (
    allAccountData.value?.find((account) => String(account.status) === '1') ??
    null
  );
});

const { t } = useI18n();
const { isRtl } = useLocale();

const menuItems = computed(() => [
  {
    to: '/',
    icon: 'DaryaAccountsIcon',
    label: t('common.accounts'),
  },
  {
    to: firstActiveAccount.value
      ? `/account/${firstActiveAccount.value.id}/account-overview`
      : '',
    icon: 'DaryaOutlineCategoryIcon',
    label: t('sidebar.account_overview'),
    condition: !!firstActiveAccount.value,
  },
  {
    to: firstActiveAccount.value
      ? `/account/${firstActiveAccount.value.id}/trading-overview`
      : '',
    icon: 'DaryaOutlineCandleIcon',
    label: t('sidebar.trading_overview'),
    condition: !!firstActiveAccount.value,
  },
  {
    to: '/transactions',
    icon: 'DaryaOutlineBitcoinConvertIcon',
    label: t('sidebar.transactions'),
  },
  {
    to: '/withdraw',
    icon: 'DaryaOutlineCardIcon',
    label: t('sidebar.withdrawal'),
  },
  { divider: true },
  {
    to: '/challenges',
    icon: 'DaryaOutlineRankingIcon',
    label: t('sidebar.challenges'),
  },
  {
    to: '/competition',
    icon: 'DaryaOutlineCupIcon',
    label: t('sidebar.competition'),
  },
  {
    to: '/certifications',
    icon: 'DaryaOutlineMedalStarIcon',
    label: t('sidebar.certifications'),
  },
  { divider: true },
  {
    to: '/tools',
    icon: 'DaryaOutlineColorSwatchIcon',
    label: t('sidebar.tools'),
  },
  {
    to: '/calculator',
    icon: 'DaryaOutlineCalculatorIcon',
    label: t('sidebar.calculator'),
  },
]);

const bottomMenuItems = [
  {
    to: '/setting',
    icon: 'DaryaOutlineSetting2Icon',
    label: t('sidebar.setting'),
  },
  {
    to: '/faq',
    icon: 'DaryaOutlineMessageQuestionIcon',
    label: t('sidebar.faq'),
  },
];
</script>
