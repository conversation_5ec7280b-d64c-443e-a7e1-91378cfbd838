<template>
  <aside
    class="sidebar"
    :class="sidebarClasses"
    role="navigation"
    aria-label="Main navigation"
  >
    <div class="sidebar__container">
      <!-- Logo Section -->
      <SidebarLogo />

      <!-- Main Navigation -->
      <nav class="sidebar__nav" role="menubar">
        <SidebarSection
          :items="mainMenuItems"
          :is-loading="isAccountDataLoading"
          @navigate="handleNavigation"
        />
      </nav>

      <!-- Bottom Navigation -->
      <div class="sidebar__bottom">
        <SidebarSection
          :items="bottomMenuItems"
          @navigate="handleNavigation"
        />
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useLocale } from '@/composables/useLocale';
import { useGetAllAccountByUserId } from '@/modules/account';
import { useAuth } from '@/modules/user';
import SidebarLogo from './components/SidebarLogo.vue';
import SidebarSection from './components/SidebarSection.vue';
import { createMenuConfig } from './config/menuConfig';
import type { MenuItem } from './types/sidebar';

// Composables
const { t } = useI18n();
const { isRtl } = useLocale();
const router = useRouter();
const { user } = useAuth();

// Account data
const { allAccountData, isFetching: isAccountDataLoading } = useGetAllAccountByUserId(
  user.value?.nameid ?? '',
  true
);

const firstActiveAccount = computed(() => {
  return (
    allAccountData.value?.find((account) => String(account.status) === '1') ??
    null
  );
});

// Menu configuration
const mainMenuItems = computed(() => {
  const { mainMenuItems } = createMenuConfig({
    t,
    firstActiveAccount: firstActiveAccount.value,
  });
  return mainMenuItems;
});

const bottomMenuItems = computed(() => {
  const { bottomMenuItems } = createMenuConfig({
    t,
    firstActiveAccount: firstActiveAccount.value,
  });
  return bottomMenuItems;
});

// Computed properties
const sidebarClasses = computed(() => ({
  'sidebar--rtl': isRtl.value,
  'sidebar--ltr': !isRtl.value,
}));

// Event handlers
const handleNavigation = (item: MenuItem) => {
  if (item.to && item.to !== router.currentRoute.value.path) {
    router.push(item.to);
  }
};
</script>

<style scoped>
.sidebar {
  @apply bg-lightMood-2 fixed top-0 w-[240px] z-50 h-full py-5 px-3;
}

.sidebar--rtl {
  @apply right-0;
}

.sidebar--ltr {
  @apply left-0;
}

.sidebar__container {
  @apply flex flex-col w-full h-full;
}

.sidebar__nav {
  @apply flex flex-col w-full flex-1;
}

.sidebar__bottom {
  @apply mt-auto;
}
</style>
