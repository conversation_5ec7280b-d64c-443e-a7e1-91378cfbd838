<template>
  <header
    class="bg-lightMood-1 fixed top-0 left-[15rem] right-0 h-16 flex items-center justify-end border-b border-gray-200 px-8 z-50 mb-8"
  >
    <AccountDropdown v-if="isShowAccountDropdown" :user-id="user?.nameid" />

    <div class="h-6 border-l border-gray-200 mx-1"></div>

    <div
      aria-label="Notifications"
      class="space-x-2 rounded-full hover:bg-gray-100 transition"
    >
      <DaryaNotificationIcon />
    </div>

    <div class="h-6 border-r border-gray-200 mx-1"></div>
    <DPopper
      v-model="isShowUserDropdown"
      placement="bottom-end"
      :arrow="false"
      offset-y="6"
      append-to-body
      @click="isShowUserDropdown = !isShowUserDropdown"
    >
      <template #default>
        <div class="flex items-center ml-6 space-x-2 cursor-pointer">
          <img
            src="@/assets/images/avatar.svg"
            alt="User avatar"
            class="w-8 h-8 rounded-full object-cover"
          />
          <span class="text-sm font-medium text-gray-700">
            {{ userName }}
          </span>
          <DaryaArrowDownIcon size="16" class="text-gray-500 ml-6" />
        </div>
      </template>

      <template #content>
        <div
          class="p-2 my-4 bg-white shadow-lg border border-gray-100 rounded-lg w-72"
        >
          <div class="flex flex-col items-center pb-4">
            <img
              src="@/assets/images/avatar.svg"
              alt="User avatar"
              class="w-16 h-16 rounded-full object-cover"
            />
            <span class="text-sm font-medium pt-1">{{ userName }}</span>
          </div>
          <ul class="border-b border-t border-neutral-13 py-4">
            <li
              class="flex items-center rounded-2xl px-2 py-3 text-neutral-3 hover:bg-primary/15 cursor-pointer mb-3"
              @click="navigateToProfile"
            >
              <DaryaOutlineFrameIcon />
              <span class="text-base ps-1">Profile</span>
            </li>
            <li
              class="flex items-center rounded-2xl px-2 py-3 hover:bg-primary/15 text-neutral-3 cursor-pointer"
              @click="handleLogout"
            >
              <DaryaOutlineLogoutIcon />
              <span class="text-base ps-1">Sign out</span>
            </li>
          </ul>
          <div class="flex justify-center pt-4 text-sm text-neutral-7">
            <span>Start Date</span>
            <span class="font-bold ms-0.5">Apr 15, 2025</span>
          </div>
        </div>
      </template>
    </DPopper>
  </header>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useAuth, useUserLogoutMutation } from '@/modules/user';
import { AccountDropdown } from '@/modules/account';

const route = useRoute();
const router = useRouter();

const { user, clearAuth } = useAuth();

const SHOW_DROPDOWN_ROUTES = computed(() => [
  `/account/${route.params.id}/trading-overview`,
  `/account/${route.params.id}/account-overview`,
]);
const isShowAccountDropdown = computed(() =>
  SHOW_DROPDOWN_ROUTES.value.includes(route.path)
);
const isShowUserDropdown = ref(false);

const { logout } = useUserLogoutMutation();

const userName = computed(
  () => `${user.value?.given_name} ${user.value?.family_name}`
);
const handleLogout = async (): Promise<void> => {
  try {
    await logout();
  } catch (error) {
    // Handle error if needed
  } finally {
    clearAuth();
    isShowUserDropdown.value = false;
  }
};

const navigateToProfile = () => {
  isShowUserDropdown.value = false;
  router.replace('/profile');
};
</script>
