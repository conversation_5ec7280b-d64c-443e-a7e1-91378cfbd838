<template>
  <h2 class="text-lg font-medium mb-4 text-blue-500">Economic Calendar</h2>
  <DBox class="flex justify-between">
    <div v-for="item in calendar" class="px-3 py-4">
      <DButton
        :label="item.label"
        :icon="item.icon"
        styleType="secondary"
        variant="fill"
      >
      </DButton>
    </div>
  </DBox>

  <div class="flex items-center justify-between mt-3">
    <div class="flex flex-col">
      <span class="text-sm">Timezone</span>
      <div class="flex text-sm items-center mt-2">
        <DSearchBox class="min-w-86" />
        <DCheckbox v-model="isChecked" label="See as MT4 Time" class="ms-3" />
      </div>
    </div>

    <DBox class="px-2 py-1">
      <span class="text-sm">
        Source:
        <span class="text-sm font-medium">forexfactory.com</span>
      </span>
    </DBox>
  </div>

  <div class="flex">
    <div class="flex justify-between mt-4 w-1/4 pe-4 pt-2">
      <div>
        <span class="text-sm font-medium">Filter by Impact</span>

        <DCheckboxGroup
          v-model="selectedOptions"
          label="Impact"
          class="border-b-2 py-2"
        >
          <DCheckbox
            v-for="fruit in impact"
            :key="fruit"
            :value="fruit"
            :label="fruit"
          />
        </DCheckboxGroup>

        <DCheckboxGroup
          v-model="selectedOptions"
          label="Restrictions"
          class="border-b-2 pb-2"
        >
          <DCheckbox
            v-for="fruit in restrictions"
            :key="fruit"
            :value="fruit"
            :label="fruit"
          />
        </DCheckboxGroup>

        <DCheckboxGroup v-model="selectedOptions" label="Days">
          <DCheckbox
            v-for="fruit in days"
            :key="fruit"
            :value="fruit"
            :label="fruit"
          />
        </DCheckboxGroup>
      </div>

      <div>
        <a
          href="#"
          @click.prevent="clearAllFilters"
          class="text-sm text-blue-500"
          >Clear all</a
        >
      </div>
    </div>

    <DBaseTable
      :columns="columns"
      :data="tableData"
      class="mt-4"
      :rowsPerPage="8"
      :enable-pagination="true"
    >
      <template #column-currency="{ row }">
        <div class="inline-flex items-center">
          <component :is="row.icon" />
          <span class="text-sm ps-1.5">{{ row.currency }}</span>
        </div>
      </template>
      <template #column-action="{ row }">
        <DButton label="Add to Calendar" />
      </template>
    </DBaseTable>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const isChecked = ref(true);

const calendar = [
  { icon: 'DaryaOutlineGlobalIcon', label: 'ALL CURRENCY' },
  { icon: 'DaryaEnglandIcon', label: 'NZD' },
  { icon: 'DaryaIndiaIcon', label: 'AUD' },
  { icon: 'DaryaSouthAfricaIcon', label: 'CAD' },
  { icon: 'DaryaIndiaIcon', label: 'EUR' },
  { icon: 'DaryaEnglandIcon', label: 'GBP' },
  { icon: 'DaryaSouthAfricaIcon', label: 'USD' },
  { icon: 'DaryaIndiaIcon', label: 'CHF' },
  { icon: 'DaryaEnglandIcon', label: 'JPY' },
  { icon: 'DaryaSouthAfricaIcon', label: 'CNY' },
];

const columns = ref([
  { key: 'time', label: 'Time and Date' },
  { key: 'currency', label: 'Currency' },
  { key: 'impact', label: 'Impact' },
  { key: 'event', label: 'Event' },
  { key: 'forecast', label: 'Forecast' },
  { key: 'previous', label: 'Previous' },
  { key: 'action', label: 'Action' },
]);
const tableData = ref([
  {
    time: 'July 11 12:23',
    currency: 'AUD',
    impact: 'High',
    event: 'BOE Gov Bailey Speaks',
    forecast: '3.5%',
    previous: '3.4%',
    action: '',
    icon: 'DaryaEnglandIcon',
  },
  {
    time: 'July 11 2:50',
    currency: 'AUD',
    impact: 'Low',
    event: 'Bank Lending y/y',
    forecast: '1.87T',
    previous: '1.90T',
    action: '',
    icon: 'DaryaSouthAfricaIcon',
  },
  {
    time: 'July 11 4:16',
    currency: 'CHF',
    impact: 'Low',
    event: 'Economy Watchers Sentiment',
    forecast: '0.2%',
    previous: '0.2%',
    action: '',
    icon: 'DaryaIndiaIcon',
  },
  {
    time: 'July 11 4:33',
    currency: 'AUD',
    impact: 'Medium',
    event: 'CPI y/y',
    forecast: '-5.0%',
    previous: '-4.6%',
    action: '',
    icon: 'DaryaIndiaIcon',
  },
  {
    time: 'July 10 1:33',
    currency: 'CHF',
    impact: 'Low',
    event: 'Sentix Investor Confidence',
    forecast: '54.8',
    previous: '55.0',
    action: '',
    icon: 'DaryaSouthAfricaIcon',
  },
  {
    time: 'July 10 3:30',
    currency: 'CHF',
    impact: 'Low',
    event: 'Building Permits m/m',
    forecast: '74.0B',
    previous: '65.8B',
    action: '',
    icon: 'DaryaEnglandIcon',
  },
  {
    time: 'July 10 8:30',
    currency: 'CHF',
    impact: 'Low',
    event: 'PPI y/y',
    forecast: '0.2%',
    previous: '452B',
    action: '',
    icon: 'DaryaSouthAfricaIcon',
  },
  {
    time: 'July 9 7:30',
    currency: 'CHF',
    impact: 'Low',
    event: 'PPI m/m',
    forecast: '0.2%',
    previous: '-0.3%',
    action: '',
    icon: 'DaryaEnglandIcon',
  },
]);

const selectedOptions = ref<string[]>(['Holiday']);

const impact = ['Low', 'Medium', 'High', 'Holiday'];
const restrictions = ['Restrictions', 'No Restrictions'];
const days = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];

const clearAllFilters = () => {
  selectedOptions.value = [];
};
</script>
