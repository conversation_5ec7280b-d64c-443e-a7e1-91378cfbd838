<template>
  <div class="mb-6">
    <h1 class="text-3xl font-bold text-neutral-2">Trading Calculators</h1>
    <p class="text-neutral-6 mt-2">
      Use our trading calculators to analyze your forex positions and make
      informed trading decisions.
    </p>
  </div>

  <!-- Calculator Data with State Management -->
  <DataStateWrapper
    :isLoading="calculatorState.isLoading"
    :hasError="calculatorState.hasError"
    :hasNoData="calculatorState.hasNoData"
    :isRefetching="calculatorState.isRefetching"
    :errorConfig="calculatorState.errorConfig"
    :emptyConfig="calculatorState.emptyConfig"
    :onRetry="retryCalculatorData"
  >
    <DTab :tabs="calculatorTabs" v-model="activeTab" class="w-full">
      <template #currency-converter>
        <div class="mt-6">
          <CurrencyConverter :exchangeData="exchangeDataList" />
        </div>
      </template>

      <template #pip-value>
        <div class="mt-6">
          <PipValueCalculator
            :currencyPairs="exchangePairsList"
            :exchangeData="exchangeDataList"
          />
        </div>
      </template>

      <template #margin>
        <div class="mt-6">
          <MarginCalculator
            :currencyPairs="exchangePairsList"
            :exchangeData="exchangeDataList"
          />
        </div>
      </template>

      <template #position-size>
        <div class="mt-6">
          <PositionSizeCalculator
            :currencyPairs="exchangePairsList"
            :exchangeData="exchangeDataList"
          />
        </div>
      </template>

      <template #profit-loss>
        <div class="mt-6">
          <ProfitLossCalculator
            :currencyPairs="exchangePairsList"
            :exchangeData="exchangeDataList"
          />
        </div>
      </template>

      <template #swap>
        <div class="mt-6">
          <SwapCalculator
            :currencyPairs="exchangePairsList"
            :exchangeData="exchangeDataList"
          />
        </div>
      </template>

      <template #currency-table>
        <div class="mt-6">
          <CurrencyTable :exchangeData="exchangeDataList" />
        </div>
      </template>
    </DTab>
  </DataStateWrapper>
</template>

<script setup lang="ts">
import { ref, computed, ComputedRef } from 'vue';
import { useI18n } from 'vue-i18n';

import {
  useGetAllExchangeData,
  useGetAllExchangePairs,
  CurrencyConverter,
  CurrencyTable,
  PipValueCalculator,
  MarginCalculator,
  PositionSizeCalculator,
  ProfitLossCalculator,
  SwapCalculator,
} from '@/modules/calculator';

import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const { t } = useI18n();

// Exchange Data with state management
const {
  exchangeDataList,
  status: exchangeDataStatus,
  isFetching: exchangeDataFetching,
  isError: exchangeDataError,
  error: exchangeDataErrorObj,
  getAllExchangeData,
} = useGetAllExchangeData();

const exchangeDataState = useDataState(
  exchangeDataStatus,
  exchangeDataList,
  exchangeDataFetching,
  exchangeDataError,
  exchangeDataErrorObj,
  'no-data'
);

// Exchange Pairs with state management
const {
  exchangePairsList,
  status: exchangePairsStatus,
  isFetching: exchangePairsFetching,
  isError: exchangePairsError,
  error: exchangePairsErrorObj,
  getAllExchangePairs,
} = useGetAllExchangePairs();

const exchangePairsState = useDataState(
  exchangePairsStatus,
  exchangePairsList,
  exchangePairsFetching,
  exchangePairsError,
  exchangePairsErrorObj,
  'no-data'
);

// Combined state for calculator data (both exchange data and pairs are needed)
const calculatorState = computed(() => {
  const isLoading =
    exchangeDataState.isLoading.value || exchangePairsState.isLoading.value;
  const hasError =
    exchangeDataState.hasError.value || exchangePairsState.hasError.value;
  const isRefetching =
    exchangeDataState.isRefetching.value ||
    exchangePairsState.isRefetching.value;

  // Check if both data sources have data
  const hasExchangeData =
    exchangeDataList.value && Object.keys(exchangeDataList.value).length > 0;
  const hasExchangePairs =
    exchangePairsList.value && exchangePairsList.value.length > 0;
  const hasNoData = !hasExchangeData || !hasExchangePairs;

  return {
    isLoading,
    hasError,
    hasNoData,
    isRefetching,
    errorConfig: {
      title: 'Unable to Load Calculator Data',
      message:
        'We encountered an issue loading exchange rates and currency pairs. Please try again.',
      image: 'error.svg',
      alt: 'Error loading calculator data',
    },
    emptyConfig: {
      title: 'No Calculator Data Available',
      message: 'Exchange rates and currency pairs are currently unavailable.',
      image: 'empty-state.svg',
      alt: 'No calculator data available',
    },
  };
});

// Retry function
const retryCalculatorData = () => {
  getAllExchangeData();
  getAllExchangePairs();
};

const activeTab = ref<string>('currency-converter');

const calculatorTabs = [
  { name: 'currency-converter', label: t('calculator.tabs.currencyConverter') },
  { name: 'pip-value', label: t('calculator.tabs.pipValue') },
  { name: 'margin', label: t('calculator.tabs.margin') },
  { name: 'position-size', label: t('calculator.tabs.positionSize') },
  { name: 'profit-loss', label: t('calculator.tabs.profitLoss') },
  { name: 'swap', label: t('calculator.tabs.swap') },
  { name: 'currency-table', label: t('calculator.tabs.currencyTable') },
];
</script>
