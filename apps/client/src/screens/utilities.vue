<template>
  <div>
    <h2 class="text-lg font-medium mb-4">Utilities</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <DBox
        v-for="tool in tools"
        :key="tool.title"
        class="flex items-center justify-between p-4 shadow-sm border rounded-lg bg-white"
      >
        <div class="flex items-center gap-3">
          <component :is="tool.icon" class="text-gray-500 w-6 h-6" />

          <div>
            <h3 class="text-sm font-medium">{{ tool.title }}</h3>
            <p class="text-xs text-gray-500">{{ tool.description }}</p>
          </div>
        </div>

        <DButton
          label="Download"
          class="text-blue-600 border border-blue-500 px-3 py-1 text-sm rounded-md hover:bg-blue-100"
        />
      </DBox>
    </div>
  </div>
</template>

<script setup>
const tools = [
  {
    title: 'Profit Calculator',
    description: 'Tool’s description',
    icon: 'DaryaCalendarIcon',
  },
  {
    title: 'FundYourFx Journal',
    description: 'Tool’s description',
    icon: 'DaryaCalendarIcon',
  },
  {
    title: 'Lot Calculator',
    description: 'Tool’s description',
    icon: 'DaryaCalendarIcon',
  },
  {
    title: 'MetaTrader 4',
    description: 'Tool’s description',
    icon: 'DaryaComputerIcon',
  },
];
</script>
