<template>
  <div class="w-full">
    <h2 class="text-2xl font-semibold mb-6">Settings</h2>

    <div class="flex space-x-8 border-b border-gray-200 mb-8">
      <button
        class="flex items-center space-x-2 border-b-2 border-blue-600 text-blue-600 pb-2"
      >
        <DaryaUserLightIcon color="blue" />
        <span class="font-medium">Profile</span>
      </button>
      <button class="flex items-center space-x-2 text-gray-500 pb-2">
        <DaryaLockIcon color="gray" />
        <span>Password</span>
      </button>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-28">
      <DBox class="lg:col-span-2 p-6">
        <h3 class="text-md font-semibold mb-4 pb-2 border-b border-gray-200">
          Personal Information
        </h3>
        <VeeForm
          :initial-values="formModel"
          v-slot="{ handleSubmit, errors }"
          as="div"
        >
          <form
            class="w-full space-y-6"
            autocomplete="off"
            @submit.prevent="handleSubmit(submitForm)"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <VeeField v-slot="{ field }" name="fullName">
                <DInput
                  :modelValue="field.value"
                  @update:modelValue="field.handleChange"
                  :error="errors.fullName"
                  label="Full Name"
                  icon="DaryaUserLightIcon"
                  class="w-full"
                />
              </VeeField>

              <VeeField v-slot="{ field }" name="phoneNumber">
                <DInput
                  :modelValue="field.value"
                  @update:modelValue="field.handleChange"
                  :error="errors.phoneNumber"
                  label="Phone Number"
                  type="tel"
                  class="w-full"
                />
              </VeeField>

              <div class="col-span-full">
                <VeeField v-slot="{ field }" name="email">
                  <DInput
                    :modelValue="field.value"
                    @update:modelValue="field.handleChange"
                    :error="errors.email"
                    label="Email Address"
                    type="email"
                    icon="DaryaEnvolpeIcon"
                    class="w-full"
                  />
                </VeeField>
              </div>

              <VeeField v-slot="{ field }" name="city">
                <DInput
                  :modelValue="field.value"
                  @update:modelValue="field.handleChange"
                  :error="errors.city"
                  label="City"
                  class="w-full"
                />
              </VeeField>

              <VeeField v-slot="{ field }" name="postalCode">
                <DInput
                  :modelValue="field.value"
                  @update:modelValue="field.handleChange"
                  :error="errors.postalCode"
                  label="Postal Code"
                  type="number"
                  class="w-full"
                />
              </VeeField>
            </div>

            <div class="mt-6 flex justify-end">
              <DButton
                buttonType="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                Save Changes
              </DButton>
            </div>
          </form>
        </VeeForm>
      </DBox>
      <DBox class="bg-white p-6">
        <h3 class="text-md font-semibold mb-4 pb-2 border-b border-gray-200">
          Your Photo
        </h3>
        <div class="flex items-center space-x-4 mb-4">
          <DaryaUserLightIcon />

          <div>
            <p class="text-sm font-medium">Edit your photo</p>
            <small class="text-sm text-gray-500">
              <a href="#" class="text-red-500 mr-2">Delete</a>
              <a href="#" class="text-blue-600">Update</a>
            </small>
          </div>
        </div>

        <div
          class="border-2 border-dashed border-blue-300 p-6 text-center rounded"
        >
          <svg
            class="w-6 h-6 mx-auto mb-2 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
            />
          </svg>
          <p class="text-sm font-medium text-blue-600">Click to upload</p>
          <small class="text-xs text-gray-500">
            or drag and drop<br />
            SVG, PNG, JPG or GIF (max, 800 X 800px)
          </small>
        </div>
      </DBox>

      <div class="mt-4 ml-6">
        <button
          class="text-red-500 hover:text-red-600 underline text-sm cursor-pointer"
        >
          Deactivate Account
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

const formModel = reactive({
  fullName: 'Devid Jhon',
  phoneNumber: '+990 3343 7865',
  city: 'Los Angeles',
  email: '<EMAIL>',
  postalCode: '123456',
});

const submitForm = (values) => {
  console.log('Form submitted:', values);
};
</script>
