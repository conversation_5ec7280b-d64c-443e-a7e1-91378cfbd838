<template>
  <DTab :tabs="tabs" v-model="activeTab" class="mt-3.5">
    <template #tab2>
      <div class="grid grid-cols-2 gap-4 mt-8">
        <!-- Growth Analysis Chart -->
        <div
          class="flex flex-col py-4 px-6 border border-neutral-13 rounded-3xl"
        >
          <span class="text-base font-medium">
            {{ t('account.growth_analysis') }}
          </span>
          <div class="flex justify-between items-center mt-6">
            <div>
              <span class="text-success-shade1 text-3xl font-semibold"
                >+12.4%
              </span>
              <span class="text-neutral-3 text-sm">
                {{ t('account.total_return') }}
              </span>
            </div>
            <div
              class="flex w-64 items-center justify-between rounded-lg border border-neutral-12 bg-neutral-14 px-4 py-2 my-2"
            >
              <span class="text-sm font-normal text-neutral-9">
                {{ t('common.start_date') }}
              </span>
              <DaryaArrowSwapHorizontalIcon size="16" class="text-neutral-11" />
              <span class="text-sm font-normal text-neutral-9">
                {{ t('common.end_date') }}
              </span>
              <DaryaOutlineCalendarSearchIcon
                size="16"
                class="text-neutral-11"
              />
            </div>
          </div>
          <div class="h-[310px] mt-8">
            <canvas ref="growthChartCanvas" id="growthChart"></canvas>
          </div>
        </div>
        <!-- Win/Loss Ratio Chart -->
        <div
          class="lex flex-col py-4 px-6 border border-neutral-13 rounded-3xl"
        >
          <span class="text-base font-medium">
            {{ t('account.win_loss_ratio') }}
          </span>
          <div class="mt-6">
            <span class="text-success-shade1 text-3xl font-semibold">
              +27.27%
            </span>
            <span class="text-neutral-3 text-sm">
              {{ t('account.win_ratio') }}
            </span>
          </div>
          <div class="flex justify-center mt-6 h-[310px]">
            <canvas ref="chartCanvas"></canvas>
          </div>
        </div>

        <div
          class="flex flex-col py-4 px-6 border border-neutral-13 rounded-3xl"
        >
          <span class="text-base font-medium">
            {{ t('account.profit_loss_overview') }}
          </span>
          <div class="flex mt-6 justify-between items-center">
            <div>
              <span class="text-success-shade1 text-3xl font-semibold">
                +$920
              </span>
              <span class="text-neutral-3 text-sm">
                {{ t('account.limit_orders') }}
              </span>
            </div>

            <div class="flex text-primary-tint1 font-bold text-xs">
              <button
                class="p-2 cursor-pointer border border-primary rounded-tl-lg rounded-bl-lg"
                :class="
                  selectedChartFilter === 'Weekday' ? 'backgroundCard' : ''
                "
                @click="selectedChartFilter = 'Weekday'"
              >
                {{ t('common.Weekday') }}
              </button>
              <button
                class="border border-primary p-2 cursor-pointer"
                :class="selectedChartFilter === 'Hours' ? 'backgroundCard' : ''"
                @click="selectedChartFilter = 'Hours'"
              >
                {{ t('common.hours') }}
              </button>
              <button
                class="p-2 cursor-pointer border border-primary rounded-tr-lg rounded-br-lg"
                :class="
                  selectedChartFilter === 'Order Type' ? 'backgroundCard ' : ''
                "
                @click="selectedChartFilter = 'Order Type'"
              >
                {{ t('common.order_type') }}
              </button>
            </div>
          </div>
          <div class="h-[310px] mt-8">
            <canvas ref="profitLossChartCanvas" id="profitLossChart"></canvas>
          </div>
        </div>
        <div
          class="flex flex-col py-4 px-6 border border-neutral-13 rounded-3xl"
        >
          <span class="text-base font-medium">
            {{ t('account.instrument_performance') }}
          </span>
          <div class="flex mt-6 justify-between items-center">
            <div>
              <span class="text-success-shade1 text-3xl font-semibold">
                +37%
              </span>
              <span class="text-neutral-3 text-sm">{{
                t('account.top_symbol')
              }}</span>
              <span class="text-neutral-3 text-sm font-bold"> XAUUSD</span>
            </div>
            <DPopper
              v-model="isOpenInstrumentDropdown"
              placement="bottom"
              :arrow="false"
              offset-y="22"
              append-to-body
              @click="isOpenInstrumentDropdown = !isOpenInstrumentDropdown"
            >
              <template #default>
                <button
                  class="w-52 px-4 py-1.5 rounded-lg border border-primary-shade1"
                >
                  <div class="flex items-center justify-between text-neutral-9">
                    <span>{{ t('common.instrument') }}</span>
                    <DaryaArrowDownIcon size="16" />
                  </div>
                </button>
              </template>
              <template #content>
                <div
                  class="bg-white bg-neutral-14 shadow-md rounded-lg py-1.5 w-44"
                >
                  <div class="flex flex-col px-2">
                    <DCheckboxGroup v-model="selectedOptions">
                      <DCheckbox
                        v-for="instrument in instruments"
                        :key="instrument"
                        :value="instrument"
                        :label="instrument"
                        class="hover:border border-primary-tint2 rounded-2xl px-2 py-3"
                      />
                    </DCheckboxGroup>
                  </div>

                  <div
                    class="flex justify-center mt-2 border-t border-neutral/20"
                  >
                    <button
                      @click="resetSelectedOptions"
                      class="text-primary py-2 cursor-pointer"
                    >
                      {{ t('common.reset_all') }}
                    </button>
                  </div>
                </div>
              </template>
            </DPopper>
          </div>

          <div class="h-[310px] mt-8">
            <canvas ref="instrumentChartCanvas" id="instrumentChart"></canvas>
          </div>
        </div>
      </div>
    </template>
    <template #tab1>
      <!-- Trading History Section -->

      <div class="rounded-3xl border-2 border-neutral-13 py-4 mt-8">
        <div class="flex justify-between items-center px-6">
          <span class="font-bold text-xl">
            {{ t('account.trading_history') }}
          </span>
          <span class="flex items-center gap-2 text-sm font-semibold">
            <DaryaOutlineRefreshIcon class="text-primary" />
            {{ t('account.refresh_in') }}
            <span class="text-primary-shade3 text-xl font-bold">
              {{ t('account.numeric_five_minute') }}
            </span>
          </span>
        </div>
        <DataStateWrapper
          :isLoading="tradingOverviewState.isLoading.value"
          :hasError="tradingOverviewState.hasError.value"
          :hasNoData="tradingOverviewState.hasNoData.value"
          :isRefetching="tradingOverviewState.isRefetching.value"
          :errorConfig="tradingOverviewState.errorConfig.value"
          :emptyConfig="tradingOverviewState.emptyConfig.value"
        >
          <div class="mt-6">
            <DBaseTable
              :columns="columns"
              :data="tradingOverviewData"
              :rowsPerPage="10"
              :enable-pagination="true"
            >
              <template #column-sn="{ row, index }">
                {{ index + 1 }}
              </template>
              <template #column-date="{ row }">
                {{ d(row.date, 'shortDate') }}
              </template>
              <template #column-orderType="{ row }">
                <span
                  :class="{
                    'text-success-shade2': row.orderType === 1,
                    'text-error-shade1': row.orderType === 2,
                  }"
                >
                  {{ row.orderType === 1 ? 'Buy' : 'Sell' }}
                </span>
              </template>
            </DBaseTable>
          </div>
        </DataStateWrapper>
      </div>

      <!-- Trading Log Section -->

      <div class="rounded-3xl border-2 border-neutral-13 py-4 mt-6">
        <span class="px-6 font-bold text-xl">
          {{ t('account.trading_log') }}
        </span>
        <DataStateWrapper
          :isLoading="tradingLogsState.isLoading.value"
          :hasError="tradingLogsState.hasError.value"
          :hasNoData="tradingLogsState.hasNoData.value"
          :isRefetching="tradingLogsState.isRefetching.value"
          :errorConfig="tradingLogsState.errorConfig.value"
          :emptyConfig="tradingLogsState.emptyConfig.value"
        >
          <div class="flex ms-6 mt-6 gap-4">
            <DPopper
              v-model="isOpenStatusDropdown"
              placement="bottom"
              :arrow="false"
              offset-y="22"
              append-to-body
              @click="isOpenStatusDropdown = !isOpenStatusDropdown"
            >
              <template #default>
                <button
                  class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9"
                >
                  <div class="flex items-center justify-between">
                    <span>{{ t('account.all_status') }}</span>
                    <DaryaArrowDownIcon size="16" />
                  </div>
                </button>
              </template>

              <template #content>
                <div
                  class="bg-white bg-neutral-14 shadow-md rounded-lg py-1.5 w-44"
                >
                  <ul class="flex flex-col px-2">
                    <li
                      v-for="status in statuses"
                      :key="status as number"
                      @click="statusFilter = status"
                      :class="[
                        'px-2 py-3 rounded-2xl text-left cursor-pointer',
                        statusFilter === status
                          ? 'bg-primary text-white'
                          : 'hover:border border-primary-tint2',
                      ]"
                      class="mb-2"
                    >
                      {{ statusLabels[Number(status)] ?? status }}
                    </li>
                  </ul>

                  <div
                    class="flex justify-center mt-2 border-t border-neutral/20"
                  >
                    <button
                      @click="resetStatusFilter"
                      class="text-primary py-2 cursor-pointer"
                    >
                      {{ t('common.reset_all') }}
                    </button>
                  </div>
                </div>
              </template>
            </DPopper>

            <DPopper
              v-model="isOpenInstrumentDropdown"
              placement="bottom"
              :arrow="false"
              offset-y="22"
              append-to-body
              @click="isOpenInstrumentDropdown = !isOpenInstrumentDropdown"
            >
              <template #default>
                <button
                  class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9"
                >
                  <div class="flex items-center justify-between">
                    <span>{{ t('account.all_instrument') }}</span>
                    <DaryaArrowDownIcon size="16" />
                  </div>
                </button>
              </template>

              <template #content>
                <div
                  class="bg-white bg-neutral-14 shadow-md rounded-lg py-1.5 w-44"
                >
                  <div class="flex flex-col px-2">
                    <DCheckboxGroup v-model="selectedOptions">
                      <DCheckbox
                        v-for="instrument in instruments"
                        :key="instrument"
                        :value="instrument"
                        :label="instrument"
                        class="hover:border border-primary-tint2 rounded-2xl px-2 py-3"
                      />
                    </DCheckboxGroup>
                  </div>

                  <div
                    class="flex justify-center mt-2 border-t border-neutral/20"
                  >
                    <button
                      @click="resetSelectedOptions"
                      class="text-primary py-2 cursor-pointer"
                    >
                      {{ t('common.reset_all') }}
                    </button>
                  </div>
                </div>
              </template>
            </DPopper>

            <DPopper
              v-model="isOpenSideDropdown"
              placement="bottom"
              :arrow="false"
              offset-y="22"
              append-to-body
              @click="isOpenSideDropdown = !isOpenSideDropdown"
            >
              <template #default>
                <button
                  class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9"
                >
                  <div class="flex items-center justify-between">
                    <span>{{ t('account.all_side') }}</span>
                    <DaryaArrowDownIcon size="16" />
                  </div>
                </button>
              </template>

              <template #content>
                <div
                  class="bg-white bg-neutral-14 shadow-md rounded-lg py-1.5 w-44"
                >
                  <ul class="flex flex-col px-2">
                    <li
                      v-for="(side, index) in sides"
                      :key="index"
                      @click="sideFilter = side"
                      :class="[
                        'px-2 py-3 rounded-2xl text-left cursor-pointer',
                        sideFilter === side
                          ? 'bg-primary text-white'
                          : 'hover:border border-primary-tint2',
                      ]"
                      class="mb-2"
                    >
                      {{ side }}
                    </li>
                  </ul>

                  <div
                    class="flex justify-center mt-2 border-t border-neutral/20"
                  >
                    <button
                      @click="resetSideFilter"
                      class="text-primary py-2 cursor-pointer"
                    >
                      {{ t('common.reset_all') }}
                    </button>
                  </div>
                </div>
              </template>
            </DPopper>

            <DPopper
              v-model="isOpenDateDropdown"
              placement="bottom"
              :arrow="false"
              offset-y="22"
              append-to-body
              @click="isOpenDateDropdown = !isOpenDateDropdown"
            >
              <template #default>
                <button
                  class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9"
                >
                  <div class="flex items-center justify-between">
                    <span>
                      {{ formattedDateRange }}
                    </span>
                    <DaryaArrowDownIcon size="16" />
                  </div>
                </button>
              </template>

              <template #content>
                <div class="bg-lightMood-2 shadow-md rounded-lg p-3">
                  <div class="flex">
                    <div class="sidebar bg-lightMood-2 pt-10 p-2 w-32">
                      <button
                        class="block w-full text-left text-xs font-medium text-neutral-5 py-1 hover:text-primary cursor-pointer mb-3"
                        @click="setToday"
                      >
                        {{ t('common.today') }}
                      </button>
                      <button
                        class="block w-full text-left text-xs text-neutral-5 font-medium py-1 hover:text-primary cursor-pointer mb-3"
                        @click="setYesterday"
                      >
                        {{ t('common.yesterday') }}
                      </button>
                      <button
                        class="block w-full text-left text-xs text-neutral-5 font-medium py-1 hover:text-primary cursor-pointer mb-3"
                        @click="setLast7Days"
                      >
                        {{ t('common.last_seven_days') }}
                      </button>
                      <button
                        class="block w-full text-left text-xs text-neutral-5 font-medium py-1 hover:text-primary cursor-pointer mb-3"
                        @click="setCustom"
                      >
                        {{ t('common.custom') }}
                      </button>
                    </div>

                    <div class="calendar flex-1">
                      <VCalendar
                        v-model="selectedRange"
                        :columns="columnsDate"
                        :initial-page="{ month: 1, year: 2025 }"
                        :color="selectedColor"
                        :attributes="attributes"
                      />
                    </div>
                  </div>

                  <div class="buttons flex justify-end mt-3 gap-2">
                    <button
                      class="px-4 py-2 w-24 font-bold text-sm border border-neutral-10 rounded-lg cursor-pointer"
                      @click="cancel"
                    >
                      {{ t('common.cancel') }}
                    </button>

                    <button
                      class="px-4 py-2 font-bold text-sm w-24 bg-primary text-white rounded-lg cursor-pointer"
                      @click="apply"
                    >
                      {{ t('common.apply') }}
                    </button>
                  </div>
                </div>
              </template>
            </DPopper>
          </div>

          <div class="mt-6">
            <DBaseTable
              :columns="columnsTradingLogs"
              :data="filteredPositions"
              :rowsPerPage="10"
              :enable-pagination="true"
            >
              <template #column-sn="{ row, index }">
                {{ index + 1 }}
              </template>
              <template #column-date="{ row }">
                {{ d(row.date, 'shortDate') }}
              </template>
              <template #column-status="{ row }">
                <span
                  :class="{
                    'text-success-shade2': row.status === 0,
                    'text-error-shade1': row.status === 1,
                  }"
                >
                  {{ row.status === 0 ? 'Win' : 'Loss' }}
                </span>
              </template>
              <template #column-details="{ row }">
                <button
                  class="px-4 py-3 rounded-lg border border-primary cursor-pointer"
                  @click="handleShowLogDetail(row.positionId)"
                >
                  <span class="tex-sm text-primary font-bold">
                    {{ t('common.view') }}
                  </span>
                </button>
              </template>
            </DBaseTable>
          </div>
        </DataStateWrapper>
      </div>
    </template>
  </DTab>

  <TradingLogDetailDialog
    :isOpenTradingLogDetail="isOpenTradingLogDetail"
    :logDetailId="logDetailId"
    @update:isOpenTradingLogDetail="isOpenTradingLogDetail = $event"
    @close="isOpenTradingLogDetail = false"
  />
</template>

<script setup lang="ts">
import { ref, nextTick, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useScreens } from 'vue-screen-utils';
import { useRoute } from 'vue-router';
import { TradingLogDetailDialog } from '@/modules/trading';
import { Chart, registerables } from 'chart.js';
import AnnotationPlugin from 'chartjs-plugin-annotation';

import {
  useGetTradingOverviewById,
  useGetTradingLogByAccountId,
} from '@/modules/account';

import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const route = useRoute();
const { t, d } = useI18n();

const activeTab = ref('tab1');
const accountId = ref<number | undefined>(
  route.params.id ? Number(route.params.id) : undefined
);
const tabs = [
  { name: 'tab1', label: 'Trade History Log' },
  { name: 'tab2', label: 'Performance Overview' },
];
const isOpenTradingLogDetail = ref(false);
const isOpenStatusDropdown = ref(false);
const isOpenInstrumentDropdown = ref(false);
const isOpenSideDropdown = ref(false);
const isOpenDateDropdown = ref(false);
const selectedRange = ref<{ start: Date | null; end: Date | null }>({
  start: null,
  end: null,
});
const currentRange = ref<{ start: Date | null; end: Date | null }>({
  start: null,
  end: null,
});
const statusFilter = ref<number | undefined>(undefined);
const instrumentFilter = ref<string>('');
const sideFilter = ref<string | null>(null);
const selectedOptions = ref<string[]>([]);
const logDetailId = ref();
const columns = [
  { key: 'sn', label: 'SN' },
  { key: 'date', label: 'Date' },
  { key: 'orderType', label: 'Type' },
  { key: 'instrument', label: 'Instrument' },
  { key: 'tradesOpened', label: 'Trades Opened' },
  { key: 'tradesClosed', label: 'Trades Closed' },
  { key: 'openPrice', label: 'Open Price' },
  { key: 'closePrice', label: 'Close Price' },
  { key: 'result', label: 'Result' },
  { key: 'numberOfLots', label: 'No.of Lots' },
  { key: 'commission', label: 'Commission' },
  { key: 'swap', label: 'Swap' },
  { key: 'nextProfit', label: 'Net Profit' },
  { key: 'volume', label: 'Volume' },
  { key: 'ticketId', label: 'Ticket' },
  { key: 'pips', label: 'Pips' },
  { key: 'reason', label: 'Reason' },
  { key: 'sl', label: 'S/L' },
  { key: 'tp', label: 'T/P' },
];
const columnsTradingLogs = ref([
  { key: 'sn', label: 'SN' },
  { key: 'date', label: 'Date' },
  { key: 'status', label: 'Status' },
  { key: 'instrument', label: 'Instrument' },
  { key: 'entry', label: 'Entry' },
  { key: 'exit', label: 'Exit' },
  { key: 'return', label: 'Return' },
  { key: 'side', label: 'Side' },
  { key: 'details', label: 'Details' },
]);
const selectedColor = ref('indigo');
const attributes = ref([
  {
    highlight: {
      start: { fillMode: 'outline' },
      base: { fillMode: 'light' },
      end: { fillMode: 'outline' },
    },
    dates: { start: new Date(2025, 0, 14), end: new Date(2025, 0, 18) },
  },
]);
const chartCanvas = ref<HTMLCanvasElement | null>(null);
const selectedChartFilter = ref('Weekday');
const growthChartCanvas = ref<HTMLCanvasElement | null>(null);
const profitLossChartCanvas = ref<HTMLCanvasElement | null>(null);
const instrumentChartCanvas = ref<HTMLCanvasElement | null>(null);

const formattedDateRange = computed(() => {
  const { start, end } = currentRange.value;
  if (!start && !end) return 'Start date End Date';
  if (start && end) return `${d(start, 'shortDate')} - ${d(end, 'shortDate')}`;
  if (start) return `${d(start, 'shortDate')} - ...`;
  if (end) return `... - ${d(end, 'shortDate')}`;
  return 'Start date End Date';
});

const statusLabels = {
  0: 'Win',
  1: 'Loss',
};

// Trading Overview Data with state management
const {
  tradingOverviewData,
  status: tradingOverviewStatus,
  isFetching: tradingOverviewFetching,
  isError: tradingOverviewError,
  error: tradingOverviewErrorObj,
  getTradingOverviewById,
} = useGetTradingOverviewById(accountId, true);

const tradingOverviewState = useDataState(
  tradingOverviewStatus,
  tradingOverviewData,
  tradingOverviewFetching,
  tradingOverviewError,
  tradingOverviewErrorObj,
  'trading-data'
);

// Trading Logs Data with state management
const {
  tradingLogsData,
  status: tradingLogsStatus,
  isFetching: tradingLogsFetching,
  isError: tradingLogsError,
  error: tradingLogsErrorObj,
  getTradingLogsByAccountId,
} = useGetTradingLogByAccountId(accountId, true);

const tradingLogsState = useDataState(
  tradingLogsStatus,
  tradingLogsData,
  tradingLogsFetching,
  tradingLogsError,
  tradingLogsErrorObj,
  'trading-data'
);

const retryTradingOverview = () => getTradingOverviewById();
const retryTradingLogs = () => getTradingLogsByAccountId();

const statuses = computed<number[]>(
  () =>
    Array.from(new Set(tradingLogsData.value.map((p) => p.status))) as number[]
);

const instruments = computed(() =>
  Array.from(new Set(tradingLogsData.value.map((p) => p.instrument)))
);

const sides = computed<string[]>(
  () =>
    Array.from(new Set(tradingLogsData.value.map((p) => p.side))) as string[]
);

const filteredPositions = computed(() => {
  return tradingLogsData.value.filter((pos) => {
    // status
    if (statusFilter.value != undefined && pos.status !== statusFilter.value) {
      return false;
    }
    // instrument
    if (instrumentFilter.value && pos.instrument !== instrumentFilter.value) {
      return false;
    }
    // side
    if (sideFilter.value && pos.side !== sideFilter.value) {
      return false;
    }
    if (
      selectedOptions.value.length > 0 &&
      !selectedOptions.value.includes(pos.instrument)
    ) {
      return false;
    }
    if (currentRange.value.start && pos.date < currentRange.value.start)
      return false;
    if (currentRange.value.end && pos.date > currentRange.value.end)
      return false;
    return true;
  });
});

const resetSideFilter = () => {
  sideFilter.value = null;
};

const resetStatusFilter = () => {
  statusFilter.value = undefined;
};

const resetSelectedOptions = () => {
  selectedOptions.value = [];
};

const handleShowLogDetail = (item) => {
  logDetailId.value = item;
  isOpenTradingLogDetail.value = true;
};

const { mapCurrent } = useScreens({
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
});

const columnsDate = mapCurrent({ lg: 2 }, 1);

watch(isOpenDateDropdown, (newVal) => {
  if (newVal) {
    selectedRange.value = { ...currentRange.value };
  }
});

// Preset Functions
const setToday = () => {
  const today = new Date();
  selectedRange.value = { start: today, end: today };
};
const setYesterday = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  selectedRange.value = { start: yesterday, end: yesterday };
};

const setLast7Days = () => {
  const today = new Date();
  const lastWeek = new Date();
  lastWeek.setDate(today.getDate() - 7);
  selectedRange.value = { start: lastWeek, end: today };
};

const setCustom = () => {
  selectedRange.value = { start: null, end: null };
};

const cancel = () => {
  isOpenDateDropdown.value = false;
};

const apply = () => {
  currentRange.value = { ...selectedRange.value };
  isOpenDateDropdown.value = false;
};

watch(
  () => route.params.id,
  (newId) => {
    accountId.value = newId ? Number(newId) : undefined;
  }
);
watch(activeTab, async (newTab) => {
  if (newTab === 'tab2') {
    await nextTick();
    initializeChart();
  }
});

Chart.register(...registerables, AnnotationPlugin);

const initializeChart = () => {
  if (chartCanvas.value) {
    const ctx = chartCanvas.value.getContext('2d');
    if (ctx) {
      // Initialize the Chart.js instance
      new Chart(ctx, {
        type: 'pie',
        data: {
          labels: ['Win', 'Loss'],
          datasets: [
            {
              data: [27.27, 72.73],
              backgroundColor: ['#1E3A8A', '#D1C4E9'],
              borderWidth: 2,
              borderColor: '#FFFFFF',
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'bottom',
            },
          },
        },
      });
    } else {
      console.error('Failed to get 2D context from canvas.');
    }
  } else {
    console.error('chartCanvas is null.');
  }
  if (growthChartCanvas.value) {
    const ctx = growthChartCanvas.value.getContext('2d');
    if (ctx) {
      new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['Apr 12', 'Apr 13', 'Apr 14', 'Apr 15', 'Apr 16'],
          datasets: [
            {
              label: 'Balance',
              data: [5000, 5000, 5000, 4900, 4800],
              borderColor: '#6A5ACD',
              borderWidth: 2,
              borderDash: [5, 5],
              pointBackgroundColor: '#6A5ACD',
              pointRadius: 4,
              pointHoverRadius: 6,
              fill: false,
            },
            {
              label: 'Equity',
              data: [4900, 4900, 4900, 4900, 4850],
              borderColor: '#1E90FF',
              borderWidth: 2,
              pointBackgroundColor: '#1E90FF',
              pointRadius: 4,
              pointHoverRadius: 6,
              fill: false,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false, // Allow the chart to scale freely within the defined height
          scales: {
            y: {
              beginAtZero: false,
              min: 4750,
              max: 5000,
              ticks: { stepSize: 50, color: '#000000' },
              grid: { color: '#D3D3D3' },
            },
            x: {
              ticks: { color: '#000000' },
              grid: { color: '#D3D3D3' },
            },
          },
          plugins: {
            legend: {
              position: 'bottom',
              align: 'start',
              labels: { boxWidth: 12, padding: 20, color: '#000000' },
            },
            annotation: {
              annotations: {
                line1: {
                  type: 'line',
                  xMin: 'Apr 16',
                  xMax: 'Apr 16',
                  borderColor: 'red',
                  borderWidth: 2,
                  borderDash: [5, 5],
                },
              },
            },
          },
          elements: { line: { tension: 0 } },
        },
      });
    } else {
      console.error('Failed to get 2D context from canvas.');
    }
  } else {
    console.error('growthChartCanvas is null.');
  }
  if (profitLossChartCanvas.value) {
    const ctx = profitLossChartCanvas.value.getContext('2d');
    if (ctx) {
      new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Buy', 'Sell'],
          datasets: [
            {
              label: 'Buy',
              data: [-40, 0], // Sample data for Buy
              backgroundColor: '#1CC6E0',
              borderColor: '#1599AD', // Light blue for Buy
              barThickness: 60, // Wider bars to match the image
              borderWidth: 2,
              borderRadius: 24,
              borderSkipped: false,
            },
            {
              label: 'Sell',
              data: [0, 20], // Sample data for Sell
              backgroundColor: '#DF4756',
              borderColor: '#C92334', // Red for Sell
              barThickness: 60,
              borderWidth: 2,
              borderRadius: 5,
              borderSkipped: false,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: false,
              min: -80,
              max: 20,
              ticks: {
                stepSize: 20,
                color: '#000000',
              },
              grid: {
                color: '#D3D3D3',
              },
            },
            x: {
              ticks: {
                color: '#000000',
              },
              grid: {
                display: false, // Hide vertical grid lines
              },
            },
          },
          plugins: {
            legend: {
              position: 'bottom',
              align: 'start',
              labels: {
                boxWidth: 12,
                padding: 20,
                color: '#000000',
                usePointStyle: true,
                pointStyle: 'circle',
              },
            },
          },
        },
      });
    } else {
      console.error('Failed to get 2D context from canvas.');
    }
  } else {
    console.error('profitLossChartCanvas is null.');
  }
  if (instrumentChartCanvas.value) {
    const ctx = instrumentChartCanvas.value.getContext('2d');

    if (ctx) {
      new Chart(ctx, {
        type: 'pie',
        data: {
          labels: ['USDJPY', 'NZDUSD', 'AUDJPY', '+3 more'],
          datasets: [
            {
              data: [30, 20, 20, 30], // Sample data to approximate the image
              backgroundColor: ['#1E3A8A', '#D1C4E9', '#F9A8D4', '#FDE68A'], // Colors to match the image
              borderWidth: 2,
              borderColor: '#FFFFFF', // White border for segment separation
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                boxWidth: 12,
                padding: 20,
                color: '#000000',
                usePointStyle: true,
                pointStyle: 'circle',
              },
            },
            tooltip: {
              callbacks: {
                label: function (context) {
                  const label = context.label || '';
                  const value = context.parsed;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(2);
                  return `${label}: ${percentage}%`;
                },
              },
            },
          },
        },
      });
    } else {
      console.error('Failed to get 2D context from canvas.');
    }
  } else {
    console.error('instrumentChartCanvas is null.');
  }
};

onMounted(() => {
  if (activeTab.value === 'tab2') {
    initializeChart();
  }
});
</script>

<style scoped>
.backgroundCard {
  background: linear-gradient(
    98deg,
    rgba(170, 153, 255, 0.25) 0%,
    rgba(129, 105, 255, 0.25) 48.35%,
    rgba(81, 47, 255, 0.25) 100%
  );
}
</style>
