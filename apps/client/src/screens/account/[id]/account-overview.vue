<template xmlns="http://www.w3.org/1999/html">
  <div class="flex justify-between items-center mt-8 mb-12">
    <span class="font-bold text-xl">{{ t('common.accounts') }}</span>
    <router-link v-slot="{ navigate }" to="/challenges">
      <button
        class="flex items-center bg-primary rounded-lg py-3 px-5 text-white"
        @click="navigate"
      >
        <DaryaPlusIcon class="mr-2" />
        <span class="font-bold text-base">
          {{ t('common.start_challenge') }}
        </span>
      </button>
    </router-link>
  </div>

  <!-- Account Overview Section -->
  <DataStateWrapper
    :isLoading="accountOverviewState.isLoading.value"
    :hasError="accountOverviewState.hasError.value"
    :hasNoData="accountOverviewState.hasNoData.value"
    :isRefetching="accountOverviewState.isRefetching.value"
    :errorConfig="accountOverviewState.errorConfig.value"
    :emptyConfig="accountOverviewState.emptyConfig.value"
  >
    <div class="flex gap-6">
      <div class="bg-lightMood-2 rounded-3xl flex flex-col py-4 px-6 min-w-80">
        <DaryaBoldCoinIcon class="text-primary" />
        <span class="text-xs font-normal text-neutral-4 pt-6 pb-1">
          {{ t('common.balance') }}
        </span>
        <span class="inline-flex items-center">
          <span class="text-3xl font-semibold pr-2">
            ${{ accountOverviewData.balance }}
          </span>
          <span class="flex items-center text-success">
            <DaryaTrendArrowUpIcon class="mt-2" size="24" />
            <span class="text-base font-normal">
              {{ accountOverviewData.balancePercent }}%
            </span>
          </span>
        </span>
      </div>

      <div class="bg-lightMood-2 rounded-3xl flex flex-col py-4 px-6 min-w-80">
        <DaryaBoldBitcoinConvertIcon class="text-primary" />
        <span class="text-xs font-normal text-neutral-4 pt-6 pb-1">
          {{ t('common.profit_loss') }}
        </span>

        <span class="inline-flex items-center">
          <span class="text-3xl font-semibold pr-2">
            ${{ accountOverviewData.profitLoss }}
          </span>
          <span class="flex items-center text-error-shade1">
            <DaryaTrendArrowDownIcon class="mt-2" size="24" />
            <span class="text-base font-normal">
              {{ accountOverviewData.profitLoss }}%
            </span>
          </span>
        </span>
      </div>

      <div class="bg-lightMood-2 rounded-3xl flex flex-col py-4 px-6 min-w-80">
        <DaryaBoldCardCoinIcon class="text-primary" />
        <span class="text-xs font-normal text-neutral-4 pt-6 pb-1">
          {{ t('common.equity') }}
        </span>
        <span class="inline-flex items-center">
          <span class="text-3xl font-semibold pr-2">
            ${{ accountOverviewData.equity }}
          </span>
          <span class="flex items-center text-success">
            <DaryaTrendArrowUpIcon class="mt-2" size="24" />
            <span class="text-base font-normal">
              {{ accountOverviewData.equityPercent }}%
            </span>
          </span>
        </span>
      </div>

      <div class="bg-lightMood-2 rounded-3xl flex flex-col py-4 px-6 min-w-80">
        <DaryaBoldMenuBoardIcon class="text-primary" />
        <span class="text-xs font-normal text-neutral-4 pt-6 pb-1">
          {{ t('account.trading_days') }}
        </span>
        <span class="inline-flex items-center">
          <span class="text-3xl font-semibold pr-2">
            {{ accountOverviewData.tradingDays }}
          </span>
        </span>
      </div>
    </div>
  </DataStateWrapper>

  <div class="grid grid-cols-3 gap-4 w-full mt-11 mb-6">
    <!-- Trading Objective Block -->
    <div class="px-6 py-4 rounded-3xl border-2 border-neutral-13 col-span-2">
      <div class="flex justify-between items-center mb-11">
        <span class="font-semibold text-lg">
          {{ t('account.trading_objective') }}
        </span>
        <span class="flex items-center gap-2 text-sm font-semibold">
          <DaryaOutlineRefreshIcon class="text-primary" />
          {{ t('account.refresh_in') }}
          <span class="text-primary-shade3 text-xl font-bold">
            {{ t('account.numeric_five_minute') }}
          </span>
        </span>
      </div>

      <!-- Trading Objectives with State Management -->
      <DataStateWrapper
        :isLoading="tradingObjectiveState.isLoading.value"
        :hasError="tradingObjectiveState.hasError.value"
        :hasNoData="tradingObjectiveState.hasNoData.value"
        :isRefetching="tradingObjectiveState.isRefetching.value"
        :errorConfig="tradingObjectiveState.errorConfig.value"
        :emptyConfig="tradingObjectiveState.emptyConfig.value"
      >
        <!-- Objective Cards Grid -->
        <div class="grid grid-cols-2 gap-6">
          <div
            v-for="(item, index) in tradingObjectiveData"
            :key="index"
            class="rounded-2xl border border-neutral-300 p-5 flex flex-col"
          >
            <div class="flex justify-between items-start">
              <span class="font-medium text-neutral-4 text-xs">
                {{ item.title }}
              </span>
              <span
                class="text-xs px-3 py-1 border rounded-lg"
                :class="stateColors[item.tagState].chip.class"
              >
                {{ stateColors[item.tagState].chip.label }}
              </span>
            </div>

            <div class="text-2xl font-bold my-2">
              {{ item.tagState === 3 ? '' : '$' }}{{ item.ruleValue }}
            </div>

            <div class="flex items-end justify-between mt-auto">
              <ul class="text-xs text-gray-500 space-y-1">
                <li
                  class="mb-3"
                  v-for="(textItem, textIndex) in item.selectedListItem"
                  :key="textIndex"
                >
                  <span
                    class="inline-block w-2 h-2 rounded-full mr-1"
                    :class="
                      textItem.selected
                        ? stateColors[item.tagState].bulletColor.selected
                        : stateColors[item.tagState].bulletColor.default
                    "
                  />
                  {{ textItem.text }} {{ textItem.value }}
                </li>
              </ul>

              <!-- Only show the SVG on non-achieved cards -->
              <div v-if="item.tagState !== 3" class="relative w-28 h-28">
                <svg viewBox="0 0 36 36" class="w-full h-full">
                  <!-- Background Track -->
                  <path
                    :class="
                      stateColors[item.tagState].strokeStyle.parentPathColor
                    "
                    d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="3"
                  />
                  <!-- Progress -->
                  <path
                    :class="stateColors[item.tagState].strokeStyle.pathColor"
                    :stroke-dasharray="
                      calculateChartPercent(item.currentValue, item.ruleValue)
                    "
                    d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-width="3"
                  />
                  <text
                    x="18"
                    y="18"
                    class="text-[4px] font-bold fill-current"
                    text-anchor="middle"
                    alignment-baseline="middle"
                    :class="
                      stateColors[item.tagState]?.strokeStyle?.textColor ||
                      'text-neutral-12'
                    "
                  >
                    <tspan x="18" dy="-4">
                      {{
                        calculateChartPercent(
                          item.currentValue,
                          item.ruleValue
                        )
                      }}%
                    </tspan>
                    <tspan x="18" dy="5" class="fill-neutral-12">of</tspan>
                    <tspan x="18" dy="5">${{ item.ruleValue }}</tspan>
                  </text>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </DataStateWrapper>
    </div>

    <!-- Right Side Panel -->
    <div class="flex flex-col">
      <!-- Calendar -->
      <div
        class="flex justify-between rounded-3xl border-2 border-neutral-13 py-5 px-6"
      >
        <div class="flex justify-between items-center">
          <div class="flex gap-1 text-sm text-center">
            <div
              class="flex flex-col items-center py-1 px-2 rounded-lg bg-primary/15"
            >
              <DaryaFillStreakOffIcon class="text-primary" size="32" />
              <span class="text-xs text-neutral-11">S</span>
              <span class="text-base font-medium">07</span>
            </div>

            <div class="flex flex-col items-center py-1 px-2 rounded-lg">
              <DaryaFillStreakOffIcon class="text-neutral-11" size="32" />
              <span class="text-xs text-neutral-11">Su</span>
              <span class="text-base font-medium">08</span>
            </div>

            <div class="flex flex-col items-center py-1 px-2 rounded-lg">
              <DaryaFillStreakOnIcon class="text-neutral-11" size="32" />
              <span class="text-xs text-neutral-11">M</span>
              <span class="text-base font-medium">09</span>
            </div>

            <div class="flex flex-col items-center py-1 px-2 rounded-lg">
              <DaryaFillStreakOnIcon class="text-neutral-11" size="32" />
              <span class="text-xs text-neutral-11">T</span>
              <span class="text-base font-medium">10</span>
            </div>

            <div class="flex flex-col items-center py-1 px-2 rounded-lg">
              <DaryaFillStreakOnIcon class="text-neutral-11" size="32" />
              <span class="text-xs text-neutral-11">W</span>
              <span class="text-base font-medium">11</span>
            </div>
          </div>
        </div>

        <div class="h-20 border-neutral-13 border-r px-1" />

        <div
          class="flex flex-col justify-center items-center text-sm font-medium text-right"
        >
          <span class="text-primary-shade1 text-xl font-bold">May</span>
          <span class="text-xs text-neutral-6">2025</span>
        </div>
      </div>

      <!-- Account Info Section -->

      <div
        class="rounded-3xl border-2 border-neutral-13 pt-4 px-6 pb-8 space-y-4 my-8"
      >
        <DataStateWrapper
          :isLoading="accountOverviewState.isLoading.value"
          :hasError="accountOverviewState.hasError.value"
          :hasNoData="accountOverviewState.hasNoData.value"
          :isRefetching="accountOverviewState.isRefetching.value"
          :errorConfig="accountOverviewState.errorConfig.value"
          :emptyConfig="accountOverviewState.emptyConfig.value"
        >
          <div class="flex items-center justify-between mb-8">
            <div class="text-xs">
              <span class="text-neutral-6">{{ t('account.account_id') }} </span>
              <span class="text-primary-shade1 font-bold">
                #{{ accountOverviewData.accountId }}
              </span>
            </div>

            <button
              class="bg-primary/15 w-20 text-xs px-2 py-1 border border-primary-tint6 rounded-lg"
            >
              {{ accountOverviewData.accountType }}
            </button>
          </div>

          <div class="text-sm text-gray-700">
            <div class="text-xs mb-2">
              <span class="text-neutral-5 pe-0.5">
                {{ t('common.initial_balance') }}
              </span>
              <span class="text-neutral-2">
                {{ accountOverviewData.initialBalance }}
              </span>
            </div>

            <div class="text-xs mb-2">
              <span class="text-neutral-5 pe-0.5">
                {{ t('common.plan_type') }}
              </span>
              <span class="text-neutral-2">
                {{ accountOverviewData.planType }}
              </span>
            </div>

            <div class="text-xs">
              <span class="text-neutral-5 pe-0.5">
                {{ t('common.account_type') }}
              </span>
              <span class="text-neutral-2">
                {{ accountOverviewData.accountType }}
              </span>
            </div>
          </div>

          <div class="space-y-2 text-sm text-gray-600">
            <div class="flex items-center justify-between">
              <span class="flex items-center gap-2">
                <span
                  class="flex items-center justify-center rounded-lg border border-neutral-13 p-2"
                >
                  <DaryaOutlinePersonalCardIcon class="text-primary-tint4" />
                </span>
                <span class="text-neutral-4">
                  {{ t('common.login') }}
                </span>
              </span>

              <div
                class="w-40 flex justify-between items-center bg-primary/10 px-4 py-1 rounded-lg text-xs"
              >
                <span>{{ accountOverviewData.accountId }}</span>
                <button
                  class="cursor-pointer"
                  @click="copyAccountId"
                  type="button"
                  title="Copy to clipboard"
                >
                  <DaryaOutlineCopyIcon class="text-neutral-4" size="20" />
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <span class="flex items-center gap-2">
                <span
                  class="flex items-center justify-center rounded-lg border border-neutral-13 p-2"
                >
                  <DaryaOutlineLockIcon class="text-primary-tint4" />
                </span>
                <span class="text-neutral-4">
                  {{ t('account.master_pass') }}
                </span>
              </span>

              <div
                class="w-40 flex justify-between items-center bg-primary/10 px-4 py-1 rounded-lg text-xs"
              >
                <span class="truncate">
                  <!-- truncate so very long passwords don’t overflow -->
                  <template v-if="showMasterPass">
                    {{ accountOverviewData.metaMasterPassword }}
                  </template>
                  <template v-else> ••••••• </template>
                </span>

                <div class="inline-flex">
                  <button
                    class="cursor-pointer"
                    @click="copyMasterPassword"
                    type="button"
                    title="Copy to clipboard"
                  >
                    <DaryaOutlineCopyIcon class="text-neutral-4" size="20" />
                  </button>
                  <button
                    class="cursor-pointer"
                    @click="toggleShowMasterPass"
                    type="button"
                    title="Toggle visibility"
                  >
                    <template v-if="showMasterPass">
                      <DaryaOutlineEyeSlashIcon
                        class="text-neutral-4"
                        size="20"
                      />
                    </template>
                    <template v-else>
                      <DaryaOutlineEyeIcon class="text-neutral-4" size="20" />
                    </template>
                  </button>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <span class="flex items-center gap-2">
                <span
                  class="flex items-center justify-center rounded-lg border border-neutral-13 p-2"
                >
                  <DaryaOutlineCloudChangeIcon class="text-primary-tint4" />
                </span>
                <span class="text-neutral-4">{{ t('common.server') }}</span>
              </span>

              <div
                class="w-40 flex justify-center items-center bg-primary/10 px-4 py-1 rounded-lg text-xs"
              >
                <span>{{ accountOverviewData.server }}</span>
              </div>
            </div>
          </div>
        </DataStateWrapper>
      </div>
    </div>
  </div>

  <div class="rounded-3xl border-2 border-neutral-13 pt-4">
    <span class="px-6 font-bold text-xl">
      {{ t('account.daily_reports') }}
    </span>
    <!-- Daily Trade Data Section -->
    <DataStateWrapper
      :isLoading="dailyTradeState.isLoading.value"
      :hasError="dailyTradeState.hasError.value"
      :hasNoData="dailyTradeState.hasNoData.value"
      :isRefetching="dailyTradeState.isRefetching.value"
      :errorConfig="dailyTradeState.errorConfig.value"
      :emptyConfig="dailyTradeState.emptyConfig.value"
    >
      <div class="mt-11">
        <DBaseTable
          :columns="columns"
          :data="accountDailyTradeData"
          :rowsPerPage="10"
          :enable-pagination="true"
        >
          <template #column-sn="{ row, index }">
            {{ index + 1 }}
          </template>
          <template #column-date="{ row }">
            {{ d(row.date, 'shortDate') }}
          </template>
          <template #column-orderType="{ row }">
            <span
              :class="{
                'text-success-shade2': row.orderType === 'Buy',
                'text-error-shade1': row.orderType === 'Sell',
              }"
            >
              {{ row.orderType === 'Buy' ? 'Buy' : 'Sell' }}
            </span>
          </template>
        </DBaseTable>
      </div>
    </DataStateWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

import {
  useGetDailyTradeById,
  useGetTradingObjectiveById,
  useGetAccountOverviewById,
} from '@/modules/account/index';

import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const { t, d } = useI18n();
const route = useRoute();

const columns = ref([
  { key: 'sn', label: 'Sn' },
  { key: 'date', label: 'Date' },
  { key: 'orderType', label: 'Order Type' },
  { key: 'instrument', label: 'Instrument' },
  { key: 'tradesOpened', label: 'Trades Opened' },
  { key: 'tradesClosed', label: 'Trades Closed' },
  { key: 'numberOfLots', label: 'No. of Lots' },
  { key: 'commission', label: 'Commission' },
  { key: 'result', label: 'Result' },
]);
const showMasterPass = ref(false);
const accountId = ref<number | undefined>(
  route.params.id ? Number(route.params.id) : undefined
);

// Daily Trade Data with state management
const {
  accountDailyTradeData,
  status: dailyTradeStatus,
  isFetching: dailyTradeFetching,
  isError: dailyTradeError,
  error: dailyTradeErrorObj,
  getDailyTradeById,
} = useGetDailyTradeById(accountId, true);

const dailyTradeState = useDataState(
  dailyTradeStatus,
  accountDailyTradeData,
  dailyTradeFetching,
  dailyTradeError,
  dailyTradeErrorObj,
  'trading-data'
);

// Trading Objective Data with state management
const {
  tradingObjectiveData,
  status: tradingObjectiveStatus,
  isFetching: tradingObjectiveFetching,
  isError: tradingObjectiveError,
  error: tradingObjectiveErrorObj,
  getTradingObjectiveById,
} = useGetTradingObjectiveById(accountId, true);

const tradingObjectiveState = useDataState(
  tradingObjectiveStatus,
  tradingObjectiveData,
  tradingObjectiveFetching,
  tradingObjectiveError,
  tradingObjectiveErrorObj,
  'trading-data'
);

// Account Overview Data with state management
const {
  accountOverviewData,
  status: accountOverviewStatus,
  isFetching: accountOverviewFetching,
  isError: accountOverviewError,
  error: accountOverviewErrorObj,
  getAccountOverviewById,
} = useGetAccountOverviewById(accountId, true);

const accountOverviewState = useDataState(
  accountOverviewStatus,
  accountOverviewData,
  accountOverviewFetching,
  accountOverviewError,
  accountOverviewErrorObj,
  'trading-data'
);

// Retry functions
const retryDailyTrade = () => getDailyTradeById();
const retryTradingObjective = () => getTradingObjectiveById();
const retryAccountOverview = () => getAccountOverviewById();

const calculateChartPercent = (value, total) => {
  if (!value || !total || total === 0) return 0;
  return (value / total) * 100;
};

const stateColors = {
  1: {
    chip: {
      label: 'On Going',
      class: 'bg-info/15 text-info-shade2 border-info-tini1',
    },
    bulletColor: { default: 'bg-neutral-12 ', selected: 'bg-info' },
    strokeStyle: {
      parentPathColor: 'text-neutral-12',
      pathColor: 'text-info',
      textColor: 'fill-info',
    },
  },
  2: {
    chip: {
      label: 'Breached',
      class: 'bg-error/15  text-error-shade1 border border-error-tint2',
    },
    bulletColor: { default: 'bg-neutral-12 ', selected: 'bg-warning' },
    strokeStyle: {
      parentPathColor: 'text-error-tint3',
      pathColor: 'text-warning',
      textColor: 'fill-warning',
    },
  },
  3: {
    chip: {
      label: 'Achieved',
      class: 'bg-success/15 text-success-shade2 border-success-tint1',
    },

    bulletColor: { default: 'bg-neutral-12 ', selected: 'bg-green-600' },
    strokeStyle: {
      parentPathColor: 'text-neutral-12',
      pathColor: 'text-success',
      textColor: 'fill-success',
    },
  },
};

const toggleShowMasterPass = () => {
  showMasterPass.value = !showMasterPass.value;
};

const copyMasterPassword = () => {
  const raw = accountOverviewData.value.metaMasterPassword;

  navigator.clipboard
    .writeText(raw)
    .then(() => {
      console.log('Copied!');
    })
    .catch((err) => console.error(err));
};

const copyAccountId = () => {
  const raw = accountOverviewData.value.accountId;

  navigator.clipboard
    .writeText(raw)
    .then(() => {
      console.log('Copied!');
    })
    .catch((err) => console.error(err));
};

watch(
  () => route.params.id,
  (newId) => {
    accountId.value = newId ? Number(newId) : undefined;
  }
);
</script>
