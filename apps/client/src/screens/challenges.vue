<template>
  <span class="font-bold text-xl">{{ t('challenge.challenges') }}</span>
  <!-- Data State Wrapper -->
  <DataStateWrapper
    :isLoading="dataState.isLoading.value"
    :hasError="dataState.hasError.value"
    :hasNoData="dataState.hasNoData.value"
    :isRefetching="dataState.isRefetching.value"
    :errorConfig="dataState.errorConfig.value"
    :emptyConfig="dataState.emptyConfig.value"
  >
    <div class="flex w-full justify-center text-primary font-bold text-sm mt-8">
      <button
        class="flex items-center py-2 px-4 cursor-pointer border border-primary rounded-tl-lg rounded-bl-lg"
        :class="selectedPlanFilter === 'Standard' ? 'backgroundCard ' : ''"
        @click="handleGetPlan(PLAN_TYPE.Standard)"
      >
        <DaryaFillLeaderBoardIcon size="20" />
        <span class="ps-2">Standard</span>
      </button>

      <button
        class="flex items-center border border-primary py-2 px-4 cursor-pointer"
        :class="selectedPlanFilter === 'Pro' ? 'backgroundCard ' : ''"
        @click="handleGetPlan(PLAN_TYPE.Pro)"
      >
        <DaryaFillRocketIcon size="20" />
        <span class="ps-2">Pro</span>
      </button>

      <button
        class="flex items-center py-2 px-4 cursor-pointer border border-primary rounded-tr-lg rounded-br-lg"
        :class="selectedPlanFilter === 'Premium' ? 'backgroundCard ' : ''"
        @click="handleGetPlan(PLAN_TYPE.Premium)"
      >
        <DaryaFillFlameIcon size="20" />
        <span class="ps-2">Premium</span>
      </button>
    </div>

    <div class="flex flex-row w-full gap-6 mt-11 overflow-x-auto">
      <PlanCard
        v-for="item in planListGroup"
        :key="item.id"
        :plan="item"
        @getPlanDetail="handleGetPlanDetail"
      />
    </div>
  </DataStateWrapper>

  <GetPlanDialog
    :isOpenGetPlanDialog="isOpenGetPlanDialog"
    @update:isOpenGetPlanDialog="isOpenGetPlanDialog = $event"
    :planDetailData="planDetailData"
    @close="isOpenGetPlanDialog = false"
  />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import {
  useGetPlanDetailById,
  useGetPlanByPlanTypeId,
  GetPlanDialog,
  PlanCard,
} from '@/modules/plan/index';

import { useDataState } from '@/composables/useDataState';
const planId = ref();
const planTypeId = ref(4);
const selectedPlanFilter = ref('Standard');
const isOpenGetPlanDialog = ref(false);
const PLAN_TYPE = {
  Pro: 2,
  Premium: 3,
  Standard: 4,
};

const { t } = useI18n();

const {
  getPlansByPlanTypeId,
  planListGroup,
  status,
  isFetching,
  isError,
  error,
} = useGetPlanByPlanTypeId(planTypeId, false);

// Data state management - single composable handles everything
const dataState = useDataState(
  status,
  planListGroup,
  isFetching,
  isError,
  error,
  'no-data' // Empty state type
);
const { getPlanDetailById, planDetailData } = useGetPlanDetailById(
  planId,
  false
);

const handleGetPlan = (id: number) => {
  selectedPlanFilter.value =
    Object.keys(PLAN_TYPE).find((key) => PLAN_TYPE[key] === id) || '';

  planTypeId.value = id;
  getPlansByPlanTypeId();
};

const handleGetPlanDetail = (id) => {
  planId.value = id;
  getPlanDetailById();
  isOpenGetPlanDialog.value = true;
};

onMounted(() => {
  getPlansByPlanTypeId();
});
</script>

<style scoped>
.backgroundCard {
  background: linear-gradient(
    98deg,
    rgba(170, 153, 255, 0.25) 0%,
    rgba(129, 105, 255, 0.25) 48.35%,
    rgba(81, 47, 255, 0.25) 100%
  );
}
</style>

<script setup lang="ts"></script>
