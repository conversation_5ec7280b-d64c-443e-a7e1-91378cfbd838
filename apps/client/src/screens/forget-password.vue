<template>
  <div class="flex flex-col items-center w-full pt-14 overflow-y-auto h-screen">
    <img src="@/assets/images/logo.svg" alt="Logo" class="w-24 h-24" />
    <span class="text-3xl font-semibold pt-5">
      {{ t('login.forgot_password') }}
    </span>

    <div class="w-96 mt-8">
      <VeeForm v-slot="{ handleSubmit }" class="space-y-6">
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onsubmit)"
          class="space-y-6"
        >
          <VeeField
            v-slot="{ field, errorMessage }"
            name="email"
            v-model="formModel.userName"
          >
            <DInput v-bind="field" placeholder="Enter Your Email Address" />
          </VeeField>
          <button
            type="submit"
            class="flex w-full justify-center items-center gap-2 text-white text-xs font-bold px-6 py-4 rounded-lg bg-primary"
          >
            {{ t('common.continue') }}
          </button>
        </form>
      </VeeForm>

      <router-link
        to="/login"
        class="flex justify-center text-xs font-bold text-primary-tint3 mt-4 cursor-pointer"
      >
        {{ t('login.sign_in_now') }}
      </router-link>
    </div>
  </div>

  <DDialog
    :open="isOpenPasswordResetConfirmDialog"
    :showCloseIcon="false"
    :closeOnClickOutside="false"
  >
    <template #body>
      <div class="flex flex-col items-center justify-center">
        <div class="p-1 primary-tint-10 rounded-lg">
          <DaryaOutlineMessageCircleIcon size="32" class="text-primary-tint1" />
        </div>

        <span class="pt-5 text-lg font-bold">
          {{ t('login.password_reset_sent') }}
        </span>
        <span
          class="flex justify-center text-center pt-1 w-72 text-[10px] font-normal"
        >
          {{
            t('login.forgot_password_dialog_description', {
              userName: formModel.userName,
            })
          }}
        </span>

        <button
          @click="isOpenPasswordResetConfirmDialog = false"
          class="w-20 bg-primary px-4 py-3 rounded-lg mt-5 text-white text-xs font-bold"
        >
          {{ t('common.ok') }}
        </button>
      </div>
    </template>
  </DDialog>

  <OtpDialog
    :isOpenOtpDialog="isOpenOtpDialog"
    @update:isOpenOtpDialog="isOpenOtpDialog = $event"
    @close="isOpenOtpDialog = false"
    @verify-code="handleVerifyOtp"
    :email="formModel.userName"
    :has-error="hasOtpError"
  />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import {
  useForgetPasswordRequestMutation,
  OtpDialog,
  useValidateForgetPasswordOtpMutation,
} from '@/modules/user/index';

const { t } = useI18n();

const userId = ref();
const hasOtpError = ref(false);
const isOpenOtpDialog = ref(false);
const isOpenPasswordResetConfirmDialog = ref(false);
const formModel = reactive({
  userName: '',
});

const handleSuccessForgetPassword = ({ data }) => {
  userId.value = data;
  isOpenOtpDialog.value = true;
};

const handleErrorForgetPassword = () => {};

const { forgetPasswordRequest } = useForgetPasswordRequestMutation({
  onSuccess: handleSuccessForgetPassword,
  onError: handleErrorForgetPassword,
});

const handleSuccessVerifyOtpForgetPassword = () => {
  isOpenOtpDialog.value = false;
  isOpenPasswordResetConfirmDialog.value = true;
};

const handleErrorVerifyOtpForgetPassword = () => {
  hasOtpError.value = true;
};

const { verifyForgetPasswordOtp } = useValidateForgetPasswordOtpMutation({
  onSuccess: handleSuccessVerifyOtpForgetPassword,
  onError: handleErrorVerifyOtpForgetPassword,
});

const handleVerifyOtp = (otp) => {
  verifyForgetPasswordOtp({ otp: otp, userId: userId.value });
};

const onsubmit = () => {
  forgetPasswordRequest(formModel);
};
</script>

<route lang="yaml">
meta:
  auth: false
  layout: false
</route>
