<template>
  <div class="font-bold text-xl my-8">{{ t('withdraw.payment_methods') }}</div>

  <div class="w-80 rounded-3xl border border-primary-tint4 px-6 py-4">
    <div class="flex items-center">
      <img
        src="@/assets/images/bank.svg"
        alt="Bank account"
        class="w-20 h-20 rounded-lg object-cover"
      />
      <span class="font-bold text-lg px-4">Bank Account</span>
    </div>

    <div class="mt-3">
      <div class="flex justify-between items-center text-sm mb-2">
        <span class="text-neutral-5">{{ t('withdraw.processing_time') }}</span>
        <span class="text-neutral-4 font-bold">Instant - 24 hours</span>
      </div>

      <div class="flex justify-between items-center text-sm mb-2">
        <span class="text-neutral-5">{{ t('common.fee') }}</span>
        <span class="text-neutral-4 font-bold">0%</span>
      </div>

      <div class="flex justify-between items-center text-sm mb-2">
        <span class="text-neutral-5">{{ t('common.limits') }}</span>
        <span class="text-neutral-4 font-bold">10 - 20,000 USD</span>
      </div>
    </div>

    <button
      @click="isOpenWithdrawRequestDialog = true"
      class="w-full rounded-lg border border-primary text-primary p-3 font-bold text-xs mt-6 cursor-pointer"
    >
      {{ t('withdraw.withdraw_now') }}
    </button>
  </div>

  <WithdrawRequestDialog
    :isOpenWithdrawRequestDialog="isOpenWithdrawRequestDialog"
    @update:isOpenWithdrawRequestDialog="isOpenWithdrawRequestDialog = $event"
    @close="isOpenWithdrawRequestDialog = false"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

import { WithdrawRequestDialog } from '@/modules/withdraw/index';

const { t } = useI18n();
const isOpenWithdrawRequestDialog = ref(false);
</script>
