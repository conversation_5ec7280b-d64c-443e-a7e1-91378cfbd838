<template>
  <div class="font-bold text-xl my-8">
    {{ t('transaction.transaction_history') }}
  </div>
  <DataStateWrapper
    :isLoading="dataState.isLoading.value"
    :hasError="dataState.hasError.value"
    :hasNoData="dataState.hasNoData.value"
    :isRefetching="dataState.isRefetching.value"
    :errorConfig="dataState.errorConfig.value"
    :emptyConfig="dataState.emptyConfig.value"
  >
    <div class="flex justify-between items-center">
      <div class="flex gap-2">
        <DPopper
          placement="bottom-end"
          offset-y="22"
          :arrow="false"
          append-to-body
        >
          <template #default>
            <button class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9">
              <div class="flex items-center justify-between">
                <span class="text-xs">
                  {{ t('common.all_days') }}
                </span>
                <DaryaArrowDownIcon size="16" />
              </div>
            </button>
          </template>
          <template #content>
            <div class="bg-lightMood-2 shadow-md rounded-lg p-3">
              <div class="flex">
                <!-- Sidebar with Presets -->
                <div class="sidebar bg-lightMood-2 pt-10 p-2 w-32">
                  <button
                    class="block w-full text-left text-xs font-medium text-neutral-5 py-1 hover:text-primary cursor-pointer mb-3"
                    @click=""
                  >
                    {{ t('common.today') }}
                  </button>
                  <button
                    class="block w-full text-left text-xs text-neutral-5 font-medium py-1 hover:text-primary cursor-pointer mb-3"
                    @click=""
                  >
                    {{ t('common.yesterday') }}
                  </button>
                  <button
                    class="block w-full text-left text-xs text-neutral-5font-medium py-1 hover:text-primary cursor-pointer mb-3"
                    @click=""
                  >
                    {{ t('common.last_seven_days') }}
                  </button>
                  <button
                    class="block w-full text-left text-xs text-neutral-5 font-medium py-1 hover:text-primary cursor-pointer mb-3"
                    @click=""
                  >
                    {{ t('common.custom') }}
                  </button>
                </div>
                <!-- Calendar -->
                <div class="calendar flex-1">
                  <VCalendar
                    v-model="selectedRange"
                    :columns="columnsDate"
                    :initial-page="{ month: 1, year: 2025 }"
                    :color="selectedColor"
                    :attributes="attributes"
                  />
                </div>
              </div>
              <!-- Buttons -->
              <div class="buttons flex justify-end mt-3 gap-2">
                <button
                  class="px-4 py-2 w-24 font-bold text-sm border border-neutral-10 rounded-lg cursor-pointer"
                  @click="cancel"
                >
                  {{ t('common.cancel') }}
                </button>
                <button
                  class="px-4 py-2 font-bold text-sm w-24 bg-primary text-white rounded-lg cursor-pointer"
                  @click=""
                >
                  {{ t('common.apply') }}
                </button>
              </div>
            </div>
          </template>
        </DPopper>
        <DPopper placement="bottom" :arrow="false" offset-y="22" append-to-body>
          <template #default>
            <button class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9">
              <div class="flex items-center justify-between">
                <span class="text-xs">
                  {{ t('transaction.all_transaction_types') }}
                </span>
                <DaryaArrowDownIcon size="16" />
              </div>
            </button>
          </template>
          <template #content>
            <div
              class="bg-white bg-neutral-14 shadow-md rounded-lg py-1.5 w-44"
            >
              <ul class="flex flex-col px-2">
                <li
                  v-for="transActionType in ['Purchase', 'Payout', 'Reward']"
                  :key="transActionType"
                  @click="transActionTypeFilter = transActionType"
                  :class="[
                    'px-2 py-3 rounded-2xl text-left cursor-pointer',
                    transActionTypeFilter === transActionType
                      ? 'bg-primary/15'
                      : 'hover:border border-primary-tint2',
                  ]"
                  class="mb-2"
                >
                  {{ transActionType }}
                </li>
              </ul>

              <div class="flex justify-center mt-2 border-t border-neutral/20">
                <button class="text-primary py-2 cursor-pointer">
                  {{ t('common.reset_all') }}
                </button>
              </div>
            </div>
          </template>
        </DPopper>
        <DPopper placement="bottom" :arrow="false" offset-y="22" append-to-body>
          <template #default>
            <button class="w-52 px-4 py-1.5 rounded-lg border border-neutral-9">
              <div class="flex items-center justify-between">
                <span class="text-xs">{{ t('transaction.all_statuses') }}</span>
                <DaryaArrowDownIcon size="16" />
              </div>
            </button>
          </template>
          <template #content>
            <div
              class="bg-white bg-neutral-14 shadow-md rounded-lg py-1.5 w-44"
            >
              <ul class="flex flex-col px-2">
                <li
                  @click="sideFilter = 'Done'"
                  :class="[
                    'px-2 py-3 rounded-2xl text-left cursor-pointer',
                    sideFilter === 'Done'
                      ? 'bg-primary/15 '
                      : 'hover:border border-primary-tint2',
                  ]"
                  class="px-2 py-3 hover:border border-primary-tint2 rounded-2xl text-left cursor-pointer"
                >
                  <span> {{ t('common.done') }} </span>
                </li>

                <li
                  @click="sideFilter = 'Pending'"
                  :class="[
                    'px-2 py-3 rounded-2xl text-left cursor-pointer',
                    sideFilter === 'Pending'
                      ? 'bg-primary/15 '
                      : 'hover:border border-primary-tint2',
                  ]"
                  class="px-2 py-3 hover:border border-primary-tint2 rounded-2xl text-left mt-3 cursor-pointer"
                >
                  <span> {{ t('common.pending') }} </span>
                </li>
                <li
                  @click="sideFilter = 'Rejected'"
                  :class="[
                    'px-2 py-3 rounded-2xl text-left cursor-pointer',
                    sideFilter === 'Rejected'
                      ? 'bg-primary/15 '
                      : 'hover:border border-primary-tint2',
                  ]"
                  class="px-2 py-3 hover:border border-primary-tint2 rounded-2xl text-left mt-3 cursor-pointer"
                >
                  <span> {{ t('common.rejected') }} </span>
                </li>
              </ul>

              <div class="flex justify-center mt-2 border-t border-neutral/20">
                <button
                  @click="sideFilter = null"
                  class="text-primary py-2 cursor-pointer"
                >
                  {{ t('common.reset_all') }}
                </button>
              </div>
            </div>
          </template>
        </DPopper>
      </div>

      <button
        class="w-44 py-2 px-4 rounded-lg border border-primary cursor-pointer"
      >
        <span class="text-primary font-bold text-xs">
          {{ t('common.support') }}
        </span>
      </button>
    </div>

    <div class="rounded-3xl border border-neutral-13 py-4 px-6 mt-4">
      <div class="flex items-center w-full mb-6">
        <span class="text-neutral-5 text-xl font-medium pr-2">2025</span>
        <div class="flex-1 bg-neutral-13 h-[0.5px]"></div>
      </div>

      <div
        v-for="transaction in transactionList"
        :key="transaction.id"
        @click="handleOpenTransactionDetail(transaction.id)"
        class="rounded-lg border-[0.5px] border-neutral-11 py-2 px-6 mb-3 hover:border-primary-tint2"
      >
        <div class="flex justify-between items-center cursor-pointer">
          <div class="flex items-center">
            <component
              :is="getTypeIcon(transaction.transactionType)"
              size="32"
              :class="getTypeIconClass(transaction.transactionType)"
            />

            <div class="flex flex-col ps-6">
              <span class="text-lg font-bold pb-2">
                {{ getTypeLabel(transaction.transactionType) }}
              </span>
              <span class="text-neutral-8 text-base">
                {{ d(transaction.createDate) }}
              </span>
              <span class="text-neutral-8 text-base">
                {{ t('transaction.invoice_id') }} {{ transaction.refrenceId }}
              </span>
            </div>
          </div>

          <div class="flex">
            <DaryaOutlineCardIcon class="text-neutral-5" size="24" />

            <div class="flex flex-col ms-4">
              <span class="text-neutral-5 text-xl font-medium pb-2">
                {{ transaction.paymentInfoUiModel?.label }}
              </span>
              <span class="text-neutral-8 text-base">
                {{ transaction.paymentInfoUiModel?.details }}
              </span>
            </div>
          </div>

          <button
            class="flex items-center rounded-lg p-2 text-white"
            :class="getStatusButtonClass(transaction.status)"
          >
            <component :is="getStatusIcon(transaction.status)" size="16" />
            <span class="text-xs ps-1">{{ transaction.status }}</span>
          </button>

          <span class="text-neutral-5 font-bold text-base">
            {{ transaction.amount }} {{ transaction.currency }}
          </span>
        </div>
      </div>
    </div>
  </DataStateWrapper>
  <TransactionHistoryDialog
    :isOpenTransactionHistoryDetail="isOpenTransactionHistoryDetail"
    @update:isOpenTransactionHistoryDetail="
      isOpenTransactionHistoryDetail = $event
    "
    :transActionDetailData="transActionDetailData"
    :isLoading="transactionDetailState.isLoading.value"
    :hasError="transactionDetailState.hasError.value"
    :error="transactionDetailErrorObj"
    :onRetry="() => getTransActionDetailById()"
    @close="isOpenTransactionHistoryDetail = false"
  />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useScreens } from 'vue-screen-utils';
import { useI18n } from 'vue-i18n';

import {
  TransactionHistoryDialog,
  useGetTransactionDetailById,
  useGetAllTransaction,
} from '@/modules/transaction/index';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';
const transActionId = ref();
const sideFilter = ref();
const transActionTypeFilter = ref();
const isOpenTransactionHistoryDetail = ref(false);
const selectedRange = ref<{ start: Date | null; end: Date | null }>({
  start: null,
  end: null,
});
const attributes = ref([
  {
    highlight: {
      start: { fillMode: 'outline' },
      base: { fillMode: 'light' },
      end: { fillMode: 'outline' },
    },
    dates: { start: new Date(2025, 0, 14), end: new Date(2025, 0, 18) },
  },
]);
const selectedColor = ref('indigo');

const { transactionList, status, isFetching, isError, error } =
  useGetAllTransaction();

const dataState = useDataState(
  status,
  transactionList,
  isFetching,
  isError,
  error,
  'transactions' // Empty state type
);
const {
  getTransActionDetailById,
  transActionDetailData,
  status: transactionDetailStatus,
  isFetching: transactionDetailFetching,
  isError: transactionDetailError,
  error: transactionDetailErrorObj
} = useGetTransactionDetailById(transActionId, false);

// Create data state for transaction detail dialog
const transactionDetailState = useDataState(
  transactionDetailStatus,
  transActionDetailData,
  transactionDetailFetching,
  transactionDetailError,
  transactionDetailErrorObj,
  'transactions'
);

const { t, d } = useI18n();
const { mapCurrent } = useScreens({
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
});
const columnsDate = mapCurrent({ lg: 2 }, 1);

const cancel = () => {};

const getStatusButtonClass = (status) => {
  if (status === 'Succeeded') return 'bg-success';
  if (status === 'Created') return 'bg-warning';
  if (status === 'Failed' || status === 'Canceled') return 'bg-error';

  return 'bg-neutral-5';
};

const getStatusIcon = (status) => {
  if (status === 'Succeeded') return 'DaryaBoldTickCircleIcon';
  if (status === 'Created') return 'DaryaBoldClockIcon';
  if (status === 'Failed' || status === 'Canceled')
    return 'DaryaBoldCloseCircleIcon';

  return 'DaryaBoldClockIcon';
};

const getTypeIconClass = (type) => {
  return type === 1 ? 'text-primary' : 'text-neutral-5';
};

const getTypeLabel = (type) => {
  if (type === 1) return 'Withdraw';
  if (type === 2) return 'Deposit';
};

const getTypeIcon = (type) => {
  // 1: Withdraw/Payout, 2: Deposit/Purchase, 3: Reward, etc.
  if (type === 1) return 'DaryaOutlineSendSqaureIcon';
  if (type === 2) return 'DaryaOutlineReceiveSquareIcon';
  // Add more as needed
  return 'DaryaOutlineReceiveSquareIcon';
};

const handleOpenTransactionDetail = (id) => {
  transActionId.value = id;
  getTransActionDetailById();
  isOpenTransactionHistoryDetail.value = true;
};
</script>
