<template>
  <div>
    <div class="flex gap-6">
      <div
        class="flex flex-col !bg-primary-tint8 rounded-3xl py-4 px-6 w-6/12 h-44"
      >
        <span class="text-2xl font-bold pb-1 pt-11">
          {{ t('home.banner.opportunity_text') }}
        </span>
        <span class="text-xm">{{ t('home.banner.rewarded_text') }}</span>
      </div>

      <DBox class="flex background-card py-4 rounded-3xl px-6 w-6/12 h-44">
        <div class="mr-16">
          <img
            src="@/assets/images/glossy.svg"
            class="w-36 h-36 rounded-full object-cover"
            alt="Glossy Image"
          />
        </div>

        <div class="flex flex-col">
          <span class="text-primary-tint7 font-light text-xs pt-8 pb-1">
            {{ t('home.banner.education') }}
          </span>
          <span>
            <span class="text-white text-2xl">
              {{ t('home.banner.learn_trading') }}
            </span>
            <span class="text-primary-tint4 text-2xl">
              {{ t('home.banner.essentials') }}
            </span>
          </span>
          <span>
            <span class="text-white text-sm">{{ t('home.banner.build') }}</span>
            <span class="text-primary-tint4 text-sm">
              {{ t('home.banner.expertise') }}
            </span>
            <span class="text-white text-sm">
              {{ t('home.banner.from_scratch') }}
            </span>
          </span>
        </div>
      </DBox>
    </div>

    <!-- Accounts Section -->
    <div class="flex justify-between mt-14 mb-5">
      <h5 class="font-bold text-xl">{{ t('common.accounts') }}</h5>
      <div>
        <router-link v-slot="{ navigate }" to="/challenges">
          <button
            class="bg-primary h-14 px-6 py-4 rounded-lg mr-4 cursor-pointer"
            @click="navigate"
          >
            <span class="text-white font-bold">
              {{ t('common.start_challenge') }}
            </span>
          </button>
        </router-link>

        <button
          class="w-40 h-14 px-6 py-4 rounded-lg border-primary border-2 cursor-pointer"
        >
          <span class="text-primary font-bold">{{ t('home.free_trial') }}</span>
        </button>
      </div>
    </div>

    <!-- Data State Wrapper -->
    <DataStateWrapper
      :isLoading="dataState.isLoading.value"
      :hasError="dataState.hasError.value"
      :hasNoData="dataState.hasNoData.value"
      :isRefetching="dataState.isRefetching.value"
      :errorConfig="dataState.errorConfig.value"
      :emptyConfig="dataState.emptyConfig.value"
    >
      <!-- Tabs with Data -->
      <DTab :tabs="tabs" v-model="activeTab">
        <!-- Active Accounts -->
        <template #tab1>
          <div v-if="hasActiveAccounts">
            <section v-if="activeFreeTrialAccounts.length" class="mt-12">
              <h6 class="text-neutral-4 text-base font-medium mb-8">
                {{ t('home.free_trial') }}
              </h6>
              <div class="flex gap-6">
                <AccountCard
                  v-for="account in activeFreeTrialAccounts"
                  :key="account.id"
                  :account="account"
                />
              </div>
            </section>
            <section v-if="activeChallengeAccounts.length" class="mt-12">
              <h6 class="text-neutral-4 text-base font-medium mb-8">
                {{ t('home.challenge_account') }}
              </h6>
              <div class="flex gap-6">
                <AccountCard
                  v-for="account in activeChallengeAccounts"
                  :key="account.id"
                  :account="account"
                />
              </div>
            </section>
          </div>

          <!-- Empty state for active accounts -->
          <EmptyState
            v-else
            type="no-data"
            title="No Active Accounts"
            message="You don't have any active accounts at the moment."
          />
        </template>

        <!-- Breached Accounts -->
        <template #tab2>
          <div v-if="hasBreachedAccounts">
            <section v-if="breachedFreeTrialAccounts.length" class="mt-12">
              <h6 class="text-neutral-4 text-base font-medium mb-8">
                {{ t('home.free_trial') }}
              </h6>
              <div class="flex gap-6">
                <AccountCard
                  v-for="account in breachedFreeTrialAccounts"
                  :key="account.id"
                  :account="account"
                />
              </div>
            </section>
            <section v-if="breachedChallengeAccounts.length" class="mt-12">
              <h6 class="text-neutral-4 text-base font-medium mb-8">
                {{ t('home.challenge_account') }}
              </h6>
              <div class="flex gap-6">
                <AccountCard
                  v-for="account in breachedChallengeAccounts"
                  :key="account.id"
                  :account="account"
                />
              </div>
            </section>
          </div>

          <!-- Empty state for breached accounts -->
          <EmptyState
            v-else
            type="no-data"
            title="No Breached Accounts"
            message="Great! You don't have any breached accounts."
            imageWidth="150"
            imageHeight="150"
          />
        </template>
      </DTab>
    </DataStateWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { useAuth } from '@/modules/user';
import { useGetAllAccountByUserId, AccountCard } from '@/modules/account';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const { user } = useAuth();

const { allAccountData, status, isFetching, isError, error } =
  useGetAllAccountByUserId(user.value?.nameid ?? '', true);

const { t } = useI18n();

// Data state management - single composable handles everything
const dataState = useDataState(
  status,
  allAccountData,
  isFetching,
  isError,
  error,
  'accounts' // Empty state type
);

// Tab-specific empty state logic
const hasActiveAccounts = computed(() => {
  return activeAccounts.value.length > 0;
});

const hasBreachedAccounts = computed(() => {
  return breachedAccounts.value.length > 0;
});

const activeTab = ref('tab1');
const tabs = [
  { name: 'tab1', label: 'Active' },
  { name: 'tab2', label: 'Breached' },
];

const ACCOUNT_STATUS = {
  ACTIVE: 1,
  BREACHED: 3,
};

const activeAccounts = computed(() =>
  allAccountData.value.filter(
    (account) => account.status === ACCOUNT_STATUS.ACTIVE
  )
);
const breachedAccounts = computed(() =>
  allAccountData.value.filter(
    (account) => account.status === ACCOUNT_STATUS.BREACHED
  )
);

const activeFreeTrialAccounts = computed(() =>
  activeAccounts.value.filter((a) => a.planTypeId === 1).slice(0, 3)
);
const activeChallengeAccounts = computed(() =>
  activeAccounts.value.filter((a) => a.planTypeId !== 1).slice(0, 3)
);
const breachedFreeTrialAccounts = computed(() =>
  breachedAccounts.value.filter((a) => a.planTypeId === 1).slice(0, 3)
);
const breachedChallengeAccounts = computed(() =>
  breachedAccounts.value.filter((a) => a.planTypeId !== 1).slice(0, 3)
);
</script>

<style scoped>
.background-card {
  background: linear-gradient(192deg, #0e1766 9.22%, #0a0226 107.15%);
}
</style>
