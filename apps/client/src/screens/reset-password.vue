<template>
  <div class="flex flex-col items-center w-full pt-14 overflow-y-auto h-screen">
    <img src="@/assets/images/logo.svg" alt="Logo" class="w-24 h-24" />
    <span class="text-3xl font-semibold pt-5">
      {{ t('login.reset_password') }}
    </span>

    <div class="w-96 mt-8">
      <VeeForm v-slot="{ handleSubmit }" class="space-y-6">
        <form
          autocomplete="off"
          @submit="handleSubmit($event, onSubmit)"
          class="space-y-6"
        >
          <VeeField
            v-slot="{ field, errorMessage }"
            name="password"
            v-model="formModel.newPassword"
          >
            <DInput v-bind="field" placeholder="Password" />
          </VeeField>
          <VeeField
            v-slot="{ field, errorMessage }"
            name="rePassword"
            v-model="formModel.confirmNewPassword"
          >
            <DInput v-bind="field" placeholder="Re-Password" />
          </VeeField>
          <button
            type="submit"
            class="flex w-full justify-center items-center gap-2 text-white text-xs font-bold px-6 py-4 rounded-lg bg-primary"
          >
            {{ t('common.continue') }}
          </button>
        </form>
      </VeeForm>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

import { useResetPasswordMutation } from '@/modules/user/index';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();

const formModel = reactive({
  token: String(
    Array.isArray(route.query.token)
      ? route.query.token[0]
      : (route.query.token ?? '')
  ),
  newPassword: '',
  confirmNewPassword: '',
  username: String(
    Array.isArray(route.query.email)
      ? route.query.email[0]
      : (route.query.email ?? '')
  ),
});

const handleSuccessResetPassword = () => {
  router.push('./login');
};

const handleErrorResetPassword = () => {};

const { resetPassword } = useResetPasswordMutation({
  onSuccess: handleSuccessResetPassword,
  onError: handleErrorResetPassword,
});

const onSubmit = () => {
  resetPassword(formModel);
};
</script>

<route lang="yaml">
meta:
  auth: false
  layout: false
</route>
