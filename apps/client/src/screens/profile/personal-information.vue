<template>
  <!-- Loading State -->
  <div
    v-if="formState.isLoading"
    class="flex justify-center items-center py-20"
  >
    <div class="flex flex-col items-center gap-4">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"
      ></div>
      <span class="text-neutral-5">Loading identity information...</span>
    </div>
  </div>

  <!-- Error State -->
  <div v-else-if="formState.hasError" class="flex flex-col items-center py-20">
    <div class="text-center max-w-md">
      <div class="mb-6">
        <img
          :src="`/images/${formState.errorConfig.image}`"
          :alt="formState.errorConfig.alt"
          class="w-32 h-32 mx-auto mb-4"
        />
      </div>
      <h3 class="text-lg font-semibold text-neutral-2 mb-2">
        {{ formState.errorConfig.title }}
      </h3>
      <p class="text-neutral-5 mb-6">
        {{ formState.errorConfig.message }}
      </p>
      <button
        @click="retryLoadData"
        class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-shade1 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>

  <!-- Form Content (shown when not loading and no error, even if data is empty) -->
  <div v-else-if="formState.showForm">
    <div class="flex justify-between items-center">
      <div class="flex flex-col">
        <div class="flex items-center">
          <button @click="handleGoBack" aria-label="Go back" class="p-1">
            <DaryaOutlineArrowLeftLongIcon class="text-neutral-6" />
          </button>
          <span class="text-base font-bold ps-2">Proof of Identification</span>
        </div>

        <span class="pt-3 text-neutral-5 text-sm">
          Please enter your identity information for review.
        </span>
      </div>

      <div v-if="proofOfIdentificationData?.verificationStatusTypeId">
        <span class="me-3">Verification Status:</span>
        <DChip variant="outline" :color="chipColor" :label="chipLabel" />
        <span
          v-if="proofOfIdentificationData?.verificationStatusTypeId === 5"
          class="font-bold text-[13px] text-primary ps-1 cursor-pointer"
          @click="isOpenRejectionReasonDialog = true"
        >
          See why?
        </span>
      </div>
    </div>

    <VeeForm v-slot="{ meta, handleSubmit }">
      <form autocomplete="off" @submit.prevent="() => onSubmit(meta)">
        <div class="grid grid-cols-1 mt-11 md:grid-cols-3">
          <div
            class="flex flex-col gap-y-5 py-4 px-6 border border-neutral-13 rounded-2xl me-3"
          >
            <VeeField
              v-slot="{ field, errorMessage }"
              name="firstName"
              v-model="formModel.firstName"
              :rules="{ required: true }"
            >
              <DInput
                v-bind="field"
                type="text"
                label="First Name"
                placeholder="First Name"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="lastName"
              v-model="formModel.lastName"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Last Name"
                placeholder="Last Name"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>

            <VeeField v-slot="{ field, errorMessage }" name="gender">
              <DSelectBox
                v-bind="field"
                v-model="formModel.genderTypeId"
                label="Gender"
                :options="genderListOptions"
                placeholder="Gender"
                required
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>

            <VeeField v-slot="{ field, errorMessage }" name="birthCountryId">
              <DSelectBox
                v-bind="field"
                v-model="formModel.birthCountryId"
                label="Country of birth"
                :options="countryListOptions"
                placeholder="Country of birth"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>

            <VeeField
              v-model="formModel.dateOfBirth"
              v-slot="{ field, errorMessage }"
              name="dateOfBirth"
            >
              <DInput
                v-bind="field"
                type="date"
                label="Date Of Birth"
                placeholder="Date Of Birth"
                :max="new Date().toISOString().split('T')[0]"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>
          </div>

          <div
            class="px-6 py-4 border border-neutral-13 rounded-2xl"
            :class="isPassport ? 'md:col-span-1' : 'md:col-span-2'"
          >
            <div class="flex justify-self-end w-48">
              <VeeField
                v-slot="{ field, errorMessage }"
                name="proofOfIdentificationTypeId"
              >
                <DSelectBox
                  v-bind="field"
                  v-model="formModel.proofOfIdentificationTypeId"
                  :options="documentListOptions"
                  placeholder="Document Type"
                  :disabled="!isFormEditable"
                />
              </VeeField>
            </div>

            <div
              class="grid grid-cols-1 mt-8"
              :class="isPassport ? 'md:grid-cols-1' : 'md:grid-cols-2 gap-5'"
            >
              <div
                class="flex flex-col items-center border border-dashed border-primary-tint6 rounded-2xl"
                :class="previewFront ? 'py-0 px-0' : 'py-8 px-16'"
              >
                <template v-if="previewFront">
                  <div
                    class="relative w-full h-60 md:h-[22.5rem] rounded-2xl overflow-hidden"
                  >
                    <img
                      :src="previewFront"
                      class="object-cover w-full h-full"
                      alt="Front side preview"
                    />
                    <button
                      v-if="isFormEditable"
                      class="flex px-2 py-1 w-24 absolute bottom-2 right-2 bg-neutral/50 text-error-tint1 font-bold rounded-lg shadow cursor-pointer"
                      @click="removePreviewImage('front')"
                    >
                      <DaryaOutlineTrashIcon aria-hidden="true" />
                      <span class="ps-1">{{ t('common.delete') }}</span>
                    </button>
                  </div>
                </template>
                <template v-else>
                  <img
                    src="@/assets/images/upload-document.svg"
                    class="w-28 h-28"
                    alt=""
                  />
                  <span class="pt-6 text-sm font-bold">
                    {{
                      isPassport ? 'Upload Passport' : 'Upload Front of ID Card'
                    }}
                  </span>
                  <span class="pt-2 pb-12 text-xs text-neutral-9">
                    Make sure the image is clear and shows all details
                  </span>
                  <input
                    type="file"
                    ref="fileInputFront"
                    accept="image/*"
                    class="hidden"
                    @change="onFileChanged('front', $event)"
                    id="file-input-front"
                  />
                  <DButton
                    v-if="isFormEditable"
                    label="Upload Image"
                    class="w-72"
                    @click="triggerFileInput('front')"
                    aria-label="Upload front side image"
                  />
                </template>
              </div>

              <template v-if="!isPassport">
                <div
                  class="flex flex-col items-center border border-dashed border-primary-tint6 rounded-2xl"
                  :class="previewBack ? 'py-0 px-0' : 'py-8 px-16'"
                >
                  <template v-if="previewBack">
                    <div
                      class="relative w-full h-60 md:h-[22.5rem] rounded-2xl overflow-hidden"
                    >
                      <img
                        :src="previewBack"
                        class="object-cover w-full h-full"
                        alt="Back side preview"
                      />
                      <button
                        v-if="isFormEditable"
                        class="flex px-2 py-1 w-24 absolute bottom-2 right-2 bg-neutral/50 text-error-tint1 font-bold rounded-lg shadow cursor-pointer"
                        @click="removePreviewImage('back')"
                      >
                        <DaryaOutlineTrashIcon aria-hidden="true" />
                        <span class="ps-1">{{ t('common.delete') }}</span>
                      </button>
                    </div>
                  </template>
                  <template v-else>
                    <img
                      src="@/assets/images/upload-document.svg"
                      class="w-28 h-28"
                      alt=""
                    />
                    <span class="pt-6 text-sm font-bold"
                      >Upload Back of ID Card</span
                    >
                    <span class="pt-2 pb-12 text-xs text-neutral-9">
                      Make sure the image is clear and shows all details
                    </span>
                    <input
                      type="file"
                      ref="fileInputBack"
                      accept="image/*"
                      class="hidden"
                      @change="onFileChanged('back', $event)"
                      id="file-input-back"
                    />
                    <DButton
                      v-if="isFormEditable"
                      label="Upload Image"
                      class="w-72"
                      @click="triggerFileInput('back')"
                      aria-label="Upload back side image"
                    />
                  </template>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- Hidden fields for image validation -->
        <VeeField
          name="imageFront"
          :rules="{ required: true }"
          v-model="formModel.imageFront"
          type="hidden"
        />
        <VeeField
          name="imageBack"
          :rules="{ required: !isPassport }"
          v-model="formModel.imageBack"
          type="hidden"
        />

        <div class="grid grid-cols-1 mt-6 md:grid-cols-3">
          <div :class="isPassport ? 'md:col-span-1' : 'md:col-span-2'" />
          <div class="flex justify-end">
            <DButton
              size="large"
              class="w-36"
              type="submit"
              label="Save"
              :disabled="!isFormEditable"
            />
          </div>
        </div>
      </form>
    </VeeForm>
  </div>

  <ConfirmDocumentDialog
    :isOpenConfirmDialog="isOpenConfirmDialog"
    @update:isOpenConfirmDialog="isOpenConfirmDialog = $event"
    @close="handleCloseConfirmDialog"
    @submitForm="submitForm"
  />
  <SuccessSubmitDialog
    :isOpenSuccessSubmitDialog="isOpenSuccessSubmitDialog"
    @update:isOpenSuccessSubmitDialog="isOpenSuccessSubmitDialog = $event"
    @close="handleCloseSuccessDialog"
  />
  <RejectionReasonDialog
    :isOpenRejectionReasonDialog="isOpenRejectionReasonDialog"
    :message="proofOfIdentificationData?.message"
    @update:isOpenRejectionReasonDialog="isOpenRejectionReasonDialog = $event"
    @close="isOpenRejectionReasonDialog = false"
  />
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useGetAllCountry } from '@/modules/user';
import { useRouter, useRoute } from 'vue-router';
import { DButton } from '@libs/darya-design-system';
import { useI18n } from 'vue-i18n';
import {
  useGetProofOfIdentificationById,
  useGetVerificationStatusTypeEnum,
  useGetProofOfIdentificationTypeEnum,
  useCreateProofOfIdentificationMutation,
  ConfirmDocumentDialog,
  SuccessSubmitDialog,
  type ProofOfIdentificationFormModel,
  type SelectOption,
  GENDER_OPTIONS,
  STATUS_COLOR_MAP,
  requiresBackImage,
  processUploadedFile,
  getStatusColor,
  getStatusText,
  mapApiFieldsToImages,
  getDocumentTypeConfig,
  isDocumentObject,
  documentObjectToDataUrl,
  RejectionReasonDialog,
  mapImagesWithFileNamesToApiFields, // <-- add this import
} from '@/modules/kyc';
import { isUndefined } from 'v-calendar/dist/types/src/utils/helpers';

function formatDateForApi(date: string): string {
  if (!date) return '';
  if (date.includes('T')) return date;
  return `${date}T00:00:00.0000000`;
}

const { t } = useI18n();

const fileInputFront = ref<HTMLInputElement>();
const fileInputBack = ref<HTMLInputElement>();
const previewFront = ref<string | null>(null);
const previewBack = ref<string | null>(null);
const isOpenConfirmDialog = ref(false);
const isOpenSuccessSubmitDialog = ref(false);
const isOpenRejectionReasonDialog = ref(false);

const formModel = reactive<ProofOfIdentificationFormModel>({
  firstName: '',
  lastName: '',
  genderTypeId: 1,
  birthCountryId: null,
  dateOfBirth: '',
  proofOfIdentificationTypeId: null,
  imageFront: '',
  imageBack: '',
  verificationStatusTypeId: 1,
  frontOfIdCard: '',
  frontOfIdCardFileName: '',
  backOfIdCard: '',
  backOfIdCardFileName: '',
  passport: '',
  passportFileName: '',
  frontOfDrivingLicense: '',
  frontOfDrivingLicenseFileName: '',
  backOfDrivingLicense: '',
  backOfDrivingLicenseFileName: '',
});

const router = useRouter();
const route = useRoute();
const {
  proofOfIdentificationData,
  getProofOfIdentificationData,
  status,
  isFetching,
  isError,
  error,
} = useGetProofOfIdentificationById(route.query.actionId as string, true);

// Custom state management for form - show form even when data is empty
const formState = computed(() => {
  return {
    isLoading: isFetching.value && status.value === 'pending',
    hasError: isError.value,
    showForm: !isFetching.value && !isError.value, // Show form when not loading and no error
    errorConfig: {
      title: 'Unable to Load Identity Information',
      message:
        'We encountered an issue loading your identity information. Please try again.',
      image: 'error-state.svg',
      alt: 'Error loading identity information',
    },
  };
});

// Retry function
const retryLoadData = () => {
  getProofOfIdentificationData();
};
const { verificationStatusTypeEnum } = useGetVerificationStatusTypeEnum();
const { proofOfIdentificationTypeEnum } = useGetProofOfIdentificationTypeEnum();
const { createProofOfIdentification } = useCreateProofOfIdentificationMutation({
  onSuccess: () => {
    isOpenSuccessSubmitDialog.value = true;
    getProofOfIdentificationData();
  },
  onError: (error) => {
    console.error('Submission failed:', error);
  },
});
const { countryList } = useGetAllCountry();

const countryListOptions = computed<SelectOption[]>(() => {
  return (
    countryList.value?.map((x) => ({ label: x.fullName, value: x.id })) ?? []
  );
});

const genderListOptions = GENDER_OPTIONS;

const documentListOptions = computed<SelectOption[]>(() => {
  return (
    proofOfIdentificationTypeEnum.value?.map((x) => ({
      label: x.text,
      value: Number(x.value),
    })) ?? []
  );
});

const isPassport = computed(() =>
  formModel.proofOfIdentificationTypeId
    ? !requiresBackImage(formModel.proofOfIdentificationTypeId)
    : false
);

const handleGoBack = () => {
  router.back();
};

const onFileChanged = async (side: 'front' | 'back', e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0];
  if (!file) return;

  try {
    const uploadInfo = await processUploadedFile(file);

    const prev = side === 'front' ? previewFront.value : previewBack.value;
    if (prev) URL.revokeObjectURL(prev);

    if (side === 'front') {
      previewFront.value = uploadInfo.preview;
      formModel.imageFront = uploadInfo.base64;

      // Set file name based on document type
      if (formModel.proofOfIdentificationTypeId === 3) {
        formModel.passportFileName = uploadInfo.fileName;
      } else if (formModel.proofOfIdentificationTypeId === 1) {
        formModel.frontOfIdCardFileName = uploadInfo.fileName;
      } else if (formModel.proofOfIdentificationTypeId === 2) {
        formModel.frontOfDrivingLicenseFileName = uploadInfo.fileName;
      }
    } else {
      previewBack.value = uploadInfo.preview;
      formModel.imageBack = uploadInfo.base64;

      // Set file name based on document type
      if (formModel.proofOfIdentificationTypeId === 1) {
        formModel.backOfIdCardFileName = uploadInfo.fileName;
      } else if (formModel.proofOfIdentificationTypeId === 2) {
        formModel.backOfDrivingLicenseFileName = uploadInfo.fileName;
      }
    }
  } catch (error) {
    console.error('File upload error:', error);
    alert(error instanceof Error ? error.message : 'Failed to upload file');
  }
};

const triggerFileInput = (side: 'front' | 'back') => {
  if (side === 'front') fileInputFront.value?.click();
  else fileInputBack.value?.click();
};

const removePreviewImage = (side: 'front' | 'back') => {
  if (side === 'front') {
    URL.revokeObjectURL(previewFront.value!);
    previewFront.value = null;
    formModel.imageFront = '';

    // Clear file name based on document type
    if (formModel.proofOfIdentificationTypeId === 3) {
      formModel.passportFileName = '';
    } else if (formModel.proofOfIdentificationTypeId === 1) {
      formModel.frontOfIdCardFileName = '';
    } else if (formModel.proofOfIdentificationTypeId === 2) {
      formModel.frontOfDrivingLicenseFileName = '';
    }

    fileInputFront.value!.value = '';
  } else {
    URL.revokeObjectURL(previewBack.value!);
    previewBack.value = null;
    formModel.imageBack = '';

    // Clear file name based on document type
    if (formModel.proofOfIdentificationTypeId === 1) {
      formModel.backOfIdCardFileName = '';
    } else if (formModel.proofOfIdentificationTypeId === 2) {
      formModel.backOfDrivingLicenseFileName = '';
    }

    fileInputBack.value!.value = '';
  }
};

const chipColor = computed(() => {
  const status = proofOfIdentificationData.value?.verificationStatusTypeId;
  return status ? getStatusColor(status) : 'neutral';
});

const chipLabel = computed(() => {
  const status = proofOfIdentificationData.value?.verificationStatusTypeId;
  return status ? getStatusText(verificationStatusTypeEnum.value, status) : '';
});

function normalizeDate(date: string): string {
  if (!date) return '';
  // If date is already in YYYY-MM-DD, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) return date;
  // If date is in YYYY-MM-DDTHH:mm:ss format
  if (date.includes('T')) return date.split('T')[0];
  // If date is in MM/DD/YYYY format
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(date)) {
    const [mm, dd, yyyy] = date.split('/');
    return `${yyyy}-${mm.padStart(2, '0')}-${dd.padStart(2, '0')}`;
  }
  return date;
}
const mapProofToForm = (data: ProofOfIdentificationFormModel) => {
  if (!data) return;

  // Map basic form fields
  formModel.firstName = data.firstName || '';
  formModel.lastName = data.lastName || '';
  formModel.genderTypeId = data.genderTypeId ?? 1;
  formModel.birthCountryId = data.birthCountryId ?? null;
  formModel.dateOfBirth = data.dateOfBirth
    ? normalizeDate(data.dateOfBirth)
    : '';
  formModel.proofOfIdentificationTypeId = data.proofOfIdentificationTypeId ?? 1;
  formModel.verificationStatusTypeId = data.verificationStatusTypeId ?? 1;

  // Store API-specific image fields and file names
  // Handle frontOfIdCard - could be DocumentObject or string
  if (isDocumentObject(data.frontOfIdCard)) {
    formModel.frontOfIdCard = data.frontOfIdCard.fileData;
    formModel.frontOfIdCardFileName = data.frontOfIdCard.fileName;
  } else {
    formModel.frontOfIdCard = data.frontOfIdCard;
    formModel.frontOfIdCardFileName = data.frontOfIdCardFileName;
  }

  // Handle backOfIdCard - could be DocumentObject or string
  if (isDocumentObject(data.backOfIdCard)) {
    formModel.backOfIdCard = data.backOfIdCard.fileData;
    formModel.backOfIdCardFileName = data.backOfIdCard.fileName;
  } else {
    formModel.backOfIdCard = data.backOfIdCard || '';
    formModel.backOfIdCardFileName = data.backOfIdCardFileName || '';
  }

  // Handle passport - could be DocumentObject or string
  if (isDocumentObject(data.passport)) {
    formModel.passport = data.passport.fileData;
    formModel.passportFileName = data.passport.fileName;
  } else {
    formModel.passport = data.passport;
    formModel.passportFileName = data.passportFileName;
  }

  // Handle frontOfDrivingLicense - could be DocumentObject or string
  if (isDocumentObject(data.frontOfDrivingLicense)) {
    formModel.frontOfDrivingLicense = data.frontOfDrivingLicense.fileData;
    formModel.frontOfDrivingLicenseFileName =
      data.frontOfDrivingLicense.fileName;
  } else {
    formModel.frontOfDrivingLicense = data.frontOfDrivingLicense;
    formModel.frontOfDrivingLicenseFileName =
      data.frontOfDrivingLicenseFileName;
  }

  // Handle backOfDrivingLicense - could be DocumentObject or string
  if (isDocumentObject(data.backOfDrivingLicense)) {
    formModel.backOfDrivingLicense = data.backOfDrivingLicense.fileData;
    formModel.backOfDrivingLicenseFileName = data.backOfDrivingLicense.fileName;
  } else {
    formModel.backOfDrivingLicense = data.backOfDrivingLicense;
    formModel.backOfDrivingLicenseFileName = data.backOfDrivingLicenseFileName;
  }

  // Map images to UI fields using utility function
  const { imageFront, imageBack } = mapApiFieldsToImages(data);
  formModel.imageFront = imageFront;
  formModel.imageBack = imageBack;

  // Set preview images with proper MIME type detection
  if (formModel.imageFront) {
    // Check if we have DocumentObject format for front image
    const frontImageData =
      data[
        getDocumentTypeConfig(formModel.proofOfIdentificationTypeId)
          .frontImageField
      ];
    if (isDocumentObject(frontImageData)) {
      previewFront.value = documentObjectToDataUrl(frontImageData);
    } else {
      previewFront.value = `data:image/jpeg;base64,${formModel.imageFront}`;
    }
  }

  if (formModel.imageBack) {
    // Check if we have DocumentObject format for back image
    const config = getDocumentTypeConfig(formModel.proofOfIdentificationTypeId);
    if (config.backImageField) {
      const backImageData = data[config.backImageField];
      if (isDocumentObject(backImageData)) {
        previewBack.value = documentObjectToDataUrl(backImageData);
      } else {
        previewBack.value = `data:image/jpeg;base64,${formModel.imageBack}`;
      }
    }
  }
};

const handleCloseConfirmDialog = () => {
  isOpenConfirmDialog.value = false;
};

const handleCloseSuccessDialog = () => {
  isOpenSuccessSubmitDialog.value = false;
};
const isFormEditable = computed(() => {
  const status = proofOfIdentificationData.value?.verificationStatusTypeId;
  return status === 2 || status === 5;
});

const submitForm = () => {
  formModel.dateOfBirth = formatDateForApi(formModel.dateOfBirth ?? '');
  const apiPayload = mapImagesWithFileNamesToApiFields(formModel);

  createProofOfIdentification(apiPayload);
  handleCloseConfirmDialog();
};

const onSubmit = (meta?: any) => {
  // Validate form before opening dialog
  if (meta?.valid) {
    isOpenConfirmDialog.value = true;
  }
};

watch(
  () => formModel.proofOfIdentificationTypeId,
  (newType, oldType) => {
    if (newType !== oldType) {
      // Clear all preview images and file inputs
      if (previewFront.value) {
        URL.revokeObjectURL(previewFront.value);
        previewFront.value = null;
      }
      if (previewBack.value) {
        URL.revokeObjectURL(previewBack.value);
        previewBack.value = null;
      }

      // Clear file inputs
      if (fileInputFront.value) {
        fileInputFront.value.value = '';
      }
      if (fileInputBack.value) {
        fileInputBack.value.value = '';
      }

      // Clear all image fields and file names
      formModel.imageFront = '';
      formModel.imageBack = '';
      formModel.frontOfIdCard = '';
      formModel.frontOfIdCardFileName = '';
      formModel.backOfIdCard = '';
      formModel.backOfIdCardFileName = '';
      formModel.passport = '';
      formModel.passportFileName = '';
      formModel.frontOfDrivingLicense = '';
      formModel.frontOfDrivingLicenseFileName = '';
      formModel.backOfDrivingLicense = '';
      formModel.backOfDrivingLicenseFileName = '';

      // If we have existing data, remap it to the new document type
      if (proofOfIdentificationData.value && newType) {
        // Remap the existing data to the new document type
        const { imageFront, imageBack } = mapApiFieldsToImages({
          ...proofOfIdentificationData.value,
          proofOfIdentificationTypeId: newType,
        });

        formModel.imageFront = imageFront;
        formModel.imageBack = imageBack;

        // Set preview images based on the new document type
        if (imageFront) {
          const config = getDocumentTypeConfig(newType);
          const frontImageData =
            proofOfIdentificationData.value[config.frontImageField];
          if (isDocumentObject(frontImageData)) {
            previewFront.value = documentObjectToDataUrl(frontImageData);
          } else {
            previewFront.value = `data:image/jpeg;base64,${imageFront}`;
          }
        }

        if (imageBack && !isPassport.value) {
          const config = getDocumentTypeConfig(newType);
          if (config.backImageField) {
            const backImageData =
              proofOfIdentificationData.value[config.backImageField];
            if (isDocumentObject(backImageData)) {
              previewBack.value = documentObjectToDataUrl(backImageData);
            } else {
              previewBack.value = `data:image/jpeg;base64,${imageBack}`;
            }
          }
        }

        // Update file names based on the new document type
        const config = getDocumentTypeConfig(newType);
        if (config.frontImageField) {
          const fileNameField =
            `${config.frontImageField}FileName` as keyof ProofOfIdentificationFormModel;
          (formModel as any)[fileNameField] =
            proofOfIdentificationData.value[fileNameField] || '';
        }
        if (config.backImageField) {
          const fileNameField =
            `${config.backImageField}FileName` as keyof ProofOfIdentificationFormModel;
          (formModel as any)[fileNameField] =
            proofOfIdentificationData.value[fileNameField] || '';
        }
      }
    }
  }
);

watch(
  () => proofOfIdentificationData.value,
  (val) => {
    if (val) {
      mapProofToForm(val);
    }
  },
  { immediate: true }
);
</script>
