<template>
  <!-- Loading State -->
  <div
    v-if="formState.isLoading"
    class="flex justify-center items-center py-20"
  >
    <div class="flex flex-col items-center gap-4">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"
      ></div>
      <span class="text-neutral-5">Loading address information...</span>
    </div>
  </div>

  <!-- Error State -->
  <div v-else-if="formState.hasError" class="flex flex-col items-center py-20">
    <div class="text-center max-w-md">
      <div class="mb-6">
        <img
          :src="`/images/${formState.errorConfig.image}`"
          :alt="formState.errorConfig.alt"
          class="w-32 h-32 mx-auto mb-4"
        />
      </div>
      <h3 class="text-lg font-semibold text-neutral-2 mb-2">
        {{ formState.errorConfig.title }}
      </h3>
      <p class="text-neutral-5 mb-6">
        {{ formState.errorConfig.message }}
      </p>
      <button
        @click="retryLoadData"
        class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-shade1 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>

  <!-- Form Content (shown when not loading and no error, even if data is empty) -->
  <div v-else-if="formState.showForm">
    <div class="flex justify-between items-center">
      <div class="flex flex-col">
        <div class="flex">
          <DaryaOutlineArrowLeftLongIcon
            class="text-neutral-6 cursor-pointer"
            @click="handleGoBack"
          />
          <span class="text-base font-bold ps-2">
            {{ t('kyc.proof_of_address') }}
          </span>
        </div>

        <span class="pt-3 text-neutral-5 text-sm">
          {{
            isFormEditable
              ? 'Please enter your Address information for review.'
              : 'Your Address information for review.'
          }}
        </span>
      </div>

      <div
        class="flex items-center"
        v-if="proofOfAddressData?.verificationStatusTypeId"
      >
        <span class="pe-3 font-bold">{{ t('kyc.verification_status') }}</span>
        <DChip variant="outline" :color="chipColor" :label="chipLabel" />
        <span
          v-if="proofOfAddressData?.verificationStatusTypeId === 5"
          class="font-bold text-[13px] text-primary ps-1 cursor-pointer"
          @click="isOpenRejectionReasonDialog = true"
        >
          See why?
        </span>
      </div>
    </div>

    <VeeForm v-slot="{ meta, handleSubmit }">
      <form autocomplete="off" @submit="handleSubmit($event, onSubmit)">
        <div
          class="grid grid-cols-1 md:grid-cols-[1fr_1.4fr] gap-12 mt-11 w-10/12"
        >
          <div
            class="flex flex-col gap-y-5 py-4 px-6 border border-neutral-13 rounded-2xl"
          >
            <VeeField
              v-slot="{ field, errorMessage }"
              name="residenceCountryId"
            >
              <DSelectBox
                v-bind="field"
                v-model="formModel.residenceCountryId"
                label="Country of Residence"
                :options="countryListOptions"
                placeholder="Country of Residence"
                required
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>

            <VeeField
              v-slot="{ field, errorMessage }"
              name="city"
              v-model="formModel.city"
            >
              <DInput
                v-bind="field"
                type="text"
                label="City"
                placeholder="City"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="postalCode"
              v-model="formModel.postalCode"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Postal Code"
                placeholder="Postal Code"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="address"
              v-model="formModel.address"
            >
              <DInput
                v-bind="field"
                type="text"
                label="Address"
                placeholder="Address"
                :error="errorMessage"
                :disabled="!isFormEditable"
              />
            </VeeField>
          </div>
          <div class="border border-neutral-13 rounded-2xl px-6 py-4">
            <div
              class="flex flex-col items-center border border-dashed border-primary-tint6 rounded-2xl"
              :class="[previewSrc ? 'py-0 px-0' : 'py-8 px-16']"
            >
              <template v-if="previewSrc">
                <div class="relative w-full h-80 rounded-2xl overflow-hidden">
                  <img
                    :src="previewSrc"
                    alt="Preview"
                    class="object-cover w-full h-full"
                  />
                  <button
                    v-if="isFormEditable"
                    class="flex px-2 py-1 w-24 absolute bottom-2 right-2 bg-neutral/50 text-error-tint1 font-bold rounded-lg p-1 shadow cursor-pointer"
                    @click="removePreviewImage"
                  >
                    <DaryaOutlineTrashIcon />
                    <span class="ps-1">{{ t('common.delete') }}</span>
                  </button>
                </div>
              </template>
              <template v-else>
                <img
                  src="@/assets/images/upload-document.svg"
                  alt="Upload passport"
                  class="w-28 h-28 object-cover"
                />
                <span class="text-center pt-6 text-sm font-bold">
                  {{ isFormEditable ? t('kyc.upload_document') : 'Document' }}
                </span>
                <span class="text-center pt-2 pb-12 text-neutral-9 text-xs">
                  {{
                    isFormEditable
                      ? t('kyc.upload_document_description_of_address')
                      : 'Uploaded document for address verification'
                  }}
                </span>
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/png, image/jpeg"
                  class="hidden"
                  @change="onFileChanged"
                />
                <DButton
                  v-if="isFormEditable"
                  label="Upload Image"
                  variant="fill"
                  class="w-80"
                  @click="triggerFileInput"
                />
              </template>
            </div>
          </div>
        </div>

        <div class="flex w-10/12 justify-end mt-6">
          <DButton
            label="Save"
            class="w-36"
            size="large"
            :disabled="!isFormEditable"
            type="submit"
          />
        </div>
      </form>
    </VeeForm>
  </div>

  <ConfirmDocumentDialog
    :isOpenConfirmDialog="isOpenConfirmDialog"
    @update:isOpenConfirmDialog="isOpenConfirmDialog = $event"
    @close="handleCloseConfirmDialog"
    @submitForm="submitForm"
  />

  <SuccessSubmitDialog
    :isOpenSuccessSubmitDialog="isOpenSuccessSubmitDialog"
    @update:isOpenSuccessSubmitDialog="isOpenSuccessSubmitDialog = $event"
    @close="handleCloseSuccessDialog"
  />
  <RejectionReasonDialog
    :isOpenRejectionReasonDialog="isOpenRejectionReasonDialog"
    :message="proofOfAddressData?.message"
    @update:isOpenRejectionReasonDialog="isOpenRejectionReasonDialog = $event"
    @close="isOpenRejectionReasonDialog = false"
  />
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useGetAllCountry } from '@/modules/user';
import { useI18n } from 'vue-i18n';

import {
  useGeProofAddressById,
  useGetVerificationStatusTypeEnum,
  useCreateProofOfAddressMutation,
  ConfirmDocumentDialog,
  SuccessSubmitDialog,
  RejectionReasonDialog,
  type ProofOfAddressCreationFormModel,
  getStatusColor,
  getStatusText,
  processUploadedFile,
  getDocumentPreviewUrl,
  createDocumentObject,
  isDocumentObject,
} from '@/modules/kyc';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';

const isOpenConfirmDialog = ref(false);
const isOpenSuccessSubmitDialog = ref(false);
const isOpenRejectionReasonDialog = ref(false);

const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const {
  proofOfAddressData,
  getProofOfAddressData,
  isError,
  isFetching,
  status,
  error,
} = useGeProofAddressById(route.query.actionId as string, true);

// Custom state management for form - show form even when data is empty
const formState = computed(() => {
  return {
    isLoading: isFetching.value && status.value === 'pending',
    hasError: isError.value,
    showForm: !isFetching.value && !isError.value, // Show form when not loading and no error
    errorConfig: {
      title: 'Unable to Load Address Information',
      message:
        'We encountered an issue loading your address information. Please try again.',
      image: 'error-state.svg',
      alt: 'Error loading address information',
    },
  };
});

// Retry function
const retryLoadData = () => {
  getProofOfAddressData();
};
const { verificationStatusTypeEnum } = useGetVerificationStatusTypeEnum();
const fileInput = ref<HTMLInputElement | null>(null);
const previewSrc = ref<string | null>(null);
const formModel = reactive<ProofOfAddressCreationFormModel>({
  residenceCountryId: null,
  city: '',
  postalCode: '',
  address: '',
  document: '',
  documentFileName: '',
});

const { countryList } = useGetAllCountry();
const { createProofOfAddress } = useCreateProofOfAddressMutation({
  onSuccess: () => {
    isOpenSuccessSubmitDialog.value = true;
  },
  onError: (error) => {
    console.log('Error update document:', error);
  },
});

const countryListOptions = computed(() => {
  return (
    countryList.value?.map((x) => ({ label: x.fullName, value: x.id })) ?? []
  );
});

const handleGoBack = () => {
  router.back();
};

const onSubmit = () => {
  isOpenConfirmDialog.value = true;
};

const onFileChanged = async (e: Event) => {
  const file = (e.target as HTMLInputElement).files?.[0];
  if (!file) return;

  try {
    const uploadInfo = await processUploadedFile(file);

    if (previewSrc.value) {
      URL.revokeObjectURL(previewSrc.value);
    }

    previewSrc.value = uploadInfo.preview;

    const documentObject = createDocumentObject(uploadInfo.base64, file.name);
    formModel.document = documentObject.fileData; // For backward compatibility, store as string
    formModel.documentFileName = file.name;
  } catch (error) {
    console.error('File upload error:', error);
    alert(error instanceof Error ? error.message : 'Failed to upload file');
  }
};

const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

const removePreviewImage = () => {
  if (previewSrc.value) {
    if (previewSrc.value.startsWith('blob:')) {
      URL.revokeObjectURL(previewSrc.value);
    }
    previewSrc.value = null;
    formModel.document = '';
    formModel.documentFileName = '';
    if (fileInput.value) fileInput.value.value = '';
  }
};

const chipColor = computed(() => {
  const status = proofOfAddressData.value?.verificationStatusTypeId;
  return status ? getStatusColor(status) : 'neutral';
});

const chipLabel = computed(() => {
  const status = proofOfAddressData.value?.verificationStatusTypeId;
  return status ? getStatusText(verificationStatusTypeEnum.value, status) : '';
});

const isFormEditable = computed(() => {
  const status = proofOfAddressData.value?.verificationStatusTypeId;
  return status === 2 || status === 5;
});

const mapProofToForm = (data: any) => {
  if (!data) return;

  formModel.residenceCountryId = data.residenceCountryId;
  formModel.city = data.city || '';
  formModel.postalCode = data.postalCode || '';
  formModel.address = data.address || '';
  formModel.verificationStatusTypeId = data.verificationStatusTypeId || 1;

  // Handle document field - could be string or object
  if (data.document) {
    if (isDocumentObject(data.document)) {
      // New format: document object
      formModel.document = data.document.fileData;
      formModel.documentFileName = data.document.fileName;

      // Set preview from document object
      const previewUrl = getDocumentPreviewUrl(data.document);
      if (previewUrl) {
        previewSrc.value = previewUrl;
      }
    } else if (typeof data.document === 'string') {
      // Legacy format: string
      formModel.document = data.document;
      formModel.documentFileName = data.documentFileName || '';

      // Set preview from base64 string
      const previewUrl = getDocumentPreviewUrl(data.document);
      if (previewUrl) {
        previewSrc.value = previewUrl;
      }
    }
  } else {
    formModel.document = '';
    formModel.documentFileName = '';
    previewSrc.value = null;
  }
};

const handleCloseConfirmDialog = () => {
  isOpenConfirmDialog.value = false;
};
const handleCloseSuccessDialog = () => {
  isOpenSuccessSubmitDialog.value = false;
  getProofOfAddressData();
};
const submitForm = () => {
  const submissionData = { ...formModel };

  createProofOfAddress(submissionData);
  handleCloseConfirmDialog();
};

watch(
  () => proofOfAddressData.value,
  (val) => {
    mapProofToForm(val);
  },
  { immediate: true }
);
</script>
