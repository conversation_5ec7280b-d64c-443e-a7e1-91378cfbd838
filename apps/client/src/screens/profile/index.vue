<!-- MainComponent.vue -->
<template>
  <DTab :tabs="tabs" v-model="activeTab" class="mt-8">
    <template #tab1>
      <UserProfileTab />
    </template>
    <template #tab2>
      <VerificationTab />
    </template>
  </DTab>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { UserProfileTab } from '@/modules/user';
import { VerificationTab } from '@/modules/kyc';

const activeTab = ref('tab1');
const tabs = [
  { name: 'tab1', label: 'Profile' },
  { name: 'tab2', label: 'Verification' },
];
</script>
