<template>
  <div class="flex flex-col items-center w-full pt-14 overflow-y-auto h-screen">
    <img src="@/assets/images/logo.svg" alt="Logo" class="w-24 h-24" />
    <span class="text-3xl font-semibold pt-5">{{ t('login.welcome') }}</span>

    <DTab :tabs="tabs" v-model="activeTab" class="py-8 w-96">
      <template #tab1>
        <VeeForm
          v-slot="{ handleSubmit }"
          :initial-values="loginFormModel"
          class="mt-8"
        >
          <form
            autocomplete="off"
            @submit="handleSubmit($event, onSubmit)"
            class="space-y-6"
          >
            <VeeField
              v-slot="{ field, errorMessage }"
              name="username"
              type="email"
              v-model="loginFormModel.username"
            >
              <DInput v-bind="field" placeholder="Enter Your Email Address" />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="password"
              type="password"
              v-model="loginFormModel.password"
            >
              <DInput
                v-bind="field"
                placeholder="Enter Password"
                postfixIcon="DaryaOutlineEyeSlashIcon"
              />
            </VeeField>
            <router-link
              v-slot="{ navigate }"
              to="/forget-password"
              custom
              class="cursor-pointer"
            >
              <span
                @click="navigate"
                class="flex justify-center text-xs font-bold text-primary-tint3"
              >
                {{ t('login.forgot_password_with_question_mark') }}
              </span>
            </router-link>

            <button
              type="submit"
              class="flex w-full justify-center items-center gap-2 text-white text-xs font-bold px-6 py-4 rounded-lg bg-primary cursor-pointer"
            >
              {{ t('common.continue') }}
            </button>
          </form>
        </VeeForm>
      </template>

      <template #tab2>
        <VeeForm
          v-slot="{ handleSubmit }"
          :initial-values="createFormModel"
          class="mt-8"
        >
          <form
            autocomplete="off"
            @submit="handleSubmit($event, onSubmitCreateUser)"
            class="space-y-6"
          >
            <VeeField
              v-slot="{ field, errorMessage }"
              name="username"
              type="email"
              v-model="createFormModel.email"
            >
              <DInput v-bind="field" placeholder="Enter Your Email Address" />
            </VeeField>
            <VeeField
              v-slot="{ field, errorMessage }"
              name="password"
              type="password"
              v-model="createFormModel.password"
            >
              <DInput
                v-bind="field"
                placeholder="Enter Password"
                postfixIcon="DaryaOutlineEyeSlashIcon"
              />
            </VeeField>
            <div class="flex flex-col text-neutral-500 gap-y-2">
              <span class="inline-flex items-center">
                <DaryaOutlineInfoCircleIcon size="16" />
                <span class="ps-1 text-[10px]">
                  {{ t('login.validate_rule_text.first') }}
                </span>
              </span>
              <span class="inline-flex items-center">
                <DaryaOutlineInfoCircleIcon size="16" />
                <span class="ps-1 text-[10px]">
                  {{ t('login.validate_rule_text.second') }}
                </span>
              </span>
              <span class="inline-flex items-center">
                <DaryaOutlineInfoCircleIcon size="16" />
                <span class="ps-1 text-[10px]">
                  {{ t('login.validate_rule_text.third') }}
                </span>
              </span>
            </div>
            <!--            <VeeField v-slot="{ field, errorMessage }" name="agreement">-->
            <!--              <DCheckbox-->
            <!--                v-bind="field"-->
            <!--                v-model="createFormModel.agreement"-->
            <!--                :label="'I confirm the above banking information is accurate'"-->
            <!--                :error="errorMessage"-->
            <!--                :value="true"-->
            <!--                labelFontSize="10px"-->
            <!--              />-->
            <!--            </VeeField>-->
            <button
              type="submit"
              class="flex w-full justify-center items-center gap-2 text-white text-xs font-bold px-6 py-4 rounded-lg bg-primary cursor-pointer"
            >
              {{ t('common.continue') }}
            </button>
          </form>
        </VeeForm>
      </template>
    </DTab>

    <div class="self-stretch inline-flex justify-center items-center gap-1">
      <div
        class="w-28 h-0 outline outline-[0.50px] outline-offset-[-0.25px] outline-zinc-400"
      />

      <div
        class="justify-start text-neutral-800 text-xs font-normal font-['Poppins'] leading-none"
      >
        {{ t('login.create_account') }}
      </div>

      <div
        class="w-28 h-0 outline outline-[0.50px] outline-offset-[-0.25px] outline-zinc-400"
      />
    </div>

    <div class="flex flex-col justify-center w-96 mt-4 gap-y-3">
      <button
        class="flex justify-center items-center py-2.5 border border-neutral-12 rounded-lg cursor-pointer"
      >
        <DaryaLogoGoogleIcon />
        <span class="text-neutral-700 text-sm font-normal ps-3">
          {{ t('login.continue_with_google') }}
        </span>
      </button>
      <button
        class="flex justify-center items-center py-2.5 border border-neutral-12 rounded-lg cursor-pointer"
      >
        <DaryaLogoFacebookIcon />
        <span class="text-neutral-700 text-sm font-normal ps-3">
          {{ t('login.continue_with_facebook') }}
        </span>
      </button>
      <button
        class="flex justify-center items-center py-2.5 border border-neutral-12 rounded-lg cursor-pointer"
      >
        <DaryaLogoSocialXIcon />
        <span class="text-neutral-700 text-sm font-normal ps-3">
          {{ t('login.continue_with_x') }}
        </span>
      </button>
      <button
        class="flex justify-center items-center py-2.5 border border-neutral-12 rounded-lg cursor-pointer"
      >
        <DaryaLogoAppleIcon />
        <span class="text-neutral-700 text-sm font-normal ps-3">
          {{ t('login.continue_with_apple') }}
        </span>
      </button>
    </div>
  </div>

  <OtpDialog
    :isOpenOtpDialog="isOpenOtpDialog"
    @update:isOpenOtpDialog="isOpenOtpDialog = $event"
    @close="isOpenOtpDialog = false"
    @verify-code="handleVerifyOtp"
    :email="loginFormModel.username"
    :has-error="hasOtpError"
  />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import {
  OtpDialog,
  useUserLoginMutation,
  useCreateUserMutation,
  useValidateOtp,
} from '@/modules/user/index';

const router = useRouter();
const { t } = useI18n();

const userId = ref('');
const isOpenOtpDialog = ref(false);
const hasOtpError = ref(false);
const activeTab = ref('tab1');
const tabs = [
  { name: 'tab1', label: 'Sign In' },
  { name: 'tab2', label: 'Create an Account' },
];

const loginFormModel = reactive({
  username: '',
  password: '',
});

const createFormModel = reactive({
  email: '',
  password: '',
  // agreement: false,
});

const handleSuccess = ({ data }) => {
  userId.value = data?.data;
  isOpenOtpDialog.value = true;
};

const handleSuccessCreateUser = () => {
  activeTab.value = 'tab2';
};

const handleVerifySuccess = () => {
  router.push('./');
};

const handleVerifyError = () => {
  hasOtpError.value = true;
};

const { loginUser } = useUserLoginMutation({
  onSuccess: handleSuccess,
});

const { verifyOtp } = useValidateOtp({
  onSuccess: handleVerifySuccess,
  onError: handleVerifyError,
});

const { createUser } = useCreateUserMutation({
  onSuccess: handleSuccessCreateUser,
  onError: (error) => {
    console.error('Error creating user:', error);
  },
});

const onSubmit = () => {
  loginUser(loginFormModel);
};

const onSubmitCreateUser = () => {
  createUser(createFormModel);
};

const handleVerifyOtp = (otp) => {
  verifyOtp({ userId: userId.value, otp: otp });
};
</script>

<route lang="yaml">
meta:
  auth: false
  layout: false
</route>
