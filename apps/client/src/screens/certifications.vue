<template>
  <div>
    <span class="font-bold text-xl">{{ t('common.certificates') }}</span>
  </div>
  <DataStateWrapper
    :isLoading="dataState.isLoading.value"
    :hasError="dataState.hasError.value"
    :hasNoData="dataState.hasNoData.value"
    :isRefetching="dataState.isRefetching.value"
    :errorConfig="dataState.errorConfig.value"
    :emptyConfig="dataState.emptyConfig.value"
  >
    <div class="flex gap-6">
      <div
        v-for="item in certificateData"
        :key="item.userId"
        class="flex flex-col mt-8 px-6 py-4 rounded-3xl border border-neutral-12 w-[492px]"
      >
        <div class="relative flex justify-center rounded-lg mb-4 w-full">
          <img
            src="@/assets/images/hands-holding.svg"
            :class="!item.isDownloadable ? 'brightness-50' : 'cursor-pointer'"
            @click="
              item.isDownloadable &&
              handleDownloadCertificate(item.certificateId)
            "
          />
          <DaryaOutlineLockIcon
            v-if="!item.isDownloadable"
            class="absolute text-white top-1/2 z-40"
          />
        </div>

        <span class="text-neutral-8 font-bold text-sm pb-2">
          {{ t('common.certificates') }}
        </span>
        <span class="font-bold text-xl">{{ item.certificateType }}</span>
        <span class="text-neutral-6 text-base">
          {{ item.title }}
        </span>
      </div>
    </div>
  </DataStateWrapper>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { download } from '@libs/utils';
import DataStateWrapper from '@/components/DataStateWrapper.vue';
import { useDataState } from '@/composables/useDataState';
import {
  useGeAllCertificateByUserId,
  useGetCertificateFileById,
} from '@/modules/certificate';
import { useAuth } from '@/modules/user';

const { user } = useAuth();

const { t } = useI18n();

const certificateId = ref<string>('');

const userId = computed(() => user.value?.nameid ?? '');

const { certificateData, status, isError, isFetching, error } =
  useGeAllCertificateByUserId(userId, true);

// Data state management - single composable handles everything
const dataState = useDataState(
  status,
  certificateData,
  isFetching,
  isError,
  error,
  'no-data' // Empty state type
);
const { getCertificateFileById, testData } = useGetCertificateFileById(
  userId,
  certificateId,
  false
);

const handleDownloadCertificate = (id: string) => {
  certificateId.value = id;
  getCertificateFileById();
};

watch(testData, (data) => {
  if (data && data?.fileName) {
    const file = `data:${data.fileData};base64,${data.fileExtension}`;

    download(data.fileName, file);
  }
});
</script>
