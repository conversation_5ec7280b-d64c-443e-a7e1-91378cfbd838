account:
  account_id: Account ID
  all_status: All status
  all_instrument: All Instrument
  all_side: All Side
  trading_objective: Trading Objective
  trading_log: Trading log
  refresh_in:  Refreshing in
  numeric_five_minute: '05 : 00'
  master_pass: Master Pass
  manage_accounts: Manage Accounts
  trading_days: Trading Days
  daily_reports: Daily Reports
  trading_history: Trading history
  growth_analysis: Growth Analysis
  total_return: Total Return
  top_symbol: Top Symbol
  profit_loss_overview: Profit and Loss Overview
  instrument_performance: Instrument Performance
  limit_orders: Limit Orders
  login_id: Login ID
  win_ratio: Win Ratio
  win_loss_ratio: Win/Loss Ratio
challenge:
  challenges: Challenges
  first_reward: First Reward
  discount: Discount
  get_plan:  Get Plan
  plan: Plan
  platform: Platform
  processing_time: Processing time
  profit_split_up_to: Profit Split Up To
  phase_one: Phase 1 profit target
  phase_two: Phase 2 profit target
  maximum_overall_loss: Maximum Overall Loss
  maximum_daily_loss: Maximum Daily Loss
  minimum_trading_days: Minimum Trading Days
  news_trading: News Trading
  total_amount: Total amount
common:
  all_days: All days
  apply: Apply
  accounts: Accounts
  account_type: Account Type
  balance: Balance
  cancel: Cancel
  created: Created
  executed: Executed
  certificates: Certificates
  continue: Continue
  custom: Custom
  delete: Delete
  days: Days
  details: Details
  done: Done
  equity: Equity
  end_date: End date
  fee: Fee
  today: Today
  server: Server
  server_type: Server Type
  start_date: Start date
  plan_type: Plan type
  pending: Pending
  ok: Ok
  price: Price
  start_challenge: Start challenge
  support: Support
  profit_loss: Profit/Loss
  initial_balance: Initial Balance
  instrument: Instrument
  order_type:  Order Type
  last_seven_days:  Last 7 Days
  login: Login
  limits: Limits
  hours: Hours
  reset_all: Reset All
  rejected: Rejected
  yesterday: Yesterday
  view: View
  validations:
      required: This field is required.
  weekday: Weekday
calculator:
  tabs:
    currencyConverter: Currency Converter
    pipValue: Pip Value
    margin: Margin
    positionSize: Position Size
    profitLoss: Profit/Loss
    swap: Swap
    currencyTable: Currency Table
  # Currency Converter
  currencyConverter:
    title: Currency Converter
    description: Convert between different currencies using live exchange rates
    amount: Amount
    from: From
    to: To
    result: Converted Amount
    convert: Convert
    swap: Swap
  # Pip Value Calculator
  pipValue:
    title: Pip Value Calculator
    description: Calculate pip value for different lot sizes and currency pairs
    currencyPair: Currency Pair
    accountCurrency: Account Currency
    lotSize: Lot Size
    micro: Micro
    mini: Mini
    standard: Standard
    calculate: Calculate
    pipMovement: Movement (pips)
    pipValue: Pip Value
    value: Value
  # Margin Calculator
  margin:
    title: Margin Calculator
    description: Calculate margin requirements for forex positions
    currencyPair: Currency Pair
    accountCurrency: Account Currency
    lotSize: Lot Size
    price: Price (optional)
    leverage: Leverage
    result: Margin Required
    positionSize: Position Size
    positionValue: Position Value
    leverageUsed: Leverage Used
    calculate: Calculate
  # Position Size Calculator
  positionSize:
    title: Position Size Calculator
    description: Calculate the optimal position size based on your account balance, risk percentage, and stop loss
    accountBalance: Account Balance
    accountCurrency: Account Currency
    riskPercentage: Risk Percentage (%)
    stopLossPips: Stop Loss (pips)
    currencyPair: Currency Pair
    entryPrice: Entry Price (optional)
    calculate: Calculate Position Size
    amountAtRisk: Amount at Risk
    recommendedPosition: Recommended Position Size
    standardLots: Standard Lots
    miniLots: Mini Lots
    microLots: Micro Lots
  # Profit/Loss Calculator
  profitLoss:
    title: Profit/Loss Calculator
    description: Calculate your potential profit or loss from a forex trade
    tradeType: Trade Type
    buy: Buy (Long)
    sell: Sell (Short)
    currencyPair: Currency Pair
    accountCurrency: Account Currency
    lotSize: Lot Size
    entryPrice: Entry Price
    exitPrice: Exit Price
    calculate: Calculate Profit/Loss
    result: Profit/Loss
    tradeDetails: Trade Details
    direction: Direction
    pipMovement: Pip Movement
    pipValue: Pip Value
    roi: Return on Investment
  # Swap Calculator
  swap:
    title: Swap Calculator
    description: Calculate the swap fee (or profit) for holding overnight positions
    tradeType: Trade Type
    buy: Buy (Long)
    sell: Sell (Short)
    currencyPair: Currency Pair
    accountCurrency: Account Currency
    lotSize: Lot Size
    swapLongRate: Swap Long Rate (pips)
    swapShortRate: Swap Short Rate (pips)
    days: Number of Days
    calculate: Calculate Swap
    result: Swap Fee/Profit
    calculationDetails: Calculation Details
    position: Position
    swapRate: Swap Rate
    dailySwap: Daily Swap
    tripleSwapDay: Triple Swap Day
    tripleSwapNote: Wednesday (3x regular swap)
    presets:
      oneDay: 1 Day
      workWeek: Work Week
      oneWeek: 1 Week
      oneMonth: 1 Month
  # Currency Table
  currencyTable:
    title: Currency Table
    amount: Amount
    baseCurrency: Base Currency
    exchangeRates: Exchange Rates
    lastUpdated: Last updated
    currency: Currency
    code: Code
    rate: Rate
    convertedAmount: Converted Amount
  # Common
  common:
    loading: Loading...
    error: Error
    required: Required
    optional: Optional
    profit: Profit
    loss: Loss
    up: up
    down: down
    long: long
    short: short
    pips: pips
    perDay: per day
    perPip: per pip
    lots: lots
  validation:
    validLotSize: Please enter a valid lot size.
    validAmount: Please enter a valid positive amount.
    validCurrency: Please select a currency.
    validAccountCurrency: Please select an account currency.
    validLeverage: Please enter a valid leverage value.
    validEntryPrice: Entry price must be a valid number.
    validExitPrice: Exit price must be a valid number.
    validAccountBalance: Please enter a valid balance.
    validRiskPercentage: Please enter a valid risk percentage.
    validStopLossPips: Stop loss must be a positive number of pips greater than zero.
    validDays: Number of days must be a positive value.
    validSwapLong: Please enter a valid long swap value.
    validSwapShort: Please enter a valid long swap value.

sidebar:
  dashboard: Dashboard
  economic_calendar: Economic calendar
  utilities: Utilities
  withdraw: Withdraw
  setting: Setting
  account_overview: Account Overview
  trading_overview: Trading Overview
  transactions: Transactions
  withdrawal: Withdrawal
  challenges: Challenges
  competition: Competition
  certifications: Certification
  tools: Tools
  calculator: Calculator
  faq: FAQ
trading:
  target_profit: Target Profit
  target_price: Target Price
  profit_aim: Profit Aim $
  stop_loss: Stop Loss
  stop_price: Stop Price
  risk: Risk
transaction:
  all_statuses: All Statuses
  all_transaction_types: All Transaction types
  transaction_history: Transaction History
  invoice_id: InvoiceID
login:
  welcome: Welcome to Etirda
  forgot_password: Forget password
  forgot_password_dialog_description: We’ve sent an email to {userName} with instructions to
    reset your password. Please check your inbox and follow the link
  forgot_password_with_question_mark: Forgot Password?
  create_account: Or create an account with
  continue_with_google: Continue with Google
  continue_with_facebook: Continue with Facebook
  continue_with_x: Continue with X
  continue_with_apple: Continue with Apple
  click_to_resend: Click to resend
  did_not_get_the_code: Didn't get the code?
  sign_in_now: Sign In Now
  password_reset_sent: Password Reset Sent
  verification_code: Verification Code
  validate_rule_text:
    first: At least one upper and one lower case letter
    second: Between 8-15 characters
    third: At least one number
  we_sent_code_to: We’ve sent a code to
  reset_password: Reset password
home:
  banner:
    opportunity_text: Share the opportunity, earn together
    rewarded_text:  Your friends get funded – you get rewarded. Win-win!
    education: Education
    learn_trading: Learn Trading
    essentials: Essentials
    build: Build
    expertise: Expertise
    from_scratch: from Scratch
  challenge_account: Challenge Account
  free_trial: Free trial
  view_dashboard: View Dashboard
withdraw:
  payment_methods: Payment methods
  processing_time: Processing time
  request_a_withdrawal: Request a Withdrawal
  make_sure_information: Make sure your banking information is correct
  submit_withdrawal: Submit Withdrawal
  withdraw_now: Withdraw Now
kyc:
  complete_kyc_verification: Complete KYC Verification
  to_make_withdrawals: To make withdrawals, your identity verification must be completed
  proof_of_identification: Proof of Identification
  proof_of_address: Proof of Address
  verification_status: 'Verification Status:'
  upload_document: Upload Document
  upload_document_description_of_address: 'Submit a document from the last 3 months showing your name and residential address. Accepted: utility bill, bank statement, government letter.'
profile:
  address: Address
  country: Country
  personal_information: Personal Information
  first_name: First Name
  date_of_birth: Date of Birth
  last_name: Last Name
  phone: Phone
  gender: Gender
  postal_code: Postal Code
  user_name: User Name
  email_address: Email Address

