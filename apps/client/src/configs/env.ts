const VERSION = import.meta.env.VITE_VERSION ?? 'N/A';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL ?? '';
const API_BASE_PATH = import.meta.env.VITE_API_BASE_PATH ?? 'api/v1';

const buildApiUrl = (resource: string): string => {
  const cleanResource = resource.replace(/^\/|\/$/g, '');
  return `${API_BASE_URL}/${cleanResource}/${API_BASE_PATH}`;
};

const USER_API = buildApiUrl('user');
const USER_MANAGE_API = buildApiUrl('usermanage');
const ACCOUNT_API = buildApiUrl('account');
const CERTIFICATE_API = buildApiUrl('certificate');
const KYC_API = buildApiUrl('kyc');
const PAYMENT_MANAGE_API = buildApiUrl('paymentmanage');
const PAYMENT_API = buildApiUrl('payment');
const PLAN_API = buildApiUrl('plan');

const NODE_ENV = import.meta.env.MODE ?? 'development';
const IS_DEV = NODE_ENV === 'development';
const IS_TEST_MODE = import.meta.env.VITEST || NODE_ENV === 'test';

export {
  VERSION,
  IS_DEV,
  IS_TEST_MODE,
  API_BASE_URL,
  USER_API,
  ACCOUNT_API,
  CERTIFICATE_API,
  KYC_API,
  PAYMENT_API,
  PAYMENT_MANAGE_API,
  PLAN_API,
  USER_MANAGE_API,
};
