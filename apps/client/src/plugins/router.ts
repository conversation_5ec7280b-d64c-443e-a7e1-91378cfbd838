import { App } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import generatedRoutes from 'virtual:generated-pages';
import { setupLayouts } from 'virtual:generated-layouts';

import { IS_TEST_MODE } from '../configs/env';

const routes = setupLayouts(generatedRoutes);
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Flush pending requests upon each route enter
router.beforeEach((to, from, next) => {
  next();
});

const install = (app: App) => {
  app.use(router);
};

export { router, install };
