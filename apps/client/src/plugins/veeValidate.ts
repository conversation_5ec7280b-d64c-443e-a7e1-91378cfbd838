import type { App } from 'vue';
import { Field, Form, configure, defineRule } from 'vee-validate';
import { isEmpty } from '@libs/utils';
import { i18n } from '@/plugins/i18n';

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const install = (Vue: App) => {
  configure({
    validateOnBlur: false,
    validateOnChange: false,
    validateOnModelUpdate: true,
    validateOnInput: false,
  });

  defineRule('required', (value) => {
    if (isEmpty(value)) {
      return i18n.global.t('common.validations.required');
    }

    return true;
  });

  defineRule('email_required', (value) => {
    if (isEmpty(value)) {
      return i18n.global.t('common.validations.email_required_message');
    }

    return emailRegex.test(value)
      ? true
      : i18n.global.t('common.validations.invalid_email_message');
  });

  defineRule('date', (value) => {
    if (isEmpty(value)) {
      return true; // Let required rule handle empty values
    }

    // Convert to string if it's not already
    const dateString = typeof value === 'string' ? value : String(value);

    // Check if it's a valid date string in YYYY-MM-DD format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }

    // Check if it's a valid date
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Please enter a valid date';
    }

    // Check if the date is not in the future (for birth date)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date > today) {
      return 'Date cannot be in the future';
    }

    return true;
  });

  Vue.component('VeeField', Field);
  Vue.component('VeeForm', Form);
};

export { install };
