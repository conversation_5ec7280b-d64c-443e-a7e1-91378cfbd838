<template>
  <div
    class="flex flex-col items-center justify-center min-h-[200px] text-center"
  >
    <Image
      :src="computedImageSrc"
      :alt="alt || computedAlt"
      :width="imageWidth"
      :height="imageHeight"
      class="mb-4 max-w-full h-auto"
    />

    <div class="empty-state__message">
      <h3 v-if="computedTitle" class="text-lg font-medium mb-2">
        {{ computedTitle }}
      </h3>
      <p class="text-sm text-gray-500">{{ computedMessage }}</p>
    </div>

    <div v-if="$slots.actions" class="mt-4">
      <slot name="actions" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Image from '@/components/Image.vue';
import {
  getEmptyStateConfig,
  type EmptyStateType,
} from '@/composables/emptyStateConfig';

interface EmptyStateProps {
  type?: EmptyStateType;
  image?: string;
  title?: string;
  message?: string;
  alt?: string;
  imageWidth?: string;
  imageHeight?: string;
}

const props = withDefaults(defineProps<EmptyStateProps>(), {
  type: 'no-data',
  imageWidth: '200',
  imageHeight: '200',
});

// Get configuration from shared config
const config = computed(() => getEmptyStateConfig(props.type));

const computedImageSrc = computed(() => {
  return props.image || config.value.image;
});

const computedTitle = computed(() => {
  return props.title !== undefined ? props.title : config.value.title;
});

const computedMessage = computed(() => {
  return props.message !== undefined ? props.message : config.value.message;
});

const computedAlt = computed(() => {
  return props.alt || config.value.alt;
});
</script>
