<template>
  <div>
    <div
      v-if="isLoading"
      class="flex justify-center items-center min-h-[200px]"
    >
      <DLoading size="60" />
    </div>

    <!-- Error State -->
    <EmptyState
      v-else-if="hasError"
      :type="errorConfig.type"
      :title="errorConfig.title"
      :message="errorConfig.message"
      :image="errorConfig.image"
      :imageWidth="errorConfig.imageWidth"
      :imageHeight="errorConfig.imageHeight"
    >
      <template #actions>
        <slot name="error-actions">
          <button
            v-if="onRetry"
            @click="onRetry"
            class="bg-primary px-6 py-3 rounded-lg cursor-pointer hover:bg-primary-shade1 transition-colors"
          >
            <span class="text-white font-bold">{{ retryText }}</span>
          </button>
        </slot>
      </template>
    </EmptyState>

    <!-- Empty State -->
    <EmptyState
      v-else-if="hasNoData"
      :type="emptyConfig.type"
      :title="emptyConfig.title"
      :message="emptyConfig.message"
      :image="emptyConfig.image"
      :imageWidth="emptyConfig.imageWidth"
      :imageHeight="emptyConfig.imageHeight"
    >
      <template #actions>
        <slot name="empty-actions" />
      </template>
    </EmptyState>

    <!-- Refetching Indicator (subtle loading for updates) -->
    <div v-else-if="showRefetchingIndicator && isRefetching" class="relative">
      <div class="absolute top-0 right-0 z-10">
        <div class="bg-primary text-white px-3 py-1 rounded-full text-sm">
          Updating...
        </div>
      </div>
      <slot />
    </div>

    <!-- Content -->
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
import EmptyState from '@/components/EmptyState.vue';
import type { StateConfig } from '@/composables/useDataState';
interface DataStateWrapperProps {
  isLoading: boolean;
  hasError: boolean;
  hasNoData: boolean;
  isRefetching?: boolean;
  showRefetchingIndicator?: boolean;
  errorConfig: StateConfig;
  emptyConfig: StateConfig;
  onRetry?: () => void;
  retryText?: string;
}

withDefaults(defineProps<DataStateWrapperProps>(), {
  isRefetching: false,
  showRefetchingIndicator: true,
  retryText: 'Try Again',
});
</script>
