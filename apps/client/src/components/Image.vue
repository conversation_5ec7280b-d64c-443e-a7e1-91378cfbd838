<template>
  <img
    :src="imageSrc"
    :alt="alt"
    :width="width"
    :height="height"
    :class="imageClasses"
    :style="imageStyles"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface ImageProps {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  class?: string;
  loading?: 'lazy' | 'eager';
}

const props = withDefaults(defineProps<ImageProps>(), {
  loading: 'lazy',
});

const imageSrc = computed(() => {
  // If src already contains a protocol (http/https) or starts with /, use it as is
  if (
    props.src.startsWith('http') ||
    props.src.startsWith('/') ||
    props.src.startsWith('data:')
  ) {
    return props.src;
  }

  try {
    return new URL(`../assets/images/${props.src}`, import.meta.url).href;
  } catch (err) {
    console.warn(`Failed to load image: ${props.src}`, err);
    return '';
  }
});

const imageClasses = computed(() => {
  return props.class || '';
});

const imageStyles = computed(() => {
  const styles: Record<string, string> = {};

  if (props.width) {
    styles.width =
      typeof props.width === 'number' ? `${props.width}px` : props.width;
  }

  if (props.height) {
    styles.height =
      typeof props.height === 'number' ? `${props.height}px` : props.height;
  }

  return styles;
});
</script>
