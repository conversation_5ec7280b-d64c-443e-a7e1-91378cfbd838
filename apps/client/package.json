{"name": "@daryasolutions-front/client", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "serve": "vite preview"}, "dependencies": {"@libs/darya-design-system": "workspace:*", "@libs/darya-icon-package": "workspace:*", "@tanstack/vue-query": "^5.66.0", "axios": "^1.7.7", "url-template": "^3.1.1", "jwt-decode": "^4.0.0", "@libs/utils": "workspace:*", "@tailwindcss/postcss": "^4.1.6", "@vueuse/core": "^12.5.0", "camelcase-keys": "^9.1.3", "chart.js": "^4.4.9", "chartjs-plugin-annotation": "^3.1.0", "ts-node": "^10.9.2", "v-calendar": "^3.1.2", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-i18n": "^9.1.14", "vue-router": "^4.5.0", "vue-screen-utils": "1.0.0-beta.13"}, "devDependencies": {"@intlify/eslint-plugin-vue-i18n": "^2.0.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@intlify/vue-i18n-loader": "^1.1.0", "@tailwindcss/vite": "^4.0.5", "@types/jsdom": "^21.1.7", "@vitejs/plugin-vue": "^5.2.1", "@vitest/eslint-plugin": "1.1.25", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "jsdom": "^26.0.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^4.0.5", "typescript": "~5.7.3", "vite": "^5.4.12", "vite-plugin-pages": "^0.32.4", "vite-plugin-vue-devtools": "^7.7.0", "vite-plugin-vue-layouts": "^0.11.0", "vitest": "^3.0.2", "vue-tsc": "^2.2.0"}}