{"name": "daryasolutions-front", "version": "1.0.0", "scripts": {"dev:admin": "lerna run dev --scope=@daryasolutions-front/admin", "dev:client": "lerna run dev --scope=@daryasolutions-front/client", "build:changed": "lerna run build --since", "build:admin": "lerna run build --scope=@daryasolutions-front/admin", "build:client": "lerna run build --scope=@daryasolutions-front/client", "test:unit:changed": "lerna run test:unit:coverage --since", "test:unit:admin": "lerna run test:unit:coverage --scope=@daryasolutions-front/admin", "test:unit:client": "lerna run test:unit:coverage --scope=@daryasolutions-front/client"}, "devDependencies": {"nx": "20.4.2", "lerna": "^8.1.9", "prettier": "^3.4.2"}, "dependencies": {}}