services:
  admin:
    build:
      context: ./
      docerfile: apps/admin/Dockerfile
      ports:
        - "3000:3000"
#    environment:
#      - NODE_ENV=production
#      - PORT=3000

  client:
    build:
      context: ./
      docerfile: apps/client/Dockerfile
    ports:
      - "3000:3000"
#    environment:
#      - NODE_ENV=production
#      - PORT=3000

  backend:
    # TODO: Add backend configuration here

  database:
    # TODO: Add database configuration here

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  kafka:
    # TODO: Add kafka configuration here